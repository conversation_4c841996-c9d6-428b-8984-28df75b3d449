/*
 * @Author: xing.zhou
 * @Date: 2021-12-13 14:48:00
 * @LastEditTime: 2023-07-24 10:12:01
 * @LastEditors: chaohui.wu
 * @Description: 菜单管理 -- api
 */

import { axiosRequest } from '@common/utils/index'

export function getList() {
    return axiosRequest({
        url: '/menu/get/list',
        method: 'get'
    })
}
export function add(data: any) {
    return axiosRequest({
        url: '/menu/add',
        method: 'put',
        data
    })
}
export function update(data: any) {
    return axiosRequest({
        url: '/menu/update',
        method: 'post',
        data
    })
}
export function remove(data: any) {
    return axiosRequest({
        url: '/menu/remove',
        method: 'post',
        data
    })
}
export function getParam(data: any) {
    return axiosRequest({
        url: '/menu/param/get',
        method: 'get',
        params: data
    })
}

// 新增、编辑菜单元素 --> 后台接口不需要保存px_param的值
export function saveParam(data: any) {
    return axiosRequest({
        url: '/menu/param/save',
        method: 'post',
        data
    })
}
export function removeParam(data: any) {
    return axiosRequest({
        url: '/menu/param/remove',
        method: 'delete',
        params: data
    })
}

export function updateMenuSort(data: any) {
    return axiosRequest({
        url: '/menu/update/sort',
        method: 'post',
        data
    })
}
