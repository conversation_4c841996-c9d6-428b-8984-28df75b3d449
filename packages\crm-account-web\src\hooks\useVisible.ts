/*
 * @Description: 弹窗显示控制hooks
 * @Author: hongdong.xie
 * @Date: 2024-06-12 10:40:39
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2024-06-12 10:40:39
 * @FilePath: /crm-web/packages/crm-template/src/hooks/useVisible.ts
 */

export function useVisible({ props, emit }: any) {
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.modelValue
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: 关闭弹窗
     * @return {*}
     */
    const handleClose = () => {
        dialogVisible.value = false
    }

    return { dialogVisible, handleClose }
}
