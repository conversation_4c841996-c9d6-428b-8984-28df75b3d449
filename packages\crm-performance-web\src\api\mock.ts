/*
 * @Description: mock组件
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-05 15:26:49
 * @FilePath: /crm-template/src/api/mock.ts
 *
 */
/**
 * @param baseURL mock url 公共域名端口
 * @param mock baseURL 测试环境'https://easymock.k8s.howbuy.com/mock/63565391e07ca20022a812b1/cms/admin'
 * @param devHD baseURL 后端本地测试环境'http://**************:8080/grayscale/admin'
 * @param dev baseURL 新版测试环境'http://howbuy-cms.it40.k8s.howbuy.com/fpsop/admin'
 */

const isDev = process.env.NODE_ENV === 'development'

const originBaseUrl = {
    dev: 'http://crm.it34.k8s.howbuy.com/crm-asset',
    devHD: 'http://**************:8087/crm-asset',
    mock: 'https://mock.apifox.cn/m1/2175602-0-default'
}
/**
 * @param mockFlag [Boolean] 是否采用mock接口
 * @param baseURLFlag [Boolean] 是否采用当前baseUrl
 */
export const { mockFlag, baseURLFlag } = {
    mockFlag: false,
    baseURLFlag: false
}

export const baseURL = mockFlag ? originBaseUrl.mock : originBaseUrl.dev

export const paramsMerge = (val: any) => {
    return baseURLFlag ? Object.assign(val, { baseURL }) : val
}
