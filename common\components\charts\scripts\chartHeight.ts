/**
 * 动态设置echart图表的高度
 */

export default {
    data() {
        return {
            legendHeight: 0,
            dataZoomHeight: 0
        }
    },
    computed: {
        chartWidth({ $refs }) {
            return $refs?.ChartWrapper?.$el?.clientWidth || window.innerWidth
        },
        chartHeight({ height, legendHeight, dataZoomHeight }) {
            const ht = parseInt(height)
            return `${ht + legendHeight + dataZoomHeight}px`
        }
    }
}
