// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Autocomplete: typeof import('./../../common/components/moduleBase/Autocomplete.vue')['default']
    BarChart: typeof import('./../../common/components/charts/BarChart.vue')['default']
    BaseButton: typeof import('./../../common/components/moduleBase/BaseButton.vue')['default']
    BaseCard: typeof import('./../../common/components/moduleBase/BaseCard.vue')['default']
    BaseTable: typeof import('./../../common/components/module/BaseTable.vue')['default']
    BlankView: typeof import('./../../common/components/layout/blankView.vue')['default']
    ChartWrapper: typeof import('./../../common/components/charts/ChartWrapper.vue')['default']
    CircleDebule: typeof import('./../../common/components/charts/CircleDebule.vue')['default']
    CrmAutocomplete: typeof import('./../../common/components/moduleBase/CrmAutocomplete.vue')['default']
    CrmAutocompleteItem: typeof import('./../../common/components/moduleBase/CrmAutocompleteItem.vue')['default']
    CrmButton: typeof import('./../../common/components/moduleBase/CrmButton.vue')['default']
    CrmButtonDrop: typeof import('./../../common/components/moduleBase/CrmButtonDrop.vue')['default']
    CrmCheckbox: typeof import('./../../common/components/moduleBase/CrmCheckbox.vue')['default']
    CrmCheckboxItem: typeof import('./../../common/components/moduleBase/CrmCheckboxItem.vue')['default']
    CrmDatePicker: typeof import('./../../common/components/moduleBase/CrmDatePicker.vue')['default']
    CrmDialog: typeof import('./../../common/components/moduleBase/CrmDialog.vue')['default']
    CrmDialogTabs: typeof import('./../../common/components/moduleBase/CrmDialogTabs.vue')['default']
    CrmFormWrapper: typeof import('./../../common/components/module/CrmFormWrapper.vue')['default']
    CrmImportFile: typeof import('./../../common/components/moduleBase/CrmImportFile.vue')['default']
    CrmInput: typeof import('./../../common/components/moduleBase/CrmInput.vue')['default']
    CrmInputItem: typeof import('./../../common/components/moduleBase/CrmInputItem.vue')['default']
    CrmRadio: typeof import('./../../common/components/moduleBase/CrmRadio.vue')['default']
    CrmRadioItem: typeof import('./../../common/components/moduleBase/CrmRadioItem.vue')['default']
    CrmSearchSelect: typeof import('./../../common/components/moduleBase/CrmSearchSelect.vue')['default']
    CrmSearchSelectItem: typeof import('./../../common/components/moduleBase/CrmSearchSelectItem.vue')['default']
    CrmSelect: typeof import('./../../common/components/moduleBase/CrmSelect.vue')['default']
    CrmSelectItem: typeof import('./../../common/components/moduleBase/CrmSelectItem.vue')['default']
    DateRange: typeof import('./../../common/components/moduleBase/DateRange.vue')['default']
    DotLineChart: typeof import('./../../common/components/charts/DotLineChart.vue')['default']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    FormTabs: typeof import('./../../common/components/moduleBase/FormTabs.vue')['default']
    FormWrapper: typeof import('./../../common/components/module/FormWrapper.vue')['default']
    LabelItem: typeof import('./../../common/components/moduleBase/LabelItem.vue')['default']
    Layout: typeof import('./../../common/components/layout/index.vue')['default']
    Loading: typeof import('./../../common/components/moduleBase/Loading.vue')['default']
    Logo: typeof import('./../../common/components/layout/components/Logo/index.vue')['default']
    MenuDrop: typeof import('./../../common/components/moduleBase/MenuDrop.vue')['default']
    MenuItem: typeof import('./../../common/components/layout/components/Navigation/components/MenuItem.vue')['default']
    MoreMenu: typeof import('./../../common/components/layout/components/Navigation/components/MoreMenu.vue')['default']
    MultiRowMergeTable: typeof import('./../../common/components/module/MultiRowMergeTable.vue')['default']
    Navigation: typeof import('./../../common/components/layout/components/Navigation/index.vue')['default']
    NewPageLayout: typeof import('./../../common/components/layout/newPageLayout.vue')['default']
    Pagination: typeof import('./../../common/components/module/Pagination.vue')['default']
    RadarChart: typeof import('./../../common/components/charts/RadarChart.vue')['default']
    RichEditor: typeof import('./../../common/components/richEditor/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SemiCircle: typeof import('./../../common/components/charts/SemiCircle.vue')['default']
    SettingBar: typeof import('./../../common/components/layout/components/SettingBar/index.vue')['default']
    SvgIcons: typeof import('./../../common/components/moduleBase/SvgIcons.vue')['default']
    TableWrapper: typeof import('./../../common/components/module/TableWrapper.vue')['default']
    TableWrapperAdd: typeof import('./../../common/components/module/TableWrapperAdd.vue')['default']
    TagsView: typeof import('./../../common/components/layout/components/TagsView/index.vue')['default']
    ThemeTable: typeof import('./../../common/components/module/ThemeTable.vue')['default']
    TreeNodeItem: typeof import('./../../common/components/module/TreeNodeItem.vue')['default']
    TreeSelect: typeof import('./../../common/components/moduleBase/TreeSelect.vue')['default']
  }
}
