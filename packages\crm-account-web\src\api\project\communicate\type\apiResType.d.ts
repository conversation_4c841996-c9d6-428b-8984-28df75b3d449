export {}
declare module './apiResType' {
    /**
     * 用户信息
     */
    interface UserInfo {
        /**
         * 用户编码
         */
        code: string
        /**
         * 用户名称
         */
        name: string
    }

    /**
     * 客户沟通记录新增页初始化响应数据
     */
    interface VisitInitRes {
        /**
         * 客户目前存量
         */
        marketVal: string
        /**
         * 客户目前综合健康度
         */
        healthAvgStar: string
        /**
         * 主管列表
         */
        manageUserList: UserInfo[]
        /**
         * 总部业资列表
         */
        supervisorUserList: UserInfo[]
        /**
         * 其他列表
         */
        otherUserList: UserInfo[]
    }

    /**
     * IPS报告信息
     */
    interface ReportInfo {
        /**
         * 报告ID
         */
        reportId: string
        /**
         * 报告名称
         */
        reportName: string
        /**
         * 创建时间
         */
        createTime: string
    }

    /**
     * 查询IPS报告列表响应数据
     */
    interface QueryAssetReportRes {
        /**
         * 报告列表
         */
        reportList: ReportInfo[]
    }

    /**
     * 新增客户沟通记录响应数据
     */
    interface AddCommunicateRes {
        /**
         * 操作结果
         */
        result: any
    }

    /**
     * 陪访人信息
     */
    interface IAccompanyingInfo {
        /**
         * 陪访人姓名
         */
        userName: string
        /**
         * 主管姓名
         */
        managerName: string
        /**
         * 主管概要
         */
        summary: string
        /**
         * 主管建议
         */
        suggestion: string
    }

    /**
     * 查询客户拜访纪要明细响应数据
     */
    interface IVisitMinutesDetailRes {
        /**
         * 客户姓名
         */
        custName: string
        /**
         * 投顾客户号
         */
        consCustNo: string
        /**
         * 拜访日期，格式YYYYMMDD
         */
        visitDt: string
        /**
         * 沟通方式
         */
        visitType: string
        /**
         * 客户存量
         */
        marketVal: string
        /**
         * 客户综合健康度
         */
        healthAvgStar: string
        /**
         * 拜访目的列表
         */
        visitPurpose: string[]
        /**
         * 拜访目的其他说明
         */
        visitPurposeOther: string
        /**
         * IPS报告信息
         */
        ipsReport: {
            reportId: string
            reportTitle: string
        }
        /**
         * 提供资料
         */
        giveInformation: string
        /**
         * 陪访人姓名，多个逗号分隔
         */
        accompanyingUser: string
        /**
         * 参与人员及角色
         */
        attendRole: string
        /**
         * 产品服务反馈
         */
        productServiceFeedback: string
        /**
         * IPS报告反馈
         */
        ipsFeedback: string
        /**
         * 人民币金额
         */
        rmb: string
        /**
         * 外币金额
         */
        foreign: string
        /**
         * 关注资产
         */
        focusAsset: string
        /**
         * 客户需求
         */
        estimateNeedBusiness: string
        /**
         * 工作计划
         */
        nextPlan: string
        /**
         * 陪访人列表
         */
        accompanyingList: IAccompanyingInfo[]
        /**
         * 是否可编辑
         */
        canEditData: boolean
    }

    /**
     * 保存客户反馈响应数据
     */
    interface ISaveCustFeedbackRes {
        /**
         * 成功标识 true:成功 false:失败
         */
        verifyMsg: string
    }

    export {
        VisitInitRes,
        UserInfo,
        QueryAssetReportRes,
        ReportInfo,
        AddCommunicateRes,
        IVisitMinutesDetailRes,
        IAccompanyingInfo,
        ISaveCustFeedbackRes
    }
}
