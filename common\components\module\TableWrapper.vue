<!--
 * @Description: 通用table列表页面容器
 * @Author: chao<PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-20 18:19:26
 * @FilePath: /crm-web/common/components/module/TableWrapper.vue
 * @slot searchArea, 搜索条件区域
 * @slot operationBtns, 操作按钮栏
 * @slot tableContentMiddle, 表格列表区域
 * @slot tableContentBottom, table列表底部区域, 分页器等
-->
<template>
    <div :class="`crm_table_wrapper ${className}`">
        <div :class="['crm_table_top', { pack_up: !topAreaExpand }]">
            <div v-if="showSearchArea" class="search_area">
                <div ref="searchArea" class="left">
                    <slot name="searchArea" />
                </div>
                <div class="right">
                    <el-button
                        plain
                        :icon="iconType === 'add' ? Plus : Search"
                        @click="$emit('searchFn')"
                        >{{ searchBtnTxt }}</el-button
                    >
                </div>
                <div v-show="showPackupBtn" class="pack_up_btn" @click="handleExpand">
                    <el-icon size="12px"><arrow-up /></el-icon>
                </div>
            </div>
            <div v-if="showTabsPanel" class="wrapper_tabs_panel">
                <slot name="tabsPanel" />
            </div>
            <div
                v-if="showOperationBtns"
                class="operation_btns"
                :class="{ 'align-right': !showOperationLeft, 'margin-top0': showTabsPanel }"
            >
                <div v-if="showOperationLeft" class="operation-left">
                    <slot name="operationLeft" />
                </div>
                <div class="operation-right">
                    <slot name="operationBtns" />
                </div>
            </div>
        </div>
        <div class="crm_table_middle">
            <div class="crm_table_panel">
                <slot name="tablePanel" />
            </div>
            <div class="crm_table_list">
                <slot name="tableContentMiddle" />
            </div>
        </div>
        <div class="crm_table_bottom">
            <slot name="tableContentBottom" />
        </div>
        <slot />
    </div>
</template>

<script>
    import { Search, ArrowUp, Plus } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'TableWrapper',
        components: { ArrowUp },
        props: {
            // 搜索条目标签名称的宽度
            labelWidth: {
                type: String,
                default: ''
            },
            // 是否展示操作按钮模块，默认展示
            showOperationBtns: {
                type: Boolean,
                default: true
            },
            showOperationLeft: {
                type: Boolean,
                default: false
            },
            showSearchArea: {
                type: Boolean,
                default: true
            },
            showTabsPanel: {
                type: Boolean,
                default: false
            },
            className: {
                type: String,
                default: ''
            },
            searchBtnTxt: {
                type: String,
                default: '查询'
            },
            iconType: {
                type: String,
                default: ''
            }
        },
        emits: ['searchFn'],
        setup() {
            return { Search, Plus }
        },
        data() {
            return {
                showPackupBtn: true, // 是否显示收起展开按钮
                topAreaExpand: true // 顶部区域是否展开
            }
        },
        watch: {
            labelWidth: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            const labelList =
                                this.$refs.searchArea.querySelectorAll('.crm-input-item .label') ||
                                []
                            labelList.forEach(item => {
                                item.style.width = newVal
                            })
                        })
                    }
                },
                immediate: true
            }
        },
        mounted() {
            this.resizeFn()
            window.addEventListener('resize', this.resizeFn)
        },
        methods: {
            handleExpand() {
                this.topAreaExpand = !this.topAreaExpand
            },
            resizeFn() {
                if (this.$refs.searchArea) {
                    // this.showPackupBtn = this.$refs.searchArea.offsetHeight > 56
                }
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.resizeFn)
            }
        }
    })
</script>

<style lang="less">
    .crm_table_wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;

        &.crm_tabs_panel_wraper {
            height: calc(~'100vh - 142px');
        }

        &.crm_wraper {
            height: calc(~'100vh');
        }

        &.crm-dialog-table-wraper {
            height: calc(~'70vh');
            padding: 0;
        }

        .crm_table_top {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            padding: 0;
            background-color: @bg_main;

            &.pack_up {
                .search_area {
                    .left {
                        height: 54px;
                    }
                }

                .pack_up_btn {
                    i {
                        transform: rotate(180deg);
                    }
                }
            }

            .pack_up_btn {
                position: absolute;
                right: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 16px;
                cursor: pointer;
                background-color: #b5b5bc;

                &:hover {
                    background-color: @theme_main_hover;
                }

                i {
                    color: @font_color_01;
                    transition: all 0.2s;
                }
            }

            .search_area {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: flex-start;
                width: 100%;
                width: calc(100% - 20px);
                padding: 0 15px;
                margin: 5px auto 0;
                overflow: hidden;
                background-color: @font_color_01;
                box-shadow: 0 0 12px 0 rgba(210, 211, 224, 0.47);

                .left {
                    display: flex;
                    flex: 1;
                    flex-wrap: wrap;
                    align-items: flex-start;
                    padding-bottom: 15px;
                    transition: height 0.2s;
                }

                .right {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    width: 100px;

                    &::before {
                        display: block;
                        width: 1px;
                        height: 42px;
                        margin-right: 16px;
                        content: '';
                        background: linear-gradient(
                            to top,
                            @font_color_01,
                            #b5b4b9,
                            @font_color_01
                        );
                    }

                    .el-button {
                        height: 28px;
                        font-size: 12px;
                        line-height: 28px;
                        color: #797979;
                        background-color: @font_color_01;
                        border-color: #e1e0e0;
                        border-radius: 14px;

                        &:hover {
                            color: #686767;
                            background-color: @bg_main;
                            border-color: #c4c4c4;
                        }

                        &:active {
                            color: #4a4a4d;
                            background-color: #e2e4ea;
                            border-color: #c4c4c4;
                        }

                        span {
                            margin-left: 2px;
                        }
                    }
                }

                .crm-input-item {
                    .value {
                        width: 190px;
                    }
                }
            }

            .wrapper_tabs_panel {
                width: calc(100% - 20px);
                margin: 7px auto 0;
            }

            .operation_btns {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: calc(100% - 20px);
                height: 40px;
                padding-right: 10px;
                margin: 7px auto 0;
                background-color: @font_color_01;
                box-shadow: 0 0 12px 0 #e6e8eb;

                .operation-right {
                    display: flex;
                    align-items: center;
                }

                &.align-right {
                    justify-content: flex-end;
                }

                &.margin-top0 {
                    margin-top: 0;
                }

                .el-button {
                    margin-left: 10px;
                    // border-radius: 0;
                }
            }
        }

        .crm_table_middle {
            flex: 1;
            padding: 0 10px;
            overflow: hidden;
            background-color: @bg_main;
            // 通用table列表
            .crm_table_list {
                height: 100%;
                min-height: 100px;

                .el-table__body {
                    td {
                        border: none;
                    }
                }
            }
        }
    }

    .blank-view-container {
        .crm_table_wrapper {
            &.crm_tabs_panel_wraper {
                height: calc(100vh - 56px);
            }
        }
    }
</style>
