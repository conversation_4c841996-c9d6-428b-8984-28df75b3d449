{"name": "crm-template", "private": true, "version": "0.0.0", "type": "commonjs", "scripts": {"prepare": "husky install", "dev": "vite --host --port 5100", "build": "vue-tsc && vite build", "preview": "vite preview", "lint-comment": "========eslint 代码校验========", "lint": "eslint --fix --ext .ts,.tsx,.vue,.js,.jsx", "stylelint-comment": "========stylelint 样式校验========", "stylelint": "stylelint \"./**/*.{css,less,vue,html}\" --fix", "prettier-comment": "========代码格式化========", "prettier": "prettier --ignore-path ../../.prettierignore . --write"}, "dependencies": {"@babel/core": "^7.0.0-0", "@ckeditor/ckeditor5-alignment": "^36.0.1", "@ckeditor/ckeditor5-autosave": "^36.0.1", "@ckeditor/ckeditor5-basic-styles": "^36.0.1", "@ckeditor/ckeditor5-build-classic": "^36.0.1", "@ckeditor/ckeditor5-core": "^36.0.1", "@ckeditor/ckeditor5-editor-classic": "^36.0.1", "@ckeditor/ckeditor5-essentials": "^36.0.1", "@ckeditor/ckeditor5-font": "^36.0.1", "@ckeditor/ckeditor5-heading": "^36.0.1", "@ckeditor/ckeditor5-horizontal-line": "^36.0.1", "@ckeditor/ckeditor5-image": "^36.0.1", "@ckeditor/ckeditor5-indent": "^36.0.1", "@ckeditor/ckeditor5-list": "^36.0.1", "@ckeditor/ckeditor5-paragraph": "^36.0.1", "@ckeditor/ckeditor5-paste-from-office": "^36.0.1", "@ckeditor/ckeditor5-remove-format": "^36.0.1", "@ckeditor/ckeditor5-table": "^36.0.1", "@ckeditor/ckeditor5-theme-lark": "^36.0.1", "@ckeditor/ckeditor5-ui": "^36.0.1", "@ckeditor/ckeditor5-utils": "^36.0.1", "@ckeditor/ckeditor5-vue": "^4.0.1", "@ckeditor/vite-plugin-ckeditor5": "^0.1.1", "@element-plus/icons-vue": "^2.0.10", "@vue/reactivity": "^3.2.45", "@vue/shared": "^3.2.45", "axios": "^1.2.1", "ckeditor": "^4.12.1", "default-passive-events": "^2.0.0", "echarts": "^5.4.1", "element-plus": "^2.2.27", "js-cookie": "^3.0.1", "less": "^4.1.3", "less-loader": "^11.1.0", "lint-staged": "^13.2.3", "lodash": "^4.17.21", "moment": "^2.29.4", "pagedjs": "^0.4.1", "pinia": "^2.0.28", "vue": "^3.2.45", "vue-dompurify-html": "^4.1.4", "vue-router": "^4.1.6"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@commitlint/config-conventional": "^17.6.6", "@types/node": "^18.11.17", "@typescript-eslint/eslint-plugin": "^5.47.0", "@typescript-eslint/parser": "^5.47.0", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "commitizen": "^4.3.0", "commitlint": "^17.6.6", "eslint": "^8.30.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^24.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.8.0", "husky": "^8.0.3", "nacos": "^2.5.1", "postcss": "^8.4.20", "postcss-html": "^1.3.0", "postcss-less": "^6.0.0", "prettier": "^2.8.1", "stylelint": "^15.10.1", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^4.2.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^4.1.0", "typescript": "^4.9.4", "unplugin-auto-import": "^0.12.1", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.22.12", "rollup-plugin-visualizer": "^5.9.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "3.2.0", "vite": "4.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.0.11"}}