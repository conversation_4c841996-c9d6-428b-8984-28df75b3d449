/*
 * @Description: 固定路由配置
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: wencai.deng <EMAIL>
 * @LastEditTime: 2024-06-05 18:08:21
 * @FilePath: /crm-template/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        // component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'templateList' },
        children: [
            {
                path: 'templateList',
                name: 'templateList',
                meta: {
                    title: '双录模版列表页'
                },
                component: () => import('@/views/doubletrade/templateList/templateListIndex.vue')
            },
            {
                path: 'createTemplate',
                name: 'createTemplate',
                meta: {
                    title: '创建双录模版'
                },
                component: () => import('@/views/doubletrade/createTemplate/createTemplate.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
