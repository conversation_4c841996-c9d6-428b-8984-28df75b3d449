/*
 * @Description: 返回响应接口常量定义
 * @Author: chao<PERSON>.wu
 * @Date: 2023-06-28 16:05:02
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-06 15:34:11
 * @FilePath: /crm-web/common/constant/resConst.ts
 */
// 一级返回码
export const responseCode = {
    SUCCESS: '0000', // 成功
    CRM_SUCCESS: 'C010000', // 成功
    DS_SUCCESS: 'C030000', // 成功
    RELOGIN: '0004', // 重新登录
    CRM_PARAM_ERROR: 'C010002', // 参数错误
    PARAM_ERROR: '0002', // 参数错误
    UNLOGIN: 'C010003', // 未登录
    SYS_ERROR: '9999', // 系统异常
    CRM_SYS_ERROR: 'C019999', // 系统异常
    CRM_SYS_NOACCESS: 'C010007', // 系统异常
    SYS_FILED: '0001', // 系统异常
    CRM_SYS_FILED: 'C010001', // 系统异常
    VERIFY_ERROR: '1000', // 校验错误
    NOT_FOUND: '4000', // 不存在
    RUN_ERROR: '5000' // 执行错误
}

// 二级返回码
export const returnCodeRes = {
    SUCCESS: '0000', // 成功
    CRM_SUCCESS: 'C020000', // 成功
    DS_SUCCESS: 'C030000', // 成功
    CRM_PARAM_ERROR: 'C020002', // 参数错误
    PARAM_ERROR: '0002', // 参数错误
    RELOGIN: '0004', // 重新登录
    CRM_UNLOGIN: 'C020003', // 未登录
    SYS_ERROR: '9999', // 系统异常
    CRM_SYS_ERROR: 'C029999', // 系统异常
    CRM_SYS_NOACCESS: 'C020007', // 系统异常
    SYS_FILED: '0001', // 请求失败
    CRM_SYS_FILED: 'C020001' // 请求失败
}
