export {}
declare module './apiReqType' {
    /**
     * 客户沟通记录新增页初始化请求参数
     */
    interface VisitInitReq {
        /**
         * 投顾客户号
         */
        consCustNo: string
    }

    /**
     * 查询IPS报告列表请求参数
     */
    interface QueryAssetReportReq {
        /**
         * 客户号
         */
        consCustNo: string

        /**
         * 第几页
         */
        page: number

        /**
         * 每页条数
         */
        size: number

        /**
         * 排序字段
         */
        sort: string

        /**
         * 排序方式
         */
        order: string
    }

    /**
     * 用户搜索请求参数
     */
    interface QueryUserReq {
        /**
         * 搜索参数
         */
        searchParam: string

        /**
         * 搜索类型 1:项目经理 2:所有正常用户
         */
        searchType: string
    }

    /**
     * 查询拜访当日客户的健康情况请求参数
     */
    interface QueryHealthReq {
        /**
         * 客户号
         */
        consCustNo: string

        /**
         * 客户号
         */
        visitDt: string
    }

    /**
     * 陪访人信息
     */
    interface AccompanyingReq {
        /**
         * 陪访人类型(1-项目经理 2-主管 3-总部业资 4-其他)
         */
        accompanyingType: string
        /**
         * 陪访人用户ID
         */
        accompanyingUserId: string
    }

    /**
     * 预约信息
     */
    interface BookingReq {
        /**
         * 预约沟通方式
         */
        nextvisittype: string
        /**
         * 预约日期(格式YYYYMMDD)
         */
        nextdt: string
        /**
         * 预约开始时间(格式HH:MM)
         */
        nextstarttime: string
        /**
         * 预约结束时间(格式HH:MM)
         */
        nextendtime: string
        /**
         * 预约内容
         */
        nextvisitcontent: string
    }

    /**
     * 沟通记录信息
     */
    interface CommunicateReq {
        /**
         * 客户号
         */
        consCustNo: string
        /**
         * 拜访日期(格式YYYYMMDD)
         */
        visitDate: string
        /**
         * 沟通方式(见面、线上会议、电话等)
         */
        visittype: string
        /**
         * 沟通内容摘要
         */
        commcontent: string
    }

    /**
     * 拜访纪要信息
     */
    interface VisitMinutesReq {
        /**
         * 客户存量
         */
        marketVal: string
        /**
         * 客户目前综合健康度，1-5星
         */
        healthAvgStar: string
        /**
         * 拜访目的(多选，逗号分隔，如"1,2,3")
         */
        visitPurpose: string
        /**
         * 拜访目的其他说明
         */
        visitPurposeOther?: string
        /**
         * IPS报告ID
         */
        assetReportId?: string
        /**
         * 提供资料
         */
        giveInformation?: string
        /**
         * 客户参与人员及角色
         */
        attendRole?: string
        /**
         * 对产品或服务的具体反馈
         */
        productServiceFeedback?: string
        /**
         * 对于IPS报告反馈
         */
        ipsFeedback?: string
        /**
         * 近期可用于加仓的金额(人民币)
         */
        addAmountRmb?: string
        /**
         * 近期可用于加仓的金额(外币)
         */
        addAmountForeign?: string
        /**
         * 近期关注的资产类别或具体产品
         */
        focusAsset?: string
        /**
         * 评估客户需求
         */
        estimateNeedBusiness?: string
        /**
         * 下一步工作计划
         */
        nextPlan?: string
        /**
         * 陪访人列表
         */
        accompanyingList?: AccompanyingReq[]
    }

    /**
     * 新增客户沟通记录请求参数
     */
    interface AddCommunicateReq {
        /**
         * 沟通记录信息
         */
        communicateReq: CommunicateReq
        /**
         * 预约信息
         */
        bookingReq?: BookingReq
        /**
         * 拜访纪要信息
         */
        visitMinutesReq?: VisitMinutesReq
    }

    /**
     * 查询客户拜访纪要明细请求参数
     */
    interface IVisitMinutesDetailReq {
        /**
         * 拜访纪要ID
         */
        visitMinutesId: string
    }

    /**
     * 陪访人列表
     */
    interface AccompanyingListReq {
        /**
         * 陪访人类型
         */
        accompanyingType: string
        /**
         * 陪访人Id
         */
        accompanyingUserId: string
    }

    /**
     * 保存客户反馈请求参数
     */
    interface ISaveCustFeedbackReq {
        /**
         * 拜访纪要ID
         */
        visitMinutesId: string
        /**
         * 陪访人列表
         */
        accompanyingList?: AccompanyingListReq[]
        /**
         * 客户参与角色
         */
        attendRole?: string
        /**
         * 对产品或服务的具体反馈
         */
        productServiceFeedback?: string
        /**
         * 对于IPS报告反馈
         */
        ipsFeedback?: string
        /**
         * 人民币金额
         */
        addAmtRmb?: string
        /**
         * 外币金额
         */
        addAmtForeign?: string
        /**
         * 近期关注的资产类别或具体产品
         */
        focusAsset?: string
        /**
         * 评估客户需求
         */
        estimateNeedBusiness?: string
        /**
         * 下一步工作计划
         */
        nextPlan?: string
    }

    export {
        VisitInitReq,
        QueryAssetReportReq,
        AddCommunicateReq,
        QueryHealthReq,
        QueryUserReq,
        IVisitMinutesDetailReq,
        ISaveCustFeedbackReq,
        AccompanyingListReq
    }
}
