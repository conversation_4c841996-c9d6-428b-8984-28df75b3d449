<!--
 * @Description: IPS报告选择弹窗
 * @Author: ai
 * @Date: 2024-03-21
-->
<template>
    <el-dialog
        v-model="dialogVisible"
        title="请选择IPS报告"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @open="handleDialogOpen"
    >
        <div class="ips-report-dialog">
            <div class="report-list">
                <el-table
                    :data="reportList"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    @sort-change="handleSortChange"
                >
                    <el-table-column type="selection" width="55" :selectable="isSelectable" />
                    <el-table-column
                        prop="serialNo"
                        label="序号"
                        width="120"
                        type="index"
                        :index="indexMethod"
                    />
                    <el-table-column prop="reportName" label="报告标题" />
                    <el-table-column
                        prop="creDt"
                        label="创建时间"
                        width="180"
                        sortable="custom"
                        :sort-orders="['descending', 'ascending', null]"
                        default-sort="descending"
                    />
                </el-table>
            </div>
            <div class="pagination-container">
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[100, 200, 300, 500, 1000, 2000]"
                    @change="handleCurrentChange"
                />
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, onMounted, watch } from 'vue'
    import { useRoute } from 'vue-router'
    import { queryAssetReport } from '@/api/project/communicate'
    import { fetchRes } from '@common/utils'
    import type { QueryAssetReportReq } from '@/api/project/communicate/type/apiType'
    import { ElMessage } from 'element-plus'

    interface IpsReport {
        reportId: string
        reportName: string
        creDt: string
    }

    // 路由参数
    const route = useRoute()
    const consCustNo = route.query.consCustNo as string

    // 控制弹窗显示
    const dialogVisible = ref(false)

    // 分页对象
    const pageObj = ref({
        page: 1,
        size: 100,
        total: 0
    })

    // 排序参数
    const sortParams = ref({
        sort: 'creDt',
        order: 'desc'
    })

    // 报告列表数据
    const reportList = ref<IpsReport[]>([])

    // 当前选中的报告
    const selectedReport = ref<IpsReport | null>(null)

    // 序号计算方法
    const indexMethod = (index: number) => {
        return (pageObj.value.page - 1) * pageObj.value.size + index + 1
    }

    // 处理弹窗打开
    const handleDialogOpen = () => {
        // 重置分页
        pageObj.value.page = 1
        // 设置默认排序
        sortParams.value = {
            sort: 'creDt',
            order: 'desc'
        }
        // 获取数据
        getReportList()
    }

    // 处理排序变化
    const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
        if (prop === 'creDt') {
            sortParams.value.sort = 'creDt'
            sortParams.value.order =
                order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
        } else {
            sortParams.value.sort = ''
            sortParams.value.order = ''
        }
        getReportList()
    }

    // 获取报告列表数据
    const getReportList = () => {
        const params: QueryAssetReportReq = {
            consCustNo,
            page: pageObj.value.page,
            size: pageObj.value.size,
            sort: sortParams.value.sort,
            order: sortParams.value.order
        }

        fetchRes(queryAssetReport(params), {
            successCB: (res: any) => {
                const { rows, total } = res || {}
                reportList.value = rows || []
                pageObj.value.total = Number(total)
            },
            successTxt: '',
            failTxt: '获取报告列表失败！',
            fetchKey: ''
        })
    }

    // 表格选择变化处理
    const handleSelectionChange = (selection: IpsReport[]) => {
        // 由于是单选,只取最后一个选中项
        selectedReport.value = selection.length > 0 ? selection[selection.length - 1] : null
    }

    // 控制表格只能单选
    const isSelectable = (row: IpsReport) => {
        // 如果没有选中项,则允许选择
        if (!selectedReport.value) {
            return true
        }
        // 如果是当前选中项,则允许取消选择
        return row.reportId === selectedReport.value.reportId
    }

    // 分页变化处理
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value.page = current
        pageObj.value.size = perPage
        getReportList()
    }

    // 取消按钮处理
    const handleCancel = () => {
        dialogVisible.value = false
        selectedReport.value = null
    }

    // 确定按钮处理
    const handleConfirm = () => {
        // 校验是否选择了报告
        if (!selectedReport.value) {
            ElMessage.warning('请选择IPS报告')
            return
        }

        // 触发选中事件
        emit('select', selectedReport.value)
        dialogVisible.value = false
        selectedReport.value = null
    }

    // 定义事件
    const emit = defineEmits(['select'])

    // 对外暴露方法
    defineExpose({
        dialogVisible
    })
</script>

<style lang="less" scoped>
    .ips-report-dialog {
        .report-list {
            max-height: 400px;
            margin: 20px 0;
            overflow-y: auto;

            :deep(.el-table__body-wrapper) {
                overflow-y: auto;
            }
        }

        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }

        .dialog-footer {
            display: flex;
            justify-content: center;
            padding-top: 20px;
        }
    }
</style>
