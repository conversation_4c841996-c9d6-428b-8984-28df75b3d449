export {}
declare module './apiResType' {
    // 查询拜访反馈详情的响应数据接口
    interface IVisitFeedbackDetailRes {
        custName: string
        consCustNo: string
        visitDt: string
        visitType: string
        marketVal: string
        healthAvgStar: string
        visitPurpose: string[]
        visitPurposeOther: string
        ipsReport: {
            reportId: string
            reportTitle: string
        }
        giveInformation: string
        accompanyingUser: string
        attendRole: string
        productServiceFeedback: string
        ipsFeedback: string
        addAmountRmb: string
        addAmountForeign: string
        focusAsset: string
        estimateNeedBusiness: string
        nextPlan: string
        userName: string
        accompanyingId: string
        managerName: string
        summary: string
        suggestion: string
        canNotEditSummary: boolean
    }

    export { IVisitFeedbackDetailRes }
}
