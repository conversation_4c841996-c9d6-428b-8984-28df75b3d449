/*
 * @Description: 固定路由配置
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 13:25:22
 * @FilePath: /crm-web/packages/crm-wechat-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'custSend' },
        children: [
            {
                path: 'custSend',
                name: 'custSend',
                meta: {
                    title: '企微发送'
                },
                component: () => import('@/views/wxMicroTask/custSend/custSendIdx.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
