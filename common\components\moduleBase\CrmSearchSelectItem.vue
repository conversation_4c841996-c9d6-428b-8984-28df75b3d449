<!--
 * @Description: 下拉搜索组件
 * @Author: chaohui.wu
 * @Date: 2023-03-16 14:24:05
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-03-16 14:24:51
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmSearchSelectItem.vue
 *  
-->
<template>
    <div class="crm_input_item">
        <div class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-search-select v-bind="$attrs" />
        </div>
        <slot name="suffix" />
        <slot />
    </div>
</template>

<script>
    export default defineComponent({
        name: 'CrmSearchSelectItem',
        props: {
            label: {
                type: String,
                default: ''
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm_input_item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        .label {
            min-width: 72px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
