<!--
 * @Description: form表单容器组件
 * @Author: chaohui.wu
 * @Date: 2023-04-07 15:04:10
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-07 19:14:00
 * @FilePath: /crm-asset-web/src/components/module/FormWrapper.vue
 *  
-->
<template>
    <el-form class="crm_dialog_form" :form-rules="formRules" v-bind="$attrs">
        <el-form-item
            v-for="(store, index) in formList"
            v-show="store.slot"
            :key="`columns${index}`"
            :label="`${store.label}：`"
            :prop="store.prop"
            label-width="140px"
            :inline-message="store.inLineMessage"
            :rules="formRules.rules"
        >
            <!-- 内容自定义 -->
            <template v-if="store.slot" #default>
                <slot :name="store.slotName"></slot>
            </template>
        </el-form-item>
    </el-form>
</template>

<script setup lang="ts">
    const props = withDefaults(
        defineProps<{
            formList?: any
            formRules?: { rules: [] }
        }>(),
        {
            formList: () => {
                return []
            },
            formRules: () => {
                return {
                    rules: []
                }
            }
        }
    )
</script>

<style lang="less" scoped></style>
