<!--
 * @Description: 弹窗
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-12 16:32:54
 * @FilePath: /crm-web/common/components/moduleBase/CrmDialog.vue
 * @className 1.crm-tabs-card1 定制tabs样式groupProducu
-->

<template>
    <el-dialog :class="`crm-dialog ${className}`" v-bind="$attrs">
        <template v-for="(slotName, index) in slotList" :key="`dialog-slot${index}`" #[slotName]>
            <slot v-if="slotName" :key="slotName" :name="slotName" />
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    defineOptions({
        name: 'CrmDialog'
    })

    const props = withDefaults(
        defineProps<{
            slotList?: string[]
            className?: string
        }>(),
        {
            slotList: () => {
                // header、footer、default
                return ['default', 'footer']
            },
            className: ''
        }
    )
</script>

<style lang="less">
    .crm-dialog,
    .el-dialog {
        min-width: 360px;

        &.dialog-table {
            .el-dialog__body {
                padding: 0;
            }
        }

        .el-dialog__header {
            width: 100%;
            padding: 12px 20px;
            margin-right: 0;
            background-color: @bg_main_01;

            .el-dialog__title {
                font-size: 15px;
                font-weight: 700;
            }
        }

        .el-dialog__headerbtn {
            top: 0;
            right: 0;
            font-size: 18px;

            &:hover {
                i {
                    color: @theme_main_hover;
                }
            }

            i {
                color: #303133;
                transition: color 0.2s;
            }
        }

        .el-dialog__body {
            box-sizing: border-box;
            max-height: @max_dialog_height;
            padding: 20px;
            overflow-y: auto;
        }

        .el-dialog__footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            padding: 10px 20px;
            background-color: @bg_main_01;

            .el-button {
                height: 30px;
                line-height: 30px;
            }
        }

        &.dialog-default {
            .el-dialog__body {
                box-sizing: border-box;
                max-height: @max_dialog_height;
                padding: 20px;
                overflow-y: auto;
            }
        }

        .crm_single_form {
            .el-form-item__label {
                padding-right: 4px;
                color: @font_color_02;
                text-align: left;
            }

            .el-form-item {
                margin-bottom: 0;
            }
        }
    }
</style>
