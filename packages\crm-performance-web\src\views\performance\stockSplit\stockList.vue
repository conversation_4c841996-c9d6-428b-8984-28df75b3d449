<!--
 * @Description: 增量分成列表页
 * @Author: chaohui.wu
 * @Date: 2023-03-16 11:15:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-20 16:31:57
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/stockList.vue
 * @notice: 后期需要抽取hooks
-->
<template>
    <div class="report-list-module">
        <table-wrapper class-name="crm_wraper" @searchFn="queryList">
            <template #searchArea>
                <label-item label="新管理层/投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.constObj"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <label-item label="原管理层/投顾">
                    <ReleatedSelect
                        ref="formerSelect"
                        v-model="queryForm.formerConstObj"
                        :organization-list="formerOrganizationList"
                        :cons-list-default="formerConsultList"
                        :default-org-code="formerOrgCodeDefault"
                        :default-cons-code="formerConsCodeDefault"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <label-item label="分成类型">
                    <crm-select
                        v-model="queryForm.configType"
                        placeholder="请选择分成类型"
                        label-format="label"
                        value-format="key"
                        :option-list="CONFIG_TYPE"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="在职状态_新">
                    <crm-select
                        v-model="queryForm.newlyOnJobStatus"
                        placeholder="请选择在职状态_新"
                        label-format="label"
                        value-format="key"
                        :option-list="JOB_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="在职状态_原">
                    <crm-select
                        v-model="queryForm.formerOnJobStatus"
                        placeholder="请选择在职状态_原"
                        label-format="label"
                        value-format="key"
                        :option-list="JOB_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="是否生效">
                    <crm-select
                        v-model="queryForm.validFlag"
                        placeholder="请选择是否生效"
                        label-format="label"
                        value-format="key"
                        :option-list="VALID_FLAG"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="计算时间">
                    <date-range
                        v-model="queryForm.calDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
                <label-item label="层级">
                    <crm-select
                        v-model="queryForm.configLevel"
                        placeholder="请选择层级"
                        label-format="label"
                        value-format="key"
                        :option-list="CONFIG_LEVEL"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="激活状态">
                    <crm-select
                        v-model="queryForm.activeFlag"
                        placeholder="请选择激活状态"
                        label-format="label"
                        value-format="key"
                        :option-list="VALID_FLAG"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="激活时间">
                    <date-range
                        v-model="queryForm.activeDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
                <label-item label="投顾客户号">
                    <crm-input
                        v-model="queryForm.custNo"
                        placeholder="可输入投顾客户号精准查询"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="审核状态">
                    <crm-select
                        v-model="queryForm.auditStatus"
                        placeholder="请选择审核状态"
                        label-format="label"
                        value-format="key"
                        :option-list="AYUDT_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="涉及投顾">
                    <ReleatedSelect
                        ref="formerSelect"
                        v-model="queryForm.relatedConsObj"
                        :organization-list="formerOrganizationList"
                        :cons-list-default="formerConsultList"
                        :default-org-code="formerOrgCodeDefault"
                        :default-cons-code="formerConsCodeDefault"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button size="small" :radius="true" plain @click="resetQueryForm"
                    >清空</crm-button
                >
                <crm-button size="small" :radius="true" plain @click="handleExplain"
                    >说明</crm-button
                >
                <crm-button
                    v-show="isPremission('export')"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="handleExport"
                    >导出</crm-button
                >
                <crm-button
                    v-show="isPremission('add')"
                    size="small"
                    :icon="Plus"
                    type="primary"
                    :radius="true"
                    @click="handleShow('', 'add')"
                    >新增</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :columns="stockListTableColumn"
                    :data="tableList"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    :empty-text="requestData ? '暂无数据' : ' '"
                    height="100%"
                    :no-index="false"
                    :border="true"
                    operation-width="200"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="operationShow(scope.row, ORERATE_LIST_SELECT.EDIT)"
                            size="small"
                            :text="true"
                            link
                            @click="handleShow(scope.row, 'edit')"
                            >编辑</el-button
                        >
                        <el-button
                            v-if="operationShow(scope.row, ORERATE_LIST_SELECT.AUDT)"
                            size="small"
                            :text="true"
                            link
                            @click="handleShow(scope.row, 'audt')"
                            >审核</el-button
                        >
                        <el-button
                            v-if="operationShow(scope.row, ORERATE_LIST_SELECT.DELATE)"
                            size="small"
                            :text="true"
                            link
                            @click="handleDelate(scope.row)"
                            >删除</el-button
                        >
                        <el-button
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row, 'operateDetail')"
                            >操作记录</el-button
                        >
                    </template>
                    <template #operationRecord="{ scope }">
                        <el-button
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row, 'operateDetail')"
                            >明细</el-button
                        >
                    </template>
                    <template #custom="{ scope }">
                        <el-button
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row, 'customDetail')"
                            >明细</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[100, 200, 500, 1000, 2000]"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>
        <ExplainStock v-model="explainDialogVisiable"></ExplainStock>
        <AddStock
            v-if="addStockVisiable"
            v-model="addStockVisiable"
            :trans-data="stockObj"
            :organization-list="organizationList"
            :consult-list="excludeArr(consultList, [''], 'consCode')"
            :former-organization-list="formerOrganizationList"
            :former-consult-list="excludeArr(formerConsultList, [''], 'consCode')"
            :former-org-code="formerOrgCodeDefault"
            :former-cons-code="formerConsCodeDefault"
            :cons-code="consCodeDefault"
            :org-code="orgCodeDefault"
            @callBack="queryList"
        ></AddStock>
        <DelateStock
            v-if="delateStockVisiable"
            v-model="delateStockVisiable"
            :trans-data="stockObj"
            @callBack="queryList"
        ></DelateStock>
        <CustDetail
            v-if="detailStockVisiable"
            v-model="detailStockVisiable"
            :trans-data="stockObj"
        ></CustDetail>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Upload, Download, RefreshLeft, Delete } from '@element-plus/icons-vue'
    import ExplainStock from './components/explainStock.vue'
    import AddStock from './components/addStock.vue'
    import DelateStock from './components/delateStock.vue'
    import ReleatedSelect from './components/releatedSelect.vue'
    import CustDetail from './components/custDetail.vue'
    import {
        queryConfigList,
        initList,
        queryAllOrgTree,
        queryAuthOrgTree
    } from '@/api/project/stockSplit/stockSplitList'
    import {
        downloadFile,
        makeElementTree,
        fetchRes,
        message,
        fetchAll,
        excludeArr
    } from '@common/utils/index'
    import {
        CONFIG_TYPE,
        JOB_STATUS,
        VALID_FLAG,
        CONFIG_LEVEL,
        AYUDT_STATUS,
        ORERATE_LIST_SELECT
    } from '@/constant/index'
    import { stockListTableColumn, showTableColumn } from './scripts/tableData'
    import { useStockListData } from './scripts/stockListData'
    import { useTableList } from './scripts/hooks/useTableList'
    // 投顾管理层
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const {
        organizationList,
        consultList,
        formerOrganizationList,
        formerConsultList,
        orgCodeDefault,
        consCodeDefault,
        formerOrgCodeDefault,
        formerConsCodeDefault
    } = storeToRefs(stockListStore)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        id = ''
        configType = ''
        // formerConsCode = ''
        // formerOrgCode = ''
        constObj = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        formerConstObj = {
            orgCode: formerOrgCodeDefault.value ?? '',
            consCode: formerConsCodeDefault.value ?? ''
        }
        formerOnJobStatus = ''
        // newlyConsCode = ''
        // newlyOrgCode = ''
        newlyOnJobStatus = ''
        configLevel = ''
        custNo = ''
        auditStatus = ''
        validFlag = ''
        activeFlag = ''
        calStartDt = ''
        calEndDt = ''
        calDt = {
            startDate: '',
            endDate: ''
        }
        activeStartDt = ''
        activeEndDt = ''
        activeDt = {
            startDate: '',
            endDate: ''
        }
        relatedConsObj = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
    }

    /**
     * @description: 列表传参
     * @return {*}
     */
    const fetchListParams = () => {
        const { calDt, activeDt, constObj, formerConstObj, relatedConsObj } = queryForm.value || {}
        const { startDate: calStartDt, endDate: calEndDt } = calDt
        const { startDate: activeStartDt, endDate: activeEndDt } = activeDt
        const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj
        const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj
        const { orgCode: relatedOrgCode, consCode: relatedConsCode } = relatedConsObj
        // 处理请求接口数据
        return {
            ...pageObj.value,
            ...queryForm.value,
            newlyOrgCode,
            newlyConsCode,
            formerOrgCode,
            formerConsCode,
            calStartDt,
            calEndDt,
            activeStartDt,
            activeEndDt,
            relatedOrgCode,
            relatedConsCode
        }
    }

    // hooks
    const {
        tableList,
        pageObj,
        queryForm,
        isPremission,
        queryList,
        handleCurrentChange,
        handleReset,
        requestData
    } = useTableList({
        fetchListParams, // 接口参数
        fetchList: queryConfigList, // table列表查询接口
        initDefault: initList, // search默认初始化数据
        queryFormParams: QueryForm
    })

    /**
     * @description: 重置
     * @param {*} void
     * @return {*}
     */
    const resetQueryForm = (): void => {
        handleReset(() => {
            newlySelect.value.resetSelect()
            formerSelect.value.resetSelect()
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault, formerOrgCodeDefault, formerConsCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.value.constObj.orgCode = orgCodeDefault.value
                queryForm.value.constObj.consCode = consCodeDefault.value
            }
            if (formerOrgCodeDefault.value || formerConsCodeDefault.value) {
                queryForm.value.formerConstObj.orgCode = formerOrgCodeDefault.value
                queryForm.value.formerConstObj.consCode = formerConsCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    // 管理层投顾组件
    const newlySelect = ref()
    const formerSelect = ref()

    /**
     * @description: 编辑
     * @return {*}
     */
    const stockObj = ref({
        title: '',
        id: '',
        type: ''
    })

    /**
     * @description: 新增/编辑/审核弹框
     * @return {*}
     */
    const addStockVisiable = ref<boolean>(false)
    const delateStockVisiable = ref<boolean>(false)
    const detailStockVisiable = ref<boolean>(false)

    const handleShow = (val: any, type = 'add'): void => {
        const { id } = val || {}
        switch (type) {
            case 'add':
                stockObj.value = {
                    title: '新增',
                    id: '',
                    type: 'add'
                }
                break
            case 'edit':
                stockObj.value = {
                    title: '修改',
                    id,
                    type: 'edit'
                }
                break
            case 'audt':
                stockObj.value = {
                    title: '审核',
                    id,
                    type: 'audt'
                }
                break
            default:
                break
        }
        addStockVisiable.value = true
    }

    /**
     * @description: 基于条件导出
     * @return {*}
     */
    const handleExport = () => {
        const { calDt, activeDt, constObj, formerConstObj, relatedConsObj, ...paramsOther } =
            queryForm.value ?? {}
        const { startDate: calStartDt, endDate: calEndDt } = calDt ?? {}
        const { startDate: activeStartDt, endDate: activeEndDt } = activeDt ?? {}
        const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj ?? {}
        const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj ?? {}
        const { orgCode: relatedOrgCode, consCode: relatedConsCode } = relatedConsObj
        downloadFile(
            {
                url: '/performance/stocksplitconfig/exportconfig',
                method: 'post',
                loadingParams: {
                    isCust: true
                },
                data: {
                    ...paramsOther,
                    newlyOrgCode,
                    newlyConsCode,
                    formerOrgCode,
                    formerConsCode,
                    calStartDt,
                    calEndDt,
                    activeStartDt,
                    activeEndDt,
                    relatedOrgCode,
                    relatedConsCode
                }
            },
            (val: string) => true,
            (val: string) => {
                // 成功回调
                message({
                    type: 'success',
                    message: val || '导出成功'
                })
            }
        )
    }

    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleDelate = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            id,
            title: '删除',
            type: 'delate'
        }
        delateStockVisiable.value = true
    }

    /**
     * @description: 详情
     * @return {*}
     */
    const handleDetail = (val: any, type: string) => {
        const { id } = val || {}
        switch (type) {
            case 'operateDetail':
                stockObj.value = {
                    title: '操作详情',
                    id,
                    type
                }
                break
            case 'customDetail':
            default:
                stockObj.value = {
                    title: '客户详情',
                    id,
                    type
                }
                break
        }
        detailStockVisiable.value = true
    }

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    /**
     * @description 提示
     */
    onMounted(() => {
        getPageInit()
        // 获取当前页面权限
        // getMenuRoles({ menuCode })
    })

    // import { usePermission } from '@/stores/permission'
    // import { useRoute } from 'vue-router'
    // 页面权限控制
    // const permissionStore = usePermission()
    // const { getMenuRoles, isPremission } = permissionStore
    // router
    // const route = useRoute()
    // const { menuCode }: any = route.query || {}

    // /**
    //  * @description: 分页数据
    //  * @return {*}
    //  */
    // class PageObj {
    //     page = 1
    //     size = 100
    //     total = 0
    //     perPage = 1
    // }
    // const pageObj = ref(new PageObj())
    // const queryForm = ref(new QueryForm())

    // /**
    //  * @description: 列表请求
    //  * @return {*}
    //  */
    // const tableList = ref([])
    // const queryList = () => {
    //     // 列表请求
    //     // 拆分日期枚举
    //     const { calDt, activeDt, constObj, formerConstObj } = queryForm.value || {}
    //     const { startDate: calStartDt, endDate: calEndDt } = calDt
    //     const { startDate: activeStartDt, endDate: activeEndDt } = activeDt
    //     const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj
    //     const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj
    //     fetchRes(
    //         queryConfigList({
    //             ...pageObj.value,
    //             ...queryForm.value,
    //             newlyOrgCode,
    //             newlyConsCode,
    //             formerOrgCode,
    //             formerConsCode,
    //             calStartDt,
    //             calEndDt,
    //             activeStartDt,
    //             activeEndDt
    //         }),
    //         {
    //             successCB: (res: any) => {
    //                 // 权限请求成功
    //                 const { rows, page, total, size } = res || {}
    //                 tableList.value = rows
    //                 pageObj.value.page = Number(page)
    //                 pageObj.value.total = Number(total)
    //                 pageObj.value.size = Number(size)
    //             },
    //             successTxt: '',
    //             failTxt: '请求失败请重试！',
    //             fetchKey: ''
    //         }
    //     )
    // }

    // /**
    //  * @description: 分页联动
    //  * @param {*} current
    //  * @param {*} perPage
    //  * @return {*}
    //  */
    // const handleCurrentChange = (current: number, perPage: number) => {
    //     pageObj.value = { ...pageObj.value, page: current, size: perPage }
    //     queryList()
    // }
    // /**
    //  * @description 重置
    //  * @param resetQueryForm 重置
    //  */
    // const resetQueryForm = () => {
    //     Object.assign(pageObj.value, new PageObj())
    //     Object.assign(queryForm.value, new QueryForm())
    //     // 组件重置
    //     newlySelect.value.resetSelect()
    //     formerSelect.value.resetSelect()
    // }

    // /**
    //  * @description: 初始化,权限控制
    //  * @return {*}
    //  */
    // const fetchUserPermissions = (): void => {
    //     // 权限
    //     fetchRes(
    //         initList({
    //             hasOperate: '1',
    //             hasOrgTree: '1'
    //         }),
    //         {
    //             successCB: (res: any) => {
    //                 const { operateList, organizationList, consultList } = res || {}
    //                 opreateList.value = operateList
    //                 // 获取组织架构数据
    //                 const organizationListTpl = makeElementTree({
    //                     pid: '',
    //                     list: organizationList,
    //                     pidFiled: 'parentOrgCode',
    //                     labelFiled: 'orgName',
    //                     valueFiled: 'orgCode'
    //                 })
    //                 // 将数据存储到pinia
    //                 setCommonData({
    //                     operateList,
    //                     organizationList: organizationListTpl,
    //                     consultList
    //                 })
    //             },
    //             errorCB: () => {
    //                 opreateList.value = []
    //             },
    //             catchCB: () => {
    //                 opreateList.value = []
    //             },
    //             successTxt: '',
    //             failTxt: '请求失败请重试！',
    //             fetchKey: ''
    //         }
    //     )
    // }
</script>
<style lang="less" scoped></style>
