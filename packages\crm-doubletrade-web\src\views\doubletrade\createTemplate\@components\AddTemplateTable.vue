<template>
    <div>
        <el-card shadow="never">
            <div class="hint-word">示例：您的姓名是${custname}，证件号码是${custno}。</div>
            <div class="hint-word">
                <el-button
                    v-if="propsData.type === 'add' || propsData.type === 'update'"
                    type="primary"
                    :icon="Plus"
                    size="small"
                    @click="addRows()"
                >
                    新增
                </el-button>
            </div>
            <el-table :data="questionList" border class="table-border">
                <el-table-column label="序号" width="100">
                    <template #default="scope">
                        <span>{{ scope.$index + 1 }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="问题">
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.question"
                            autosize
                            resize="none"
                            type="textarea"
                            :disabled="readonly"
                            @change="handlechange"
                        />
                    </template>
                </el-table-column>

                <el-table-column label="答案">
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.answer"
                            autosize
                            resize="none"
                            type="textarea"
                            :disabled="readonly"
                            @change="handlechange"
                        />
                    </template>
                </el-table-column>

                <el-table-column v-if="propsData.type === 'update'" label="审核结果" width="200">
                    <template #default="scope">
                        <el-text
                            v-if="scope.row.checkflag === '1'"
                            class="mx-1"
                            size="large"
                            type="warning"
                        >
                            未审核
                        </el-text>
                        <el-text
                            v-if="scope.row.checkflag === '2'"
                            class="mx-1"
                            size="large"
                            type="success"
                        >
                            通过
                        </el-text>
                        <el-text
                            v-else-if="scope.row.checkflag === '3'"
                            class="mx-1"
                            size="large"
                            type="danger"
                        >
                            不通过
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column v-if="propsData.type === 'check'" label="审核结果" width="300">
                    <template #default="scope">
                        <CrmRadio v-model="scope.row.checkflag" :option-list="checkList">
                            {{ scope.row.checkflag }}
                        </CrmRadio>
                    </template>
                </el-table-column>

                <el-table-column v-if="propsData.type === 'update'" label="审核原因">
                    <template #default="scope">
                        {{ scope.row.checkreason }}
                    </template>
                </el-table-column>
                <el-table-column v-if="propsData.type === 'check'" label="审核原因">
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.checkreason"
                            autosize
                            resize="none"
                            type="textarea"
                        />
                    </template>
                </el-table-column>

                <el-table-column
                    v-if="propsData.type === 'add' || propsData.type === 'update'"
                    fixed="right"
                    label="操作"
                    width="350"
                >
                    <template #default="scope">
                        <el-button
                            v-if="scope.$index !== 0"
                            link
                            type="primary"
                            size="small"
                            @click="upRows($event, scope.rows, scope.$index)"
                            >上移</el-button
                        >
                        <el-button
                            v-if="scope.$index !== questionList.length - 1"
                            link
                            type="primary"
                            size="small"
                            @click="downRows($event, scope.rows, scope.$index)"
                            >下移</el-button
                        >
                        <el-button
                            link
                            type="primary"
                            size="small"
                            @click="deleteRows($event, scope.rows, scope.$index)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>

<script setup lang="ts">
    import { Plus } from '@element-plus/icons-vue'
    // eslint-disable-next-line vue/require-prop-types
    const props = defineProps(['propsData'])
    const questionList = ref<any>([])
    const readonly = computed(() => {
        return props.propsData.type === 'show' || props.propsData.type === 'check'
    })
    const checkList = [
        {
            label: '通过',
            value: '2'
        },
        {
            label: '驳回',
            value: '3'
        }
    ]

    // 具有序号的questionList
    const questionListHasOrder = computed(() => {
        questionList.value.map((item: any, index: number) => {
            item.order = index + 1
        })
        return questionList.value
    })

    function handlechange(e: any) {
        console.log('%c Line:38 🍢 e', 'color:#4fff4B', e)
        console.log('%c Line:40 🍣 questionList.value', 'color:#fca650', questionList.value)
    }

    // 新增行
    function addRows() {
        const newobj = {
            question: '',
            answer: ''
        }
        questionList.value = [...questionList.value, newobj]
    }

    // 上移行
    function upRows(e: { stopPropagation: () => void }, item: any, index: number) {
        e.stopPropagation()
        // 改变 questionList.value的顺序，让当前向上移动一位
        const temp = questionList.value[index - 1]
        questionList.value[index - 1] = questionList.value[index]
        questionList.value[index] = temp
    }

    // 下移行
    function downRows(e: { stopPropagation: () => void }, item: any, index: number) {
        // 改变 questionList.value的顺序，让当前向下移动一位
        e.stopPropagation()
        const temp = questionList.value[index + 1]
        questionList.value[index + 1] = questionList.value[index]
        questionList.value[index] = temp
    }

    // 删除行
    function deleteRows(e: { stopPropagation: () => void }, item: any, index: any) {
        e.stopPropagation()
        questionList.value.splice(index, 1)
    }

    defineExpose({
        questionList: questionListHasOrder
    })
</script>

<style scoped>
    .hint-word {
        margin: 0 0 10px 0;
    }
    .table-border {
        border-top: 1px solid #dcdfe6;
    }
</style>
