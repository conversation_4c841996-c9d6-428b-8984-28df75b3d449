<!-- table顶部操作按钮-导入 -->
<template>
    <crm-dialog
        v-model="chooseFileVisible"
        title="导入文件"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="onClose"
    >
        <div>
            <el-upload
                ref="uploadFiles"
                class="upload-demo"
                :name="fileKey"
                :data="paramsData"
                :action="actionUploadUrl"
                :multiple="false"
                :show-file-list="true"
                :on-success="handleSuccess"
                :on-error="handleError"
                :before-upload="beforeUpload"
                :with-credentials="true"
                :accept="accectType"
                :headers="{ Authorization: getLocalItem('hb_crm_token') }"
            >
                <crm-button size="small" type="primary" :loading="importExcelLoading"
                    >导入文件</crm-button
                >
            </el-upload>
            <div v-for="(item, index) in tipText" :key="index" style="margin-top: 10px">
                {{ item.name }} --
                <span :style="computedRedColor(item.desc)">{{ item.desc }}</span>
                <span>{{ item.type }}</span>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <crm-button type="primary" plain size="small" @click="onClose">关 闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script>
    import { UploadFilled, Upload, ArrowDown } from '@element-plus/icons-vue'
    import { getLocalItem, message, messageBox } from '@common/utils/index'
    import { responseCode } from '@/constant/index'
    export default defineComponent({
        name: 'CrmInportFile',
        model: {
            prop: 'show',
            event: 'change'
        },
        props: {
            show: {
                type: Boolean,
                default: false
            },
            fileKey: {
                //文件传输字段
                type: String,
                default: 'multipartFile'
            },
            paramsData: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            uploadUrl: {
                type: String,
                required: true
            },
            type: {
                type: String,
                default: ''
            },
            accectType: {
                type: String,
                default: '.xls,.xlsx'
            },
            title: {
                type: String,
                default: '导入'
            },
            logInfo: {
                type: Object,
                default: null
            }
        },
        emits: ['update:modelValue', 'callBack'],
        setup() {
            return {
                UploadFilled,
                Upload,
                ArrowDown
            }
        },
        data() {
            return {
                chooseFileVisible: false,
                tipText: [],
                importExcelLoading: false
            }
        },
        computed: {
            actionUploadUrl() {
                return window._msApiPrefix + this.uploadUrl
                // el测试
                // return 'https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15'
            },
            computedRedColor(item) {
                return function (item) {
                    if (item?.includes('失败') || item?.includes('错误')) {
                        return 'color: #c82d30;'
                    } else if (item?.includes('成功')) {
                        return 'color: #018800;'
                    }
                }
            }
            // paramsData() {
            //     return {
            //         type: this.moduleName
            //     }
            // }
        },
        watch: {
            show(newValue, oldValue) {
                this.chooseFileVisible = newValue
            }
        },
        methods: {
            getLocalItem,
            onClose() {
                this.$emit('update:modelValue', false)
                this.tipText = []
                this.$refs.uploadFiles.clearFiles()
            },
            beforeUpload(file) {
                this.tipText = []
                this.importExcelLoading = true
                if (file !== '') {
                    const isExcel = file.name.substring(
                        file.name.lastIndexOf('.'),
                        file.name.length
                    )
                    const isValid = isExcel === '.xls' || isExcel === '.xlsx'
                    if (!isValid) {
                        message({
                            type: 'error',
                            message: '上传文件只能是xls或xlsx格式!'
                        })
                    }
                    return isValid
                }
            },
            handleSuccess(response, file, fileList) {
                if (response !== '' && file !== '') {
                    const { code, data, description } = response || {}
                    const { verifyCode, verifyDesc, description: des } = data || {}
                    switch (verifyCode) {
                        case responseCode.SUCCESS:
                        case responseCode.CRM_SUCCESS:
                            this.tipText.push({
                                name: file.name,
                                type: verifyDesc || ' 导入成功~~~',
                                desc: des || ''
                            })
                            break
                        case responseCode.SYS_ERROR:
                        case responseCode.CRM_SYS_FILED:
                        case '0001':
                        case 'C010001':
                        default:
                            this.tipText.push({
                                name: file.name,
                                type: verifyDesc || ' 导入失败，请重试~~~',
                                desc: des || ''
                            })
                            break
                    }
                    this.importExcelLoading = false
                    if (this.logInfo) {
                        const { businessKey, operationKey, detail, ...logParms } = this.logInfo
                        this.$OperationLogs.addLogFromButton(
                            businessKey,
                            operationKey,
                            detail,
                            logParms
                        )
                    }
                    this.$refs.uploadFiles.clearFiles()
                    this.$emit('callBack', { ...file, ...data })
                    // if (!this.type) this.$OperationLogs.addFromKey('lhjhc', 'import') // 导入机会池的时候才记录日志
                } else {
                    message({
                        type: 'error',
                        message: '上传文件失败~'
                    })
                    this.importExcelLoading = false
                }
            },
            handleError(response, file, fileList) {
                if (response !== '') {
                    message({
                        type: 'error',
                        message: '上传文件失败~'
                    })
                    this.importExcelLoading = false
                } else {
                    this.importExcelLoading = false
                }
            }
        }
    })
</script>
<style lang="less"></style>
