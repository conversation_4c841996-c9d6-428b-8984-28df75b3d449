import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    QueryBeisenOrgConfigReq,
    InsertBeisenOrgConfigReq,
    UpdateBeisenOrgConfigReq,
    BeisenOrgConfigIdReq
} from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigQuery = (params: QueryBeisenOrgConfigReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/query',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 导出接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigExport = (params: QueryBeisenOrgConfigReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 新增接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigInsert = (params: InsertBeisenOrgConfigReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 修改接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigUpdate = (params: UpdateBeisenOrgConfigReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/update',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 删除接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigDelete = (params: BeisenOrgConfigIdReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询单个接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenOrgConfigDetail = (params: BeisenOrgConfigIdReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/detail',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 查询单个接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const queryBeisenOrg = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenorgconfig/querybeisenorg',
            method: 'post'
        })
    )
}
