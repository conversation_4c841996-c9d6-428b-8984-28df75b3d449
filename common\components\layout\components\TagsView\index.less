.tags-view-container {
    width: 100%;
    height: @tags_view_bar_height;
    padding: 4px 8px;
    overflow: hidden;
    background: #ffffff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    ::-webkit-scrollbar,
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-thumb {
        display: none;
    }

    .el-scrollbar {
        height: 40px;
    }

    .scrollbar-content {
        box-sizing: border-box;
        display: flex;

        .scrollbar-item {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .tag-item {
            position: relative;
            display: inline-flex;
            align-items: center;
            height: 26px;
            padding: 0 6px 0 10px;
            margin: 0 3px;
            font-size: 13px;
            line-height: 26px;
            color: @font_color_05;
            cursor: pointer;
            background: #ffffff;
            border: 1px solid #d8dce5;

            a {
                display: inline-block;
                width: 100%;
                height: 100%;
                color: @font_color_05;
            }

            &.active {
                font-weight: bold;
                color: @theme_main;
                background-color: #ffeeee;
                border-color: #d9b3b3;

                &:hover {
                    background-color: #ffe5e5;
                }

                .icon-close {
                    &:hover {
                        color: #c82e30;
                        background-color: #ebafb0;
                    }
                }
            }

            .icon-close {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 15px;
                height: 15px;
                margin-left: 4px;
                font-size: 12px;
                text-align: center;
                vertical-align: middle;
                border-radius: 50%;
                transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                transform-origin: 100% 50%;

                &:hover {
                    color: #ffffff;
                    background-color: #b4bccc;
                }
            }
        }
    }

    .contextmenu {
        position: absolute;
        z-index: 3000;
        padding: 5px 0;
        margin: 0;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        list-style-type: none;
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

        li {
            padding: 7px 16px;
            margin: 0;
            cursor: pointer;

            &:hover {
                background: #eeeeee;
            }
        }
    }
}
