/*
 * @Description: 定义搜索的label列表
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    tempName: {
        label: '模版名称',
        placeholder: '输入名称名称'
    },
    custType: {
        label: '客户类型',
        placeholder: '请选择客户类型',
        selectList: [
            {
                key: '',
                label: '全部'
            },
            {
                key: '0',
                label: '机构'
            },
            {
                key: '1',
                label: '个人'
            }
        ]
    },
    preType: {
        label: '成单方式',
        placeholder: '请选择成单方式',
        selectList: [
            {
                key: '',
                label: '全部'
            },
            {
                key: '1',
                label: '纸质成单'
            },
            {
                key: '2',
                label: '电子成单'
            }
        ]
    },
    tempType: {
        label: '模版适用类型',
        placeholder: '请选择模版适用类型',
        selectList: [
            {
                key: '',
                label: '全部'
            },
            {
                key: '1',
                label: '指定产品'
            },
            {
                key: '2',
                label: '指定条件'
            }
        ]
    },
    fundCode: {
        label: '产品名称',
        placeholder: '请输入产品名称'
    },
    checkflag: {
        label: '审核状态',
        placeholder: '请选择审核状态',
        selectList: [
            {
                key: '',
                label: '全部'
            },
            {
                key: '1',
                label: '待审核'
            },
            {
                key: '2',
                label: '审核通过'
            },
            {
                key: '3',
                label: '审核不通过'
            }
        ]
    },
    templateTime: {
        label: '生效时间',
        placeholder: ['生效时间', '失效时间']
    },
    tabsPanelList: [{ label: '双录配置模版', name: 'template' }],
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    },
    chromeLink: {
        url: `https://dl.google.com/tag/s/appguid%3D%7B8A69D345-D564-463C-AFF1-A69D9E530F96%7D%26iid%3D%7B77CCCA8B-F59C-E570-E701-E2575690F29C%7D%26lang%3Dzh-CN%26browser%3D3%26usagestats%3D0%26appname%3DGoogle%2520Chrome%26needsadmin%3Dprefers%26ap%3Dx64-stable-statsdef_1%26installdataindex%3Dempty/chrome/install/ChromeStandaloneSetup64.exe`
    }
}
