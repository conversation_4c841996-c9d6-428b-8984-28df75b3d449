<!--
 * @Description: 雷达图
 * @Author: chaohui.wu
 * @Date: 2023-04-03 16:04:41
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-25 14:19:48
 * @FilePath: /crm-rpo-template/common/components/charts/RadarChart.vue
 *  
-->

<template>
    <chart-wrapper :width="width" :height="height" :options="chartOptions" v-bind="$attrs" />
</template>
<script setup lang="ts">
    import { ref, computed, watch } from 'vue'
    import { deepMergeObj } from './scripts/methods'
    import { radarChartConfig } from './scripts/chartOptions.js'
    // import customChart from './scripts/echartsConfig'
    import { getEnumber } from './scripts/labelData'

    const props = withDefaults(
        defineProps<{
            width?: string
            height?: string
            chartData?: any[]
        }>(),
        {
            width: '100%',
            height: '300px',
            chartData: () => {
                return []
            }
        }
    )

    const splitNumber = 5
    // 数据转换
    const curData = computed(() => {
        return props.chartData.map(item => {
            const { title, key, maxScore, level, score } = item
            return {
                name: key,
                min: 0,
                max: maxScore * splitNumber || 3 * splitNumber,
                level,
                title
            }
        })
    })
    const dataList = computed(() => {
        return props.chartData.map(item => {
            const { score } = item
            return score * splitNumber
        })
    })

    const chartOptions = ref<any>({})

    const getChartOptions = () => {
        if (props.chartData && props.chartData.length > 0) {
            chartOptions.value = deepMergeObj(radarChartConfig, {
                legend: {
                    show: false
                },
                tooltip: {
                    show: false
                },
                radar: {
                    splitNumber,
                    center: ['50%', '60%'],
                    // indicator: [
                    //     { name: '人生阶段', min: 0, max: 15 },
                    //     { name: '投资经验', min: 0, max: 15 },
                    //     { name: '财富水平', min: 0, max: 15 }
                    // ],
                    indicator: curData,
                    axisName: {
                        padding: [5, 0, 0, 0],
                        formatter: (value: any, indicator: any) => {
                            const valObj = curData.value?.filter(item => item.name === value)
                            const item = valObj[0]
                            if (value === '1') {
                                indicator.nameTextStyle.verticalAlign = 'bottom'
                                return [
                                    `{${item.level <= 2 ? 'a' : 'c'}|${getEnumber(
                                        'humanMap',
                                        item.level
                                    )}}`,
                                    `{b|${item.title}}`
                                ].join('\n')
                            } else if (value === '2') {
                                indicator.nameTextStyle.verticalAlign = 'top'
                                return [
                                    `{${item.level > 2 ? 'a' : 'c'}|${getEnumber(
                                        'exprenceMap',
                                        item.level
                                    )}}`,
                                    `{b|${item.title}}`
                                ].join('\n')
                            } else if (value === '3') {
                                indicator.nameTextStyle.verticalAlign = 'top'
                                return [
                                    `{${item.level > 2 ? 'a' : 'c'}|${getEnumber(
                                        'healthMap',
                                        item.level
                                    )}}`,
                                    `{b|${item.title}}`
                                ].join('\n')
                            }
                        },
                        align: 'center',
                        rich: {
                            a: {
                                color: '#D20000',
                                backgroundColor: '#ffdfdf',
                                borderRadius: 13,
                                fontSize: 14,
                                padding: [4, 15],
                                fontFamily: 'Microsoft YaHei,微软雅黑',
                                fontWeight: 'normal'
                            },
                            b: {
                                color: '#333',
                                fontWeight: '600',
                                fontFamily: 'Microsoft YaHei-bold,  微软雅黑',
                                padding: [10, 0, 2, 0],
                                verticalAlign: 'bottom',
                                fontSize: 15
                            },
                            c: {
                                color: '#ffffff',
                                backgroundColor: '#c2c2c2',
                                borderRadius: 13,
                                fontSize: 14,
                                padding: [4, 15],
                                fontFamily: 'Microsoft YaHei,微软雅黑',
                                fontWeight: 'normal'
                            }
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            // type: 'dashed',
                            type: [2, 3],
                            color: '#D0021B',
                            opacity: 0.4
                            // dashOffset:5
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(208, 2, 27, 0.02)'
                        }
                    },
                    splitArea: {
                        areaStyle: {
                            color: [
                                'rgba(208, 2, 27, 0.30)',
                                'rgba(208, 2, 27, 0.25)',
                                'rgba(208, 2, 27, 0.08)',
                                'rgba(208, 2, 27, 0.02)',
                                'rgba(208, 2, 27, 0.01)'
                            ]
                        }
                    }
                },
                series: [
                    {
                        type: 'radar',
                        data: [
                            {
                                value: dataList
                            }
                        ],
                        itemStyle: {
                            color: '#FF001F'
                        },
                        lineStyle: {
                            color: 'transparent',
                            opacity: 0.25
                        },
                        areaStyle: {
                            color: '#D0021B',
                            opacity: 0.35
                            // color: new customChart.graphic.RadialGradient(0.1, 0.6, 1, [
                            //     {
                            //         color: 'rgba(208, 2, 27, 0.25)',
                            //         offset: 0
                            //     },
                            //     {
                            //         color: 'rgba(208, 2, 27, 0.9)',
                            //         offset: 1
                            //     }
                            // ])
                        }
                    }
                ]
            })
        }
        return chartOptions
    }

    watch(
        [() => props.chartData],
        (newVal, oldVal) => {
            if (newVal) {
                getChartOptions()
            } else {
                chartOptions.value = []
            }
        },
        {
            immediate: true
            // deep: true
        }
    )
</script>
<style lang="less">
    @import './styles/index';
</style>
