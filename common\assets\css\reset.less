/*
 * @Description: 初始化reset
 * @Author: chao<PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-08 13:51:28
 * @FilePath: /crm-web/common/assets/css/reset.less
 */
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    touch-action: none;

    ::-webkit-scrollbar {
        width: 4px;
        height: 8px;
        background-color: #f5f5f5;
    }

    ::-webkit-scrollbar-track {
        background-color: #f5f5f5;
        border-radius: 4px;
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.16);
    }

    ::-webkit-scrollbar-thumb {
        cursor: pointer;
        background-color: #d3dce6;
        border-radius: 4px;
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.16);

        &:hover {
            background-color: #878ba5;
        }
    }
}

html,
div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
em,
img,
strong,
dl,
dt,
dd,
ol,
ul,
li,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
footer,
header,
menu,
nav,
section,
audio,
video,
input {
    padding: 0;
    margin: 0;
    vertical-align: baseline;
    border: none;
    outline: 0;
}

html,
form,
fieldset,
p,
div,
h1,
h2,
h3,
h4,
h5,
h6 {
    -webkit-text-size-adjust: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote::before,
blockquote::after,
q::before,
q::after {
    content: '';
    content: none;
}

del {
    text-decoration: line-through;
}

table {
    border-spacing: 0;
    border-collapse: collapse;
}

a {
    color: #333333;
    text-decoration: none;
    outline: none;
}

input[type='submit'],
input[type='button'],
input[type='text'],
textarea,
input[type='password'],
input[type='tel'] {
    appearance: none;
}

input[type='text'],
input[type='password'],
input[type='tel'],
input[type='number'] {
    line-height: normal;
}

select {
    appearance: none;
    background: transparent;
    border: 0;
}

// ::input-placeholder {
//     color: #cccccc;
// }

::placeholder {
    color: #cccccc;
}

button {
    font: inherit;
}

body {
    padding: 0;
    margin: 0;
    font-family: 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB',
        Arial, sans-serif;
    vertical-align: baseline;
    border: none;
    outline: 0;
    -webkit-text-size-adjust: none;
}
