/*
 * @Description: 项目权限接口
 * @Author: chao<PERSON>.wu
 * @Date: 2023-09-07 11:18:05
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-07 12:28:43
 * @FilePath: /crm-web/common/api/crmBase/userPermission.ts
 *
 */
import { axiosRequest } from '../../utils/index'
import { paramsMerge } from '../mock'

/**
 * @description: 查菜单下用户拥有的操作权限列表
 * @return {*}
 */
export const fetchAuth = (params: any) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/pageauth/fetchauth',
            method: 'post',
            data: params
        })
    )
}
