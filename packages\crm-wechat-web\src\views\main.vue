<!--
 * @Description: 首页
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 11:25:40
 * @FilePath: /crm-rpo-template/packages/crm-template/src/views/main.vue
 *  
-->
<template>
    <!-- <component :is="componentList[activeName]" :class="activeClassName"></component> -->
    <BlankView :class="activeClassName"></BlankView>
    <!-- <Layout :class="activeClassName"></Layout> -->
</template>

<script setup lang="ts">
    const activeName = ref<string>('BlankView')
    const activeClassName = computed(() => {
        if (activeName.value === 'BlankView') {
            document.querySelector('.crm-web-app')?.classList.add('blank-view-box')
        } else {
            document.querySelector('.crm-web-app')?.classList.remove('blank-view-box')
        }
        return activeName.value === 'BlankView' ? 'blank-view-container' : ''
    })
</script>

<style lang="less" scoped>
    .demo {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .page-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-size: 14px;
        line-height: 20px;
        color: @font_red_light;
    }

    .logo {
        height: 120px;
        padding: 30px;
        will-change: filter;

        &:hover {
            filter: drop-shadow(0 0 2em #646cffaa);
        }

        &.vue:hover {
            filter: drop-shadow(0 0 2em #42b883aa);
        }
    }

    .blank-view-container {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
</style>
