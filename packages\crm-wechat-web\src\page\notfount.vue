<!--
 * @Description: 404 页面展示
 * @Author: chaohui.wu
 * @Date: 2023-06-29 11:29:55
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-05 14:06:23
 * @FilePath: /crm-template/src/page/notfount.vue
 *  
-->
<template>
    <div class="not-fund-module">
        <div class="wscn-http404">
            <div class="pic-404">
                <img class="pic-404__parent" src="../assets/images/404.png" alt="404" />
            </div>
            <div class="bullshit">
                <div class="bullshit__oops">出错了!</div>
                <div class="bullshit__headline">访问地址错误或没有权限！</div>
                <div class="bullshit__info">请检查访问地址或点击返回首页重试</div>
                <a class="bullshit__return-home" @click="backPrevPage">返回</a>
                <a href="" class="bullshit__return-home">回到首页</a>
                <!-- <div class="bullshit__return-home relogin">重新登录</div> -->
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    /**
     * @description: 返回上一页
     * @return {*}
     */
    const backPrevPage = () => {
        window.history.back()
    }
</script>

<style scoped lang="less">
    .not-fund-module {
        width: 100%;
        height: 100vh;
    }

    .blank-view-box {
        .not-fund-module {
            width: 100%;
            height: 100vh;
        }
    }

    .wscn-http404 {
        position: absolute;
        top: 40%;
        left: 50%;
        width: 1200px;
        padding: 0 50px;
        overflow: hidden;
        transform: translate(-50%, -50%);

        .pic-404 {
            position: relative;
            float: left;
            width: 600px;
            overflow: hidden;

            &__parent {
                width: 100%;
            }

            &__child {
                position: absolute;

                &.left {
                    top: 17px;
                    left: 220px;
                    width: 80px;
                    opacity: 0;
                    animation-name: cloudLeft;
                    animation-duration: 2s;
                    animation-timing-function: linear;
                    animation-delay: 1s;
                    animation-fill-mode: forwards;
                }

                &.mid {
                    top: 10px;
                    left: 420px;
                    width: 46px;
                    opacity: 0;
                    animation-name: cloudMid;
                    animation-duration: 2s;
                    animation-timing-function: linear;
                    animation-delay: 1.2s;
                    animation-fill-mode: forwards;
                }

                &.right {
                    top: 100px;
                    left: 500px;
                    width: 62px;
                    opacity: 0;
                    animation-name: cloudRight;
                    animation-duration: 2s;
                    animation-timing-function: linear;
                    animation-delay: 1s;
                    animation-fill-mode: forwards;
                }

                @keyframes cloudLeft {
                    0% {
                        top: 17px;
                        left: 220px;
                        opacity: 0;
                    }

                    20% {
                        top: 33px;
                        left: 188px;
                        opacity: 1;
                    }

                    80% {
                        top: 81px;
                        left: 92px;
                        opacity: 1;
                    }

                    100% {
                        top: 97px;
                        left: 60px;
                        opacity: 0;
                    }
                }

                @keyframes cloudMid {
                    0% {
                        top: 10px;
                        left: 420px;
                        opacity: 0;
                    }

                    20% {
                        top: 40px;
                        left: 360px;
                        opacity: 1;
                    }

                    70% {
                        top: 130px;
                        left: 180px;
                        opacity: 1;
                    }

                    100% {
                        top: 160px;
                        left: 120px;
                        opacity: 0;
                    }
                }

                @keyframes cloudRight {
                    0% {
                        top: 100px;
                        left: 500px;
                        opacity: 0;
                    }

                    20% {
                        top: 120px;
                        left: 460px;
                        opacity: 1;
                    }

                    80% {
                        top: 180px;
                        left: 340px;
                        opacity: 1;
                    }

                    100% {
                        top: 200px;
                        left: 300px;
                        opacity: 0;
                    }
                }
            }
        }

        .bullshit {
            position: relative;
            float: left;
            width: 400px;
            padding: 30px 0;
            overflow: hidden;

            &__oops {
                margin-bottom: 20px;
                font-size: 32px;
                font-weight: bold;
                line-height: 40px;
                color: #1482f0;
                opacity: 0;
                animation-name: slideUp;
                animation-duration: 0.5s;
                animation-fill-mode: forwards;
            }

            &__headline {
                margin-bottom: 10px;
                font-size: 20px;
                font-weight: bold;
                line-height: 24px;
                color: #222222;
                opacity: 0;
                animation-name: slideUp;
                animation-duration: 0.5s;
                animation-delay: 0.1s;
                animation-fill-mode: forwards;
            }

            &__info {
                margin-bottom: 30px;
                font-size: 13px;
                line-height: 21px;
                color: grey;
                opacity: 0;
                animation-name: slideUp;
                animation-duration: 0.5s;
                animation-delay: 0.2s;
                animation-fill-mode: forwards;
            }

            &__return-home {
                display: block;
                float: left;
                width: 110px;
                height: 36px;
                margin-right: 10px;
                font-size: 14px;
                line-height: 36px;
                color: #ffffff;
                text-align: center;
                cursor: pointer;
                background: #1482f0;
                border-radius: 100px;
                opacity: 0;
                animation-name: slideUp;
                animation-duration: 0.5s;
                animation-delay: 0.3s;
                animation-fill-mode: forwards;

                &.relogin {
                    background-color: darkgrey;
                }
            }

            @keyframes slideUp {
                0% {
                    opacity: 0;
                    transform: translateY(60px);
                }

                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }
    }
</style>
