export {}
declare module './apiReqType' {
    /**
     * 查询客户拜访纪要列表请求参数
     */
    interface VisitMinutesListReq {
        /**
         * 所属机构
         */
        orgCode?: string

        /**
         * 所属投顾
         */
        consCode?: string

        /**
         * 拜访日期开始 格式YYYYMMDD
         */
        visitDateStart?: string

        /**
         * 拜访日期结束 格式YYYYMMDD
         */
        visitDateEnd?: string

        /**
         * 创建日期开始 格式YYYYMMDD
         */
        createDateStart?: string

        /**
         * 创建日期结束 格式YYYYMMDD
         */
        createDateEnd?: string

        /**
         * 拜访目的列表，多选
         */
        visitPurpose?: string[]

        /**
         * 客户姓名，精确匹配
         */
        custName?: string

        /**
         * 投顾客户号，精确匹配
         */
        consCustNo?: string

        /**
         * 陪访人，精确匹配
         */
        accompanyingUser?: string

        /**
         * 上级主管，精确匹配
         */
        managerId?: string

        /**
         * 反馈情况，可选：陪访人未填、上级主管未填、陪访人已填、上级主管已填
         */
        feedbackStatus?: string[]

        /**
         * 分页页码，默认1
         */
        pageNo: number

        /**
         * 分页大小，可选100/200/500/1000/2000
         */
        pageSize: number
    }

    /**
     * 导出客户拜访纪要列表请求参数
     */
    interface ExportVisitMinutesReq {
        /**
         * 所属投顾
         */
        consId?: string

        /**
         * 拜访日期开始 格式YYYYMMDD
         */
        visitDateStart?: string

        /**
         * 拜访日期结束 格式YYYYMMDD
         */
        visitDateEnd?: string

        /**
         * 创建日期开始 格式YYYYMMDD
         */
        createDateStart?: string

        /**
         * 创建日期结束 格式YYYYMMDD
         */
        createDateEnd?: string

        /**
         * 拜访目的列表，多选
         */
        visitPurpose?: string[]

        /**
         * 客户姓名，精确匹配
         */
        custName?: string

        /**
         * 投顾客户号，精确匹配
         */
        consCustNo?: string

        /**
         * 陪访人，精确匹配
         */
        accompanyingUser?: string

        /**
         * 上级主管，精确匹配
         */
        managerId?: string

        /**
         * 反馈情况，可选：陪访人未填、上级主管未填、陪访人已填、上级主管已填
         */
        feedbackStatus?: string[]
    }
    // 保存反馈的请求参数接口
    interface ISaveFeedbackReq {
        visitMinutesId: string
        accompanyingId: string
        feedbackType: string
        summary: string
        suggestion: string
    }
    // 查询拜访反馈详情的请求参数接口
    interface IVisitFeedbackDetailReq {
        visitMinutesId: string
        feedbackType: string
    }
    // 查询用户列表的请求参数接口
    interface ISearchUserReq {
        searchParam: string
        searchType: string
    }
    // 修改主管的请求参数接口
    interface IUpdateManagerReq {
        visitMinutesIds: string[]
        isClear: boolean
        newUserId: string
    }

    export {
        VisitMinutesListReq,
        ExportVisitMinutesReq,
        ISaveFeedbackReq,
        IVisitFeedbackDetailReq,
        ISearchUserReq,
        IUpdateManagerReq
    }
}
