/*
 * @Description: 报告列表 ts类型定义
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:21:21
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:27:48
 * @FilePath: /crm-template/src/api/project/reportList/type/apiReqType.d.ts
 *
 */
export {}
declare module './apiReqType' {
    type ListAssetFixedPosition = {
        /**
         * 所属投顾
         */
        consCode?: string
        /**
         * 投顾客户号
         */
        conscustno?: string
        /**
         * 结束时间yyyymmdd
         */
        endDate?: string
        /**
         * 一账通号
         */
        hboneNo?: string
        /**
         * 手机号码
         */
        mobile?: string
        /**
         * 排序方式
         */
        order?: string
        /**
         * 所属部门
         */
        orgCode?: string
        /**
         * 页码
         */
        page: number
        /**
         * 每页显示多少条记录
         */
        size: number
        rows?: number
        /**
         * 排序字段
         */
        sort?: string
        /**
         * 开始时间yyyymmdd
         */
        startDate?: string
    }
    type ListAssetFixedpositionByUser = Omit<ListAssetFixedPosition, 'orgCode'>
    interface OperationParams {
        /**
         * 下载文件ID数组格式
         */
        assetIds: string[]
    }

    type CheckCustAuthTpl = Pick<
        ListAssetFixedPosition,
        'conscustno' | 'inputCustNo' | 'mobile' | 'hboneNo' | 'consCode'
    >
    type CheckCustAuthVo = Required<CheckCustAuthTpl>

    interface ListCustByConsCode {
        /**
         * 所属投顾
         */
        consCode: string
        /**
         * 输入数据-客户姓名模糊搜索
         */
        custName: string
        /**
         * 所属部门
         */
        orgCode: string
    }
    type ListConsCode = Pick<ListCustByConsCode, 'orgCode'>
    export {
        ListAssetFixedpositionByUser,
        ListAssetFixedPosition,
        OperationParams,
        CheckCustAuthVo,
        ListCustByConsCode,
        ListConsCode
    }
}
