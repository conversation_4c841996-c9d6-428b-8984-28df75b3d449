/*
 * @Description: loading 调取次数合并多次loading请求，避免重复请求
 * @Author: chaohui.wu
 * @Date: 2023-07-05 15:56:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-14 17:52:24
 * @FilePath: /crm-web/common/utils/loading.ts
 *
 */
import { ElLoading } from 'element-plus'
import { load } from './loadDom'

let loadingReqCount = 0
let loadingElement: any = undefined
/**
 * @description: 显示loading调用一次++一次
 * @param {any} target
 * @return {*}
 */
export const showLoading = (loadingParams: any) => {
    if (loadingReqCount === 0) {
        if (loadingParams?.isCust) {
            load.show()
        } else {
            loadingElement = ElLoading.service({ ...loadingParams })
        }
    }
    loadingReqCount++
}

/**
 * @description: 隐藏loading 并且记录请求次数 --
 * @return {*}
 */
export const hideLoading = () => {
    if (loadingReqCount <= 0) {
        return
    }
    loadingReqCount--
    if (loadingReqCount === 0) {
        load?.hide()
        loadingElement?.close()
    }
}
