<!--
* author: di.zong
* optionList select下拉选项列表
* labelFormat  选项展示的字段   默认label
* valueFormat  组件最终v-mode的值  默认value
* 自定义下拉框样式  可通过dropItem插槽实现
 -->
<template>
    <el-select
        :class="`crm_select ${(selectClass, custClass)}`"
        popper-class="crm_select_popper crm_select_popper_vue3"
        size="small"
        :style="`width: ${width};z-index: 20;`"
        :collapse-tags="$attrs.multiple"
        v-bind="$attrs"
    >
        <slot />
        <el-option
            v-for="item in optionList"
            :key="item[valueFormat]"
            :label="item[labelFormat]"
            :value="item[valueFormat]"
        >
            <slot name="dropItem" :scope="item" />
        </el-option>
    </el-select>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmSelect',
        props: {
            optionList: {
                type: Array,
                default: () => []
            },
            labelFormat: {
                type: String,
                default: 'label'
            },
            valueFormat: {
                type: String,
                default: 'value'
            },
            width: {
                type: String,
                default: '100%'
            },
            // 样式类型，可选默认、fund两种样式，fund为基金研究里面的样式
            styleType: {
                type: String,
                default: ''
            },
            custClass: {
                type: String,
                default: ''
            }
        },
        computed: {
            selectClass() {
                switch (this.styleType) {
                    case 'fund':
                        return 'crm_select_fund'
                    case 'tableHeader':
                        return 'table_select'
                    default:
                        return ''
                }
            }
        }
    })
</script>

<style lang="less" scoped>
    .crm_select {
        width: 100%;
        height: 24px;
        --el-select-input-focus-border-color: @border_focus;

        .select-trigger {
            width: 100%;
        }

        .el-input.is-focus {
            .el-input__inner {
                border-color: @border_focus;
            }
        }

        .el-input {
            height: 24px;
            font-size: 12px;

            .el-input__wrapper {
                padding: 1px 5px 1px 1px;
                border-radius: 2px;
            }

            .el-input__inner {
                height: 22px;
                padding: 0 8px;

                &:focus {
                    border-color: @border_focus;
                }

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-select__caret {
                line-height: 22px;
            }
        }

        .el-select__tags {
            height: 22px;
            padding-left: 4px;
            overflow: hidden auto;

            .el-select__input {
                height: 22px;
                font-size: 12px;
            }

            .el-tag {
                margin: 1px 6px 1px 0;
            }

            .el-select-tags-wrapper {
                display: contents;
            }

            .is-closable {
                .el-select__tags-text {
                    display: inline;
                }
            }
        }

        .is-disabled {
            .el-input__inner {
                color: @font_color;
            }
        }

        // 基金研究里面的下拉选择样式
        &.crm_select_fund {
            display: inline-flex;
            align-items: center;
            height: 20px;
            padding-left: 11px;
            margin: 0;
            line-height: 20px;
            // background: url(./filter_black.png) no-repeat left center / 7px auto;

            .el-input {
                width: 100%;
                height: 100%;
                line-height: inherit;

                .el-input__wrapper {
                    padding: 0;
                    background-color: transparent;
                    border-radius: 0;
                    box-shadow: none;

                    &:hover,
                    &:focus,
                    &.is-focus,
                    &.is-disabled {
                        background-color: transparent;
                        box-shadow: none;
                    }
                }

                &:hover,
                &:focus,
                &.is-disabled {
                    .el-input__inner {
                        background-color: transparent;
                        box-shadow: none;
                    }
                }

                .el-input__inner {
                    height: 20px;
                    padding: 0 0 0 4px;
                    font-size: 12px;
                    line-height: 20px;
                    color: @font_color_04;
                    text-align: left;
                    border-bottom: 1px solid #7078b8;

                    &::placeholder {
                        color: @font_color_04;
                    }

                    &:hover,
                    &:focus,
                    &.is-disabled {
                        box-shadow: none;
                    }
                }

                .el-input__suffix {
                    right: 0;

                    .el-input__suffix-inner {
                        > :first-child {
                            margin-left: 0;
                        }

                        .el-select__caret {
                            display: inline-flex;
                            align-items: center;
                            width: 12px;
                            font-size: 13px;
                            color: @font_color_04;
                        }
                    }
                }
            }
        }
        // 自定义table下拉选择
        &.table_select {
            :deep(.el-input) {
                --el-input-border-color: transparent;

                text-align: right;

                &__wrapper {
                    background-color: transparent;
                }

                &__inner {
                    padding-right: 12px;
                    text-align: center;

                    &:focus {
                        border-color: @border_focus;
                    }

                    &::placeholder {
                        font-size: 12px;
                        // font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                        // font-weight: bold;
                        color: @font_color;
                    }
                }

                .el-input__suffix {
                    right: 12px;
                }
            }
            // :deep(.el-input__wrapper) {
            //     background-color: transparent;
            // }
            // .el-input__inner {
            //     height: 22px;
            //     padding: 0 8px;

            //     &:focus {
            //         border-color: @border_focus;
            //     }

            //     &::placeholder {
            //         font-size: 12px;
            //     }
            // }
        }
    }

    .crm_select_popper {
        .el-select-dropdown__item {
            height: 30px;
            padding: 0 15px;
            font-size: 12px;
            line-height: 30px;
        }

        .el-select-dropdown__item.selected {
            color: @theme_main;
        }

        .el-popper__arrow {
            display: none;
        }
    }

    .crm_select_popper_vue3 {
        &.el-select-dropdown {
            position: relative;
            margin: 0;
            border: none;
        }
    }
</style>
<style lang="less">
    .el-select {
        --el-select-input-focus-border-color: @border_focus;
    }

    .el-select-dropdown__item {
        &.selected {
            color: @theme_main;
            background-color: @bg_main_selected;
        }

        &:not(.is-disabled):hover,
        &:not(.is-disabled).hover {
            color: @theme_main;
            background-color: @bg_main_hover;
        }
    }
</style>
