/*
 * @Description: 绩效管理-增量分成列表
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:16:10
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 18:30:06
 * @FilePath: /crm-web/packages/crm-performance-web/src/api/project/stockSplit/stockSplitList.ts
 *
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import {
    StockConfigParam,
    SaveConfigReq,
    CustInfoParam,
    TradeInfoReq,
    FundInfoReq
} from './type/apiType'

/**
 * @description: 查询列表
 * @return {*}
 */
export const queryConfigList = (params: StockConfigParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/queryconfiglist',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 香港交易单接口
 * @param params
 * @returns
 */
export const queryHktradeConfigList = (params: StockConfigParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/dtmsorder/hkproduct/querylist',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

export const getFundList = (params: FundInfoReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/dtmsproduct/paramget/queryfundlist',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 编辑-init
 * @param {object} params
 * @return {*}
 */
export const queryEdit = (params: { id: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/queryconfig',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.stock-form-module'
            }
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const queryHkTradeInfo = (params: TradeInfoReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/dtmsorder/hkproduct/querytradeinfo',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.stock-form-module'
            }
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const queryMobile = (params: { tradeId: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/dtmsorder/hkproduct/getmobile',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.stock-form-module'
            }
        })
    )
}

/**
 * @description: 编辑-保存
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const updateConfig = (params: SaveConfigReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/updateconfig',
            method: 'post',
            timeout: 30000,
            data: params
        })
    )
}

/**
 * @description: 新增-保存
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const addSave = (params: SaveConfigReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/saveconfig',
            method: 'post',
            data: params,
            timeout: 30000,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * @description: 审核-保存
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const audtiList = (params: { id: string; advice: string; auditPass: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/audticonfig',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 删除
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const deleteConfig = (params: { id: string; advice: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/deleteconfig',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 客户详情
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const custDetail = (params: { id: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/queryconfigdetailList',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.ext-invest-index-module',
                isCust: false
            }
        })
    )
}

/**
 * @description: 操作详情
 * @param {SaveConfigReq} params
 * @return {*}
 */
export const operateDetail = (params: { id: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/queryconfigoperateList',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.ext-invest-index-module',
                isCust: false
            }
        })
    )
}

/**
 * @description: 初始化
 * @return {*}
 */
export const initList = (params: { hasOperate?: string; hasOrgTree?: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/initpage',
            method: 'post',
            data: params,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询所有的部门机构列表
 * @param {object} params
 * @return {*}
 */
export const queryAllOrgTree = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryallorgtree',
            method: 'post',
            data: params,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询当前登录用户，根据权限可见的部门机构列表
 * @param {*} params
 * @return {*}
 */
export const queryAuthOrgTree = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryauthorgtree',
            method: 'post',
            data: params,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询投顾列表
 * @param {object} params
 * @return {*}
 */
export const queryConsultant = (params: {
    orgCode: string
    status?: string
    recursive?: string //是否为递归查询 组织架构下的投顾 1-是 0-否。 默认1-是
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/consultant/queryconsultant',
            method: 'post',
            data: params,
            canclePending: true
        })
    )
}

/**
 * @description: 存量分成配置页面-查询客户列表
 * @param {Partial} params
 * @return {*}
 */
export const queryCustInfo = (params: Partial<CustInfoParam>) => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/stocksplitconfig/querycustinfolist',
            method: 'post',
            data: params
            // loadingParams: {
            //     target: '.cust-option',
            //     isCust: false
            // }
        })
    )
}
