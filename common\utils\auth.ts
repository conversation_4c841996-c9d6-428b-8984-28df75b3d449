/*
 * @Description: cookies设置
 * @Author: chaohui.wu
 * @Date: 2023-07-27 13:21:08
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-09 19:36:04
 * @FilePath: /crm-web/common/utils/auth.ts
 *
 */
import Cookies from 'js-cookie'

const TokenKey = 'SESSION' // 'Admin-Token'
const cookie = Cookies

export function getToken() {
    return cookie.get(TokenKey)
}

export function setToken(token: any) {
    return cookie.set(TokenKey, token)
}

export function removeToken() {
    return cookie.remove(TokenKey)
}

/**
 * localStorge存值
 */
export const setLocalItem = (key: string, val: any) => {
    window.localStorage.setItem(key, val)
}

/**
 * localStorge删值
 */
export const deleteLocalItem = (key: string) => {
    window.localStorage.removeItem(key)
}

/**
 * localStorge取值
 */
export const getLocalItem = (key: string) => {
    window.localStorage.getItem(key)
}

/**
 * sessionStorage存值
 */
export const setSessionItem = (key: string, val: any) => {
    window.sessionStorage.setItem(key, val)
}

/**
 * sessionStorage取值
 */
export const getSessionItem = (key: string) => {
    window.sessionStorage.getItem(key)
}
/**
 * sessionStorage删除
 */
export const delateSessionItem = (key: string) => {
    window.sessionStorage.removeItem(key)
}
