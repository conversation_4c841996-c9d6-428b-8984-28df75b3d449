export {}
declare module './apiReqType' {
    interface QueryBeisenPosLevelConfigWebReq {
        /**
         * 职级编码（北森）
         */
        positionsLevelBeisen?: string
        /**
         * 职级名称(北森)
         */
        positionsLevelNameBeisen?: string
        /**
         * 层级（crm）
         */
        userLevelCrm?: string
        /**
         * 职级编码（crm）
         */
        positionsLevelCrm?: string
        /**
         * 副职编码（crm）
         */
        subPositionsLevelCrm?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
    }
    interface InsertBeisenPosLevelConfigWebReq {
        /**
         * 职级编码（北森）
         */
        positionsLevelBeisen?: string
        /**
         * 职级名称(北森)
         */
        positionsLevelNameBeisen?: string
        /**
         * 层级（crm）
         */
        userLevelCrm?: string
        /**
         * 职级编码（crm）
         */
        positionsLevelCrm?: string
        /**
         * 副职编码（crm）
         */
        subPositionsLevelCrm?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
    }
    interface UpdateBeisenPosLevelConfigWebReq {
        /**
         * 主键
         */
        id?: string
        /**
         * 职级编码（北森）
         */
        positionsLevelBeisen?: string
        /**
         * 职级名称(北森)
         */
        positionsLevelNameBeisen?: string
        /**
         * 层级（crm）
         */
        userLevelCrm?: string
        /**
         * 职级编码（crm）
         */
        positionsLevelCrm?: string
        /**
         * 副职编码（crm）
         */
        subPositionsLevelCrm?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
    }
    interface BeisenPosLevelConfigWebIdReq {
        /**
         * 主键
         */
        id?: string
    }

    export {
        QueryBeisenPosLevelConfigWebReq,
        InsertBeisenPosLevelConfigWebReq,
        UpdateBeisenPosLevelConfigWebReq,
        BeisenPosLevelConfigWebIdReq
    }
}
