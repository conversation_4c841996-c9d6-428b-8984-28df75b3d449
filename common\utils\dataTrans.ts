/*
 * @Description: 数据格式转换公用方法
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-10 10:17:22
 * @FilePath: /crm-web/common/utils/dataTrans.ts
 * eg:
 * deepMergeObj | 对象深度合并
 * dateFormat | 时间戳转东8区北京时间
 * dateTrans | 北京时间转输出样式
 * sortObjDefault | 前端对象排序
 * | formatNumForTableValue
 * transArrayToParams | 传参数转换
 * treeToArray | 将树转为数组
 * makeElementTree | 转树
 * getString | 获取字符串形式的值
 * digitAdd | 加法，解决浮点计算精度问题
 * digitMinus | 减法
 */
import moment from 'moment'

export const isString = (val: any) => Object.prototype.toString.call(val) === '[object String]'
export const isBoolean = (val: any) => Object.prototype.toString.call(val) === '[object Boolean]'
export const isNumber = (val: any) => Object.prototype.toString.call(val) === '[object Number]'
export const isObject = (val: any) => Object.prototype.toString.call(val) === '[object Object]'
export const isArray = (val: any) => Object.prototype.toString.call(val) === '[object Array]'
export const isFunction = (val: any) => Object.prototype.toString.call(val) === '[object Function]'
export const isDate = (val: any) => Object.prototype.toString.call(val) === '[object Date]'

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
// export function debounce(func: Function, wait: number, immediate: boolean) {
//     let timeout: any, args: any, context: any, timestamp: any, result: any

//     const later = () => {
//         // 据上一次触发时间间隔
//         const last = +new Date() - timestamp

//         // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
//         if (last < wait && last > 0) {
//             timeout = setTimeout(later, wait - last)
//         } else {
//             timeout = null
//             // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
//             if (!immediate) {
//                 result = func.apply(context, args)
//                 if (!timeout) {
//                     context = args = null
//                 }
//             }
//         }
//     }

//     return () => {
//         context = this
//         timestamp = +new Date()
//         const callNow = immediate && !timeout
//         // 如果延时不存在，重新设定延时
//         if (!timeout) {
//             timeout = setTimeout(later, wait)
//         }
//         if (callNow) {
//             result = func.apply(context, args)
//             context = args = null
//         }

//         return result
//     }
// }

export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate: boolean
): T {
    let timeout: NodeJS.Timeout | null

    return ((...args: Parameters<T>): ReturnType<T> => {
        let result: any
        if (timeout) {
            clearTimeout(timeout)
        }
        timeout = setTimeout(() => {
            result = func(...args)
        }, wait)
        return result
    }) as T
}

/**
 * 获取字符串形式的值
 */
export const getString = (val: number | string | undefined | null) =>
    val === 0 ? '0' : val ? '' + val : ''

/**
 * 加法，解决浮点计算精度问题
 * @param arg1
 * @param arg2
 */
export const digitAdd = (num1: string, num2: string) => {
    let baseNum = '0'
    let baseNum1
    let baseNum2
    try {
        baseNum1 = num1.toString().split('.')[1].length
    } catch (e) {
        baseNum1 = 0
    }
    try {
        baseNum2 = num2.toString().split('.')[1].length
    } catch (e) {
        baseNum2 = 0
    }
    baseNum = Math.pow(10, Math.max(baseNum1, baseNum2)).toString()
    return (digitMul(num1, baseNum) + digitMul(num2, baseNum)) / Number(baseNum)
}

/**
 * 减法
 * @param arg1
 * @param arg2
 */
export const digitMinus = (arg1: number, arg2: number) => {
    let r1
    let r2
    let m = 0
    let n = 0
    try {
        r1 = arg1.toString().split('.')[1].length
    } catch (e) {
        r1 = 0
    }
    try {
        r2 = arg2.toString().split('.')[1].length
    } catch (e) {
        r2 = 0
    }
    m = Math.pow(10, Math.max(r1, r2))
    n = r1 >= r2 ? r1 : r2
    return ((arg1 * m - arg2 * m) / m).toFixed(n)
}

/**
 * 除法
 * @param arg1
 * @param arg2
 */
export const digitDiv = (arg1: number, arg2: number) => {
    let t1
    let t2
    let r1 = 0
    let r2 = 0
    try {
        t1 = arg1.toString().split('.')[1].length
    } catch (e) {
        t1 = 0
    }
    try {
        t2 = arg2.toString().split('.')[1].length
    } catch (e) {
        t2 = 0
    }

    r1 = Number(arg1.toString().replace('.', ''))
    r2 = Number(arg2.toString().replace('.', ''))
    return digitMul(String(r1 / r2), String(Math.pow(10, t2 - t1)))
}

/**
 * 乘法
 * @param arg1
 * @param arg2
 */
export const digitMul = (arg1 = '', arg2 = '') => {
    let m = 0
    const s1 = arg1.toString()
    const s2 = arg2.toString()
    try {
        m += s1.split('.')[1].length
    } catch (e) {
        m = 0
    }
    try {
        m += s2.split('.')[1].length
    } catch (e) {
        m = 0
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
}

/**
 * 对象深度合并
 * 如果target(也就是firstObj[key])存在，
 * 且是对象的话再去调用deepMergeObj，
 * 否则就是firstObj[key]里面没这个对象，需要与secondObj[key]合并
 */
export const deepMergeObj = (firstObj: any, secondObj: any) => {
    const firstObjCopy = { ...firstObj }
    const secondObjCopy = { ...secondObj }
    for (const key in secondObjCopy) {
        firstObjCopy[key] =
            firstObjCopy[key] && firstObjCopy[key].toString() === '[object Object]'
                ? deepMergeObj(firstObjCopy[key], secondObjCopy[key])
                : (firstObjCopy[key] = secondObjCopy[key])
    }
    return firstObjCopy
}

/**
 * @description: 时间戳转东8区北京时间
 * @param {*} date
 * @param {*} transStr
 * @return {*}
 */
export const dateFormat: any = (date: any, transStr = 'yyyy/MM/DD') => {
    return date ? moment.utc(date).local().format(transStr) : ''
}

/**
 * @description: 时间戳转东8区北京时间
 * @param {*} date
 * @param {*} transStr
 * @return {*}
 */
export const datenewFormat: any = (date: any, transStr = 'yyyy-MM-DD HH:MM:SS') => {
    return date ? moment(date).format(transStr) : ''
}

/**
 * @description: 北京时间转输出样式
 * @param {*} date
 * @param {*} transStr
 * @return {*}
 */
export const dateTrans = (date: any, transStr = 'yyyy-MM-dd') => {
    return date ? moment(date).format(transStr) : ''
}

/**
 * @description: 当前时间及反推时间
 * @param {*} date
 * @param {*} transStr
 * @param {*} dateType 1｜一年前  2｜3年前  3｜今年以来  4｜自定义
 * @return {*}
 */
export const dateBaseTrans = ({
    date,
    transStr = 'yyyyMMDD',
    dateType = '4'
}: {
    date: any
    transStr: string
    dateType: string
}) => {
    moment.suppressDeprecationWarnings = true
    switch (dateType) {
        case '1':
            return date ? moment(date).subtract(1, 'year').format(transStr) : ''
        case '2':
            return date ? moment(date).subtract(3, 'year').format(transStr) : ''
        case '3':
            return date ? moment(date).startOf('year').format(transStr) : ''
        case '4':
        default:
            return '2014-01-01'
    }
}

/**
 * @description: 前端对象排序
 * @param {*} sortKey
 * @param {*} sortType
 * @return {*}
 */
export const sortObjDefault = (sortKey: string, sortType: any = 1) => {
    return (a: any, b: any) => {
        const aValue = Number(a[sortKey])
        const bValue = Number(b[sortKey])
        if (aValue < bValue) {
            return -sortType
        }
        if (aValue > bValue) {
            return sortType
        }
        return 0
    }
}

/**
 * 添加红涨绿跌类名
 */
export const addRedGreenClass = (val: number | string | null | undefined) => {
    if (!val || val === '0' || val === '0.00') {
        return ''
    }
    return Number(val) > 0 ? 'red2' : Number(val) < 0 ? 'green2' : ''
}

/**
 * @description: 传参数转换
 * @param {*} list `11,11,11`
 * @return {*}
 */
export const transArrayToParams = (list: any[]) => {
    for (const item in list) {
        if (list[item] && Array.isArray(list[item])) {
            list[item] = list[item].join(',')
        }
    }
    return list
}

/**
 * @description: 将树转为数组
 * @param {*} treeData
 * @param {*} field
 * @return {*}
 */
export const treeToArray = (treeData: any, field = 'children') => {
    let result = []
    for (const key in treeData) {
        const treeObj: any = treeData[key]
        const clone: any = JSON.parse(JSON.stringify(treeObj))
        delete clone[field]
        result.push(clone)
        if (treeObj[field]) {
            const tmp: any = treeToArray(treeObj[field], field)
            result = result.concat(tmp)
        }
    }
    return result
}

/**
 * @description: 转树
 * @param {*} params
 * @return {*}
 */
export const makeElementTree = (
    params = {
        pid: '0',
        list: [],
        pidFiled: 'pValue',
        labelFiled: 'label',
        valueFiled: 'value'
    }
) => {
    const { pid, list, pidFiled, labelFiled, valueFiled } = params
    const makeTree = (pid: string, arr: any) => {
        const res: any = []
        arr?.forEach((i: any) => {
            if (i[pidFiled] === pid) {
                const children = makeTree(i[valueFiled], list)
                const obj: {
                    label: string
                    value: any
                    pid: string
                    children?: any
                } = {
                    label: i[labelFiled],
                    value: i[valueFiled],
                    pid: i[pidFiled]
                }
                if (children.length) {
                    obj.children = children
                }
                res.push(obj)
            }
        })
        return res
    }
    return makeTree(pid, list)
}

interface TreeItem {
    value: string
    label: string
    pid: string
    status?: string
    children?: TreeItem[]
}

interface InputDataItem {
    [key: string]: string | number | boolean | undefined
    status?: string
}

export function arrayToTree(
    data: InputDataItem[],
    valueFormat = 'orgCode',
    labelFormat = 'orgName',
    pidFiled = 'parentOrgCode'
): TreeItem[] {
    const _data: TreeItem[] = data.map(item => {
        return {
            value: String(item[valueFormat]),
            label: String(item[labelFormat]),
            pid: String(item[pidFiled]),
            status: item.status as string
        }
    })

    const result: TreeItem[] = []
    const map: { [key: string]: TreeItem } = {}

    _data.forEach(item => {
        map[item.value] = item
    })

    _data.forEach(item => {
        const parent = map[item.pid]
        if (parent) {
            ;(parent.children || (parent.children = [])).push(item)
        } else {
            result.push(item)
        }
    })

    return result
}
