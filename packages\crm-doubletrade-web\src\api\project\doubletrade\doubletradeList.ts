/*
 * @Description: 绩效管理-增量分成列表
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:16:10
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 18:30:06
 * @FilePath: /crm-web/packages/crm-performance-web/src/api/project/stockSplit/stockSplitList.ts
 *
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
// @ts-ignore
import {
    SearchFundReq,
    TemplateFormReq,
    UpdateEffectdtReq,
    TemplateInfoForm,
    CheckQuestionReq,
    QueryMatchFundReq
} from './type/apiType'

/**
 * 查询基金列表接口
 * @param params
 */
export const getFundList = (params: SearchFundReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/searchfund',
            method: 'post',
            data: params
        })
    )
}

/**
 * 查询双录模版列表接口
 */
export const getTemplateList = () => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/searchtemplatelist',
            method: 'post'
        })
    )
}

/**
 * 查询双录模版列表接口
 * @param params
 * @returns
 */
export const queryTemplateList = (params: TemplateFormReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/querylist',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 根据ID查询双录模版
 * @returns
 * @param id
 */
export const queryTemplateById = (id: String) => {
    const params = {
        id: id
    }
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/gettemplate',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 审核接口
 * @returns
 * @param params
 */
export const checkQuestion = (params: CheckQuestionReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/checkquestion',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 审核接口
 * @returns
 * @param params
 */
export const queryMatchfund = (params: QueryMatchFundReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/querymatchfund',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 新增保存双录模版接口
 * @returns
 * @param params
 */
export const addTemplate = (params: TemplateInfoForm) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/addtemplate',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 修改双录模版接口
 * @returns
 * @param params
 */
export const updateTemplate = (params: TemplateInfoForm) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/updatetemplate',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 删除双录模版接口
 * @returns
 * @param id
 */
export const deleteTemplateById = (id: String) => {
    const params = {
        id: id
    }
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/deletetemplate',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * 修改双录模版有效时间接口
 * @param params
 * @returns
 */
export const updateEffectdt = (params: UpdateEffectdtReq) => {
    return axiosRequest(
        paramsMerge({
            url: '/doubletrade/template/updateeffectdt',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}
