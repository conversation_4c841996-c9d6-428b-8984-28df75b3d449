<!--
 * @Description: 预览-onlyOffice
 * @Author: chaohui.wu
 * @Date: 2023-10-07 18:22:27
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-08-01 17:50:11
 * @FilePath: /crm-web/packages/crm-common-web/src/views/viewer/PreviewWrapper.vue
 *  ?docId=2421&title=00—注册、登录、开户流程竞品调研.pdf&userId=12&type=mobile
-->
<template>
    <div class="viewer-module">
        <div id="vabOnlyOffice" @scroll.stop></div>
    </div>
</template>
<script setup lang="ts">
    const route = useRoute()
    const props = defineProps({
        option: {
            type: Object,
            default() {
                return {}
            }
        }
    })

    /**
     * @description: 文本格式转换
     * @param {*} fileType
     * @return {*}
     */
    const getFileType = (fileType: string) => {
        let docType = ''
        const fileTypesDoc = [
            'doc',
            'docm',
            'docx',
            'dot',
            'dotm',
            'dotx',
            'epub',
            'fodt',
            'htm',
            'html',
            'mht',
            'odt',
            'ott',
            'pdf',
            'rtf',
            'txt',
            'djvu',
            'xps'
        ]
        const fileTypesCsv = [
            'csv',
            'fods',
            'ods',
            'ots',
            'xls',
            'xlsm',
            'xlsx',
            'xlt',
            'xltm',
            'xltx'
        ]
        const fileTypesPPt = [
            'fodp',
            'odp',
            'otp',
            'pot',
            'potm',
            'potx',
            'pps',
            'ppsm',
            'ppsx',
            'ppt',
            'pptm',
            'pptx'
        ]
        if (fileTypesDoc.includes(fileType)) {
            // docType = 'text'
            docType = 'word'
        }
        if (fileTypesCsv.includes(fileType)) {
            // docType = 'spreadsheet'
            docType = 'cell'
        }
        if (fileTypesPPt.includes(fileType)) {
            // docType = 'presentation'
            docType = 'slide'
        }
        return docType
    }

    const mergeFileConfig = ({
        docSystemCode,
        type,
        title,
        fileType,
        userId,
        fileName
    }: {
        docSystemCode?: string
        type?: string
        title: string
        fileType: string
        userId: string
        fileName: string
    }) => {
        return {
            type,
            show: true,
            isEdit: false,
            lang: 'zh-CN',
            url: `${window._msViewer}${fileName}&userId=${userId}&docSystemCode=${docSystemCode}`,
            title,
            fileType,
            isPrint: false,
            user: { id: '12', name: 'admin' }
        }
    }
    interface DocEditor {
        destroyEditor: Function
        DocEditor: Function
    }

    const docEditor: any = ref<DocEditor | null>(null)
    /**
     * @description: 初始化文档预览
     * @param {*} option
     * @return {*}
     */
    const setPreviewer = (option: any): void => {
        // if (docEditor.value !== null) {
        //     docEditor.value?.destroyEditor()
        //     docEditor.value = null
        // }
        // 类必须初始化
        docEditor.value = null

        const { fileType, key, title, isPrint, url, editUrl, lang, user, model, token, type } =
            option || {}
        const config: any = {
            document: {
                //后缀
                fileType,
                key: key || '',
                title: title,
                permissions: {
                    edit: true, //是否可以编辑: 只能查看，传false
                    print: isPrint,
                    download: false
                    // "fillForms": true,//是否可以填写表格，如果将mode参数设置为edit，则填写表单仅对文档编辑器可用。 默认值与edit或review参数的值一致。
                    // "review": true //跟踪变化
                },
                url
            },
            type: type === 'mobile' ? 'mobile' : '',
            documentType: getFileType(option.fileType),
            editorConfig: {
                callbackUrl: editUrl, //"编辑word后保存时回调的地址，这个api需要自己写了，将编辑后的文件通过这个api保存到自己想要的位置
                lang: lang, //语言设置
                //定制
                customization: {
                    autosave: false, //是否自动保存
                    chat: false,
                    comments: false,
                    help: false,
                    // "hideRightMenu": false,//定义在第一次加载时是显示还是隐藏右侧菜单。 默认值为false
                    //是否显示插件
                    plugins: false
                },
                user: {
                    id: user?.id,
                    name: user?.name
                },
                mode: model ? model : 'edit'
            },
            width: '100%',
            height: '100%',
            token
        }
        console.log('进入onlyoffice初始化', window?.DocsAPI)
        setTimeout(() => {
            nextTick(() => {
                if (window?.DocsAPI) {
                    console.log('onlyoffice初始化', config)
                    const docEditTpl = new window.DocsAPI.DocEditor('vabOnlyOffice', config) || null
                    // eslint-disable-next-line no-undef
                    docEditor.value = docEditTpl
                    console.log('onlyoffice初始化结束', config)
                }
            })
        }, 200)
    }

    /**
     * @description: 转换配置项
     * @param {*} val
     * @return {*}
     */
    const transConfig = (val: any = {}) => {
        // 当存在route传参数,优先采用传参
        if (route?.query) {
            const { docSystemCode, docId, title, userId, type }: any = route.query || {}
            if (docId && title && userId) {
                const typeList = title?.split('.') || []
                const typeListNum = typeList?.length ?? 0
                const fileType = typeListNum ? typeList[typeListNum - 1] : ''
                return mergeFileConfig({
                    docSystemCode,
                    type,
                    title,
                    fileType,
                    userId,
                    fileName: docId
                })
            }
        }
        return mergeFileConfig(val)
    }

    watch(
        [() => props.option],
        (newVal: any, oldVal: any) => {
            console.log('watch执行')
            setPreviewer(transConfig(newVal))
        },
        {
            immediate: true,
            deep: true
        }
    )

    onMounted(() => {
        const optionTpl: any = props.option
        if (window?.DocsAPI?.DocEditor) {
            console.log('mounted执行')
            setPreviewer(transConfig(optionTpl))
        }
    })
</script>

<style lang="less" scoped>
    .viewer-module {
        width: 100vw;
        height: 100vh;
    }
</style>
