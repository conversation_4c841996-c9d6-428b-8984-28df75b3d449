<template>
    <div>
        <CrmSelectV2
            v-model="value"
            :remote="true"
            :filterable="true"
            :remote-method="remoteMethod"
            clearable
            placeholder="请输入产品代码"
            :options="productOptions"
            style="width: 240px"
            @change="handleSelectChange"
        />
    </div>
</template>

<script setup lang="ts">
    import { ref, defineProps, defineEmits } from 'vue'
    import { fetchRes } from '@common/utils'
    import { getFundList } from '@/api/project/doubletrade/doubletradeList'

    // eslint-disable-next-line vue/require-prop-types
    const props = defineProps(['modelValue'])
    const emits = defineEmits(['change', 'update:modelValue'])

    const value = ref<string>(props.modelValue)
    const productOptions = ref<any[]>([])

    // 远程搜索
    function remoteMethod(query: string) {
        if (query !== '') {
            const queryFormTpl = {
                q: query
            }
            fetchRes(getFundList(queryFormTpl), {
                successCB: (res: any) => {
                    productOptions.value = []
                    if (res === null || res === undefined || res === '') {
                        productOptions.value = []
                        return
                    }
                    if (res.list) {
                        // 获取待筛选的基金数据
                        res.list.forEach((item: any) => {
                            productOptions.value.push({
                                value: item.jjdm,
                                label: item.jjjc
                            })
                        })
                    }
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } else {
            productOptions.value = []
        }
    }

    function handleSelectChange(value: any): void {
        emits('update:modelValue', value)
    }
</script>

<style scoped></style>
