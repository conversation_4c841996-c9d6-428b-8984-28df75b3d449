/*
 * @Author: wencai.deng <EMAIL>
 * @Date: 2024-06-06 13:48:08
 * @LastEditors: wencai.deng <EMAIL>
 * @LastEditTime: 2024-06-06 17:09:43
 * @FilePath: /crm-web/packages/crm-doubletrade-web/src/views/doubletrade/createTemplate/config/templateConst.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 客户类型
export const custTypeOptions: any[] = [
    { label: '机构', value: '0' },
    { label: '个人', value: '1' }
]

// 成单方式
export const preTypeoptions: any[] = [
    {
        label: '纸质成单',
        value: '1'
    },
    {
        label: '电子成单',
        value: '2'
    }
]
// 模板类型
export const tempTypeOptions: any[] = [
    {
        label: '按照指定产品代码',
        value: '1'
    },
    {
        label: '按照指定条件',
        value: '2'
    }
]
// 风险等级
export const proRiskLevelOptions: any[] = [
    { value: '1', label: 'R1' },
    { value: '2', label: 'R2' },
    { value: '3', label: 'R3' },
    { value: '4', label: 'R4' },
    { value: '5', label: 'R5' }
]

// 产品类型
export const productypeOptions: any[] = [
    {
        value: '1',
        label: '资管产品'
    },
    {
        value: '2',
        label: '非资管普通产品'
    }
]

// 查看产品使用范围-table列
export const tableColumn = [
    {
        key: 'jjjc',
        label: '基金代码'
    },
    {
        key: 'jjdm',
        label: '基金简称'
    }
]
