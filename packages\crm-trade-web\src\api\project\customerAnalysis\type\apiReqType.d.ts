/*
 * @Description: 客户定位分析 传参 ts类型
 * @Author: chaohui.wu
 * @Date: 2023-03-25 23:44:46
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-15 19:37:59
 * @FilePath: /crm-asset-web/src/api/project/customerAnalysis/type/apiReqType.d.ts
 *
 */

export {}
declare module './apiReqType' {
    type AdjustKycParams = {
        adjustKycList: AdjustKycVo[]
        /**
         * 客户号
         */
        conscustno: string
        /**
         * 问卷ID
         */
        examId: string
    }

    /**
     * 调整问卷
     */
    interface AdjustKycVo {
        /**
         * 调整答案选项
         */
        optionChar: string
        /**
         * 调整答案ID
         */
        optionId: string
        /**
         * 问卷ID
         */
        questionId: string
        /**
         * 题目编号
         */
        sortNum: string
    }

    type CustInfoParams = Pick<AdjustKycParams, 'conscustno'>

    export { AdjustKycParams, AdjustKycVo, CustInfoParams }
}
