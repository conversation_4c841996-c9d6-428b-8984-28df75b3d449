/*
 * @Description:
 * @Author: chaohui.wu
 * @Date: 2023-07-27 13:21:08
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-13 19:01:48
 * @FilePath: /crm-web/common/components/layout/stores/tagsView.ts
 *
 */
import { defineStore } from 'pinia'

// 不在tabview上显示的路由名单
const blackList = ['loading', 'login', '404', 'noPermission', 'officePreview', 'filePreview']
export const useVisitedViewStore = defineStore({
    id: 'tagsView',
    state: () => ({
        visitedViews: [] as any
    }),
    actions: {
        // 新增标签
        addVisitedView(view: any) {
            if (!this?.visitedViews?.some((v: any) => v?.name === view?.name)) {
                if (view?.meta?.title && !blackList.includes(view.name)) {
                    if (Array.isArray(this.visitedViews)) {
                        this.visitedViews?.push({
                            ...view,
                            title: view?.meta?.title ?? ''
                        })
                    }
                }
            }
        },

        // 删除标签
        delVisitedView(view: any) {
            return new Promise(resolve => {
                const visitedViews: any = this.visitedViews?.filter(
                    (item: any) => item.path !== view.path
                )
                this.visitedViews = visitedViews
                resolve({ visitedViews })
            })
        },

        // 清空标签--重登录等情况下
        clearVisitedView(view: any) {
            this.visitedViews = []
        }

        // 更新标签导航栏
        // updateVisitedView(view) {
        //     const visitedViews = this.visitedViews
        //     const index = visitedViews.findIndex((v) => v.path === view.path)
        //     visitedViews[index] = {
        //         ...visitedViews[index],
        //         ...view
        //     }
        //     this.visitedViews = visitedViews
        //     // for (let v of this.visitedViews) {
        //     //     if (v.path === view.path) {
        //     //         v = Object.assign(v, view)
        //     //         break
        //     //     }
        //     // }
        // }
    }
})
