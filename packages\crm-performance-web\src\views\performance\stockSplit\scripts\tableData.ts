/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 16:57:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/tableData.ts
 *
 */
import { TableColumnItem } from '@/types/index'
import { formatTableValue, dateTrans, addUnit } from '@common/utils/index.js'
import { STOCK_LIST_MAP } from '@/constant'

/**
 * @description: table表格数据
 * @return {*}
 */
export const stockListTableColumn: TableColumnItem[] = [
    {
        key: 'id',
        label: '数据ID',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'auditStatus',
        label: '审核状态',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return STOCK_LIST_MAP.AYUDT_STATUS_MAP.get(c) || ''
        }
    },
    {
        key: 'validFlag',
        label: '是否生效',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.VALID_FLAG_MAP.get(c) : ''
        }
    },
    {
        key: 'configType',
        label: '分成类型',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.CONFIG_TYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'configLevel',
        label: '层级',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.CONFIG_LEVEL_MAP.get(c) : ''
        }
    },
    {
        key: 'formerConsCenter',
        label: '中心_原',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'formerConsName',
        label: '原管理层/投顾',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'formerConsStatus',
        label: '在职状态_原',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.JOB_STATUS_MAP.get(c) : ''
        }
    },
    {
        key: 'newlyConsCenter',
        label: '中心_新',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'newlyConsName',
        label: '新管理层/投顾',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'newlyConsStatus',
        label: '在职状态_新',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.JOB_STATUS_MAP.get(c) : ''
        }
    },
    {
        key: 'relatedConsName',
        label: '涉及投顾',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'activeFlag',
        label: '激活状态',
        minWidth: 100,
        formatter: ({ activeFlag }: any) => {
            return STOCK_LIST_MAP.VALID_FLAG_MAP_1.get(activeFlag) || ''
        }
    },
    {
        key: 'activeDt',
        label: '激活时间',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => dateTrans(c, 'YYYY-MM-DD')
    },
    {
        key: 'calStartDt',
        label: '计算起始日',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => dateTrans(c, 'YYYY-MM-DD')
    },
    {
        key: 'calEndDt',
        label: '计算截止日',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => dateTrans(c, 'YYYY-MM-DD')
    },
    {
        key: 'formerCalRate',
        label: '计入比例_原',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => addUnit({ val: c, unit: '%', blankPlaceholder: '-' })
    },
    {
        key: 'newlyCalRate',
        label: '计入比例_新',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => addUnit({ val: c, unit: '%', blankPlaceholder: '-' })
    },
    {
        key: 'lockDurationAmt',
        label: '锁定存续D(万)',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) =>
            addUnit({ val: c / 10000, fixed: 2, unit: '', blankPlaceholder: '-' })
    },
    {
        key: 'custom',
        label: '客户',
        slotName: 'custom',
        type: 'slot',
        minWidth: 100
    },
    {
        key: 'remark',
        label: '备注',
        minWidth: 100,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
