<!--
 * @Description: 客户拜访纪要示例页面
 * @Author: hongdong.xie
 * @Date: 2024-06-12 10:35:39
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2024-06-12 10:35:39
 * @FilePath: /crm-web/packages/crm-template/src/views/customerVisit/visitRecord/visitRecordDemo.vue
-->
<template>
    <div class="visit-record-demo">
        <div class="demo-header">
            <h2>客户拜访纪要示例</h2>
            <button class="custom-button save-button" @click="openVisitRecordDialog">
                打开拜访纪要
            </button>
        </div>
        <visit-record-index v-model="dialogVisible" @save="handleSave"></visit-record-index>
    </div>
</template>

<script lang="ts" setup>
    import { ref } from 'vue'
    import { ElMessage } from 'element-plus'
    import VisitRecordIndex from './visitRecordIndex.vue'

    defineOptions({
        name: 'VisitRecordDemo'
    })

    const dialogVisible = ref(false)

    // 打开拜访纪要弹窗
    const openVisitRecordDialog = () => {
        dialogVisible.value = true
    }

    // 保存拜访纪要
    const handleSave = (data: any) => {
        console.log('保存的数据:', data)
        ElMessage({
            type: 'success',
            message: '保存成功'
        })
    }
</script>

<style lang="less" scoped>
    .visit-record-demo {
        padding: 20px;

        .demo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }

        .custom-button {
            width: 120px;
            height: 40px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .save-button {
            background-color: #d9333f;
            color: white;
            border-color: #d9333f;

            &:hover {
                background-color: #c62f3a;
            }

            &:active {
                background-color: #b32a34;
            }
        }
    }
</style>
