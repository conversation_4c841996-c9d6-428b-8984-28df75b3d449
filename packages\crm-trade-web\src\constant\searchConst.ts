/*
 * @Description: 搜索常量映射
 * @Author: chao<PERSON>.wu
 * @Date: 2023-04-09 07:30:23
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-09 07:31:16
 * @FilePath: /crm-asset-web/src/constant/searchConst.ts
 *
 */

/**
 * @description: 产品和机构类型
 * @return {*}
 */
export const searchTypes = {
    100: '公募基金',
    200: '私募基金',
    '200_0': '私募基金披露',
    '200_1': '私募基金不披露',
    300: '公募经理',
    400: '私募经理',
    500: '公募公司',
    600: '私募公司',
    700: '私募固收',
    800: '新闻',
    900: '研报',
    1000: '私募股权',
    1900: '保险产品',
    2000: '保险公司'
}

/**
 * @description: 搜索产品或机构的时候传递的类型参数
 * @return {*}
 */
export const searchTypeParams = {
    product: '100,200,1900,700,1000', // 产品
    manager: '300,400', // 基金经理
    org: '500,600,2000', // 机构
    gm: {
        product: '100', // 公募产品
        manager: '300', // 公募基金经理
        org: '500' // 公募机构
    },
    sm: {
        product: '200', // 私募产品
        manager: '400', // 私募基金经理,
        org: '600' // 私募机构
    }
}
