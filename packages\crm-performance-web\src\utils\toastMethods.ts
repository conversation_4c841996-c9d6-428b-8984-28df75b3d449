/*
 * @Description: 提示方法
 * @Author: chaohui.wu
 * @Date: 2023-04-13 19:22:58
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-16 16:11:16
 * @FilePath: /crm-asset-web/src/utils/toastMethods.ts
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

export const messageBox = (
    params: {
        title?: string
        content?: string
        confirmBtn?: string
        cancelBtn?: string
    },
    successCB: Function | null,
    cancelCB: Function | null
) => {
    ElMessageBox.confirm(params.content, params?.title ?? '提示', {
        confirmButtonText: params?.confirmBtn ?? '确定',
        cancelButtonText: params?.cancelBtn ?? '取消',
        customClass: 'blank-message-box',
        center: true,
        dangerouslyUseHTMLString: true
    })
        .then(() => {
            successCB && successCB(params)
        })
        .catch(() => {
            ElMessage({
                type: 'warning',
                message: '取消'
            })
            cancelCB && cancelCB()
        })
}

/**
 * @description: toast
 * @param {any} param1
 * @return {*}
 */
export const message = ({ message, type = 'success' }: any) => {
    return ElMessage({
        message,
        type
    })
}
