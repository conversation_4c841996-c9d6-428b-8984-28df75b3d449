/*
 * @Description: 绩效管理data
 * @Author: chaohui.wu
 * @Date: 2023-09-07 13:57:57
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 11:14:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/stockListData.ts
 *
 */

import {
    fetchRes,
    fetchAll,
    fetchSync,
    messageBox,
    dateTrans,
    makeElementTree
} from '@common/utils/index'
import {
    queryConfigList,
    initList,
    queryAllOrgTree,
    queryAuthOrgTree
} from '@/api/project/stockSplit/stockSplitList'

export const useStockListData = defineStore('stockListData', {
    state: () => {
        return {
            operateList: [],
            organizationList: [],
            consultList: [],
            formerOrganizationList: [],
            formerConsultList: [],
            orgCodeDefault: '',
            consCodeDefault: '',
            formerOrgCodeDefault: '',
            formerConsCodeDefault: ''
        }
    },
    getters: {},
    actions: {
        setCommonData({
            organizationList,
            consultList,
            formerConsultList,
            formerOrganizationList
        }: any) {
            if (organizationList) {
                this.organizationList = organizationList
            }
            if (consultList) {
                this.consultList = consultList
            }
            if (formerOrganizationList) {
                this.formerOrganizationList = formerOrganizationList
            }
            if (formerConsultList) {
                this.formerConsultList = formerConsultList
            }
        },
        fetchPageInit(type = '') {
            if (type === 'all') {
                fetchSync(queryAuthOrgTree({}), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        // 获取组织架构数据
                        const consListTpl =
                            consultList?.length > 0
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        // 将数据存储到pinia
                        this.setCommonData({
                            organizationList: makeElementTree({
                                pid,
                                list: organizationList,
                                pidFiled: 'parentOrgCode',
                                labelFiled: 'orgName',
                                valueFiled: 'orgCode'
                            }),
                            consultList: consListTpl
                        })
                        this.orgCodeDefault = organizationList[0].orgCode ?? ''
                        this.consCodeDefault = consListTpl[0].consCode ?? ''
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            } else {
                fetchSync(queryAllOrgTree({}), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        const consListTpl =
                            consultList?.length > 0
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        // 将数据存储到pinia
                        this.setCommonData({
                            formerOrganizationList: makeElementTree({
                                pid,
                                list: organizationList,
                                pidFiled: 'parentOrgCode',
                                labelFiled: 'orgName',
                                valueFiled: 'orgCode'
                            }),
                            formerConsultList: consListTpl
                        })
                        this.formerOrgCodeDefault = organizationList[0]?.orgCode ?? ''
                        this.formerConsCodeDefault = consListTpl[0]?.consCode ?? ''
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            }
        },
        getPageInit() {
            return Promise.all([this.fetchPageInit(), this.fetchPageInit('all')])
        }
    }
})
