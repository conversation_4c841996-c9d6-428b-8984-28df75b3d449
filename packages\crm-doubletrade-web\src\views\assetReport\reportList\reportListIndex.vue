<!--
 * @Description: 大权限角色报告列表页
 * @Author: chaohui.wu
 * @Date: 2023-03-16 11:15:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 16:32:57
 * @FilePath: /crm-web/packages/crm-template/src/views/assetReport/reportList/reportListIndex.vue
 * @notice: 后期需要抽取hooks
-->
<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <label-item v-show="isLargeAuthorityRole" :label="investmentDepartment.label">
                    <tree-select
                        v-model="queryForm.investmentDepartment"
                        :placeholder="investmentDepartment.placeholder"
                        :check-strictly="true"
                        label-format="orgName"
                        value-format="orgCode"
                        :data="organizationList"
                        :style="{ width: '180px' }"
                        @nodeClick="handleTreeSelect"
                    />
                </label-item>
                <label-item v-show="isLargeAuthorityRole" :label="ownerAccount.label">
                    <crm-select
                        v-model="queryForm.ownerAccount"
                        :placeholder="ownerAccount.placeholder"
                        label-format="consName"
                        value-format="consCode"
                        filterable
                        clearable
                        :option-list="ownerAccountSelectList"
                        :style="{ width: '180px' }"
                        @change="handleOwnerAccountSelect"
                    />
                </label-item>
                <label-item :label="clientName.label">
                    <crm-select
                        v-model="queryForm.clientName"
                        :placeholder="clientName.placeholder"
                        label-format="showLabel"
                        value-format="conscustno"
                        filterable
                        clearable
                        remote
                        remote-show-suffix
                        :remote-method="queryListOwnerAccount"
                        :loading="clientNameLoading"
                        :option-list="clientNameSelectList"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item :label="phoneNumber.label">
                    <crm-input
                        v-model="queryForm.phoneNumber"
                        :placeholder="phoneNumber.placeholder"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item :label="insertCustomerAccountNumber.label">
                    <crm-input
                        v-model="queryForm.insertCustomerAccountNumber"
                        :placeholder="insertCustomerAccountNumber.placeholder"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item :label="aBillPassesThroughNumber.label">
                    <crm-input
                        v-model="queryForm.aBillPassesThroughNumber"
                        :placeholder="aBillPassesThroughNumber.placeholder"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item :label="reportCreationTime.label">
                    <date-range
                        v-model="queryForm.reportCreationTime"
                        :placeholder="reportCreationTime.placeholder"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
            </template>
            <template #tabsPanel>
                <crm-dialog-tabs
                    v-show="isHealthManagementPost"
                    v-model="panelActiveName"
                    type="card"
                    background-color
                    @tabClick="handleTabsChange"
                >
                    <el-tab-pane
                        v-for="(item, index) in tabsPanelList"
                        :key="`table-panel${index}`"
                        :label="item.label"
                        :name="item.name"
                    ></el-tab-pane>
                </crm-dialog-tabs>
            </template>
            <template #operationBtns>
                <crm-button
                    v-show="showInfo('create')"
                    size="small"
                    :icon="Plus"
                    type="primary"
                    :radius="true"
                    @click="handleAdd"
                    >创建报告</crm-button
                >
                <crm-button
                    v-show="showInfo('download')"
                    size="small"
                    :icon="Upload"
                    plain
                    :radius="true"
                    :disabled="downloadFlag"
                    :loading="downloadLoading"
                    @click="handleDownload"
                    >批量下载</crm-button
                >
                <crm-button
                    v-show="showInfo('delete')"
                    size="small"
                    :icon="Upload"
                    plain
                    :radius="true"
                    :disabled="delateFlag"
                    @click="handleDelete"
                    >批量删除</crm-button
                >
            </template>
            <template #operationLeft>
                <!-- <chrome-link-index></chrome-link-index> -->
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :default-sort="defaultSort"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    @sortChange="handleSortChange"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="showInfo('edit') && scope.row.canOperate === '1'"
                            size="small"
                            :text="true"
                            link
                            @click="handleEdit(scope.row)"
                            >编辑</el-button
                        >
                        <el-button
                            v-if="showInfo('preview')"
                            size="small"
                            :text="true"
                            link
                            @click="handlePreview(scope.row)"
                            >预览</el-button
                        >
                        <el-button
                            v-if="showInfo('download') && scope.row.coun === '1'"
                            size="small"
                            :text="true"
                            link
                            @click="handleDownload(scope.row?.assetId, true)"
                            >下载</el-button
                        >
                        <el-button
                            v-if="showInfo('delete')"
                            size="small"
                            :text="true"
                            link
                            @click="handleDelete(scope.row.assetId, true)"
                            >删除</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <riskyc-index
            v-if="dialogVisible"
            v-model="dialogVisible"
            :cons-cust-no="amountTransData?.conscustNo"
            @callBack="handleAdd"
        ></riskyc-index>
        <adjust-amt-index
            v-if="amountDialogVisible"
            v-model="amountDialogVisible"
            :trans-data="amountTransData"
            @callBack="handleAdd"
        ></adjust-amt-index>
        <!-- <Suspense v-if="dialogVisible">
            <template #default>
                <riskyc-index
                    v-if="dialogVisible"
                    v-model="dialogVisible"
                    :cons-cust-no="amountTransData?.conscustNo"
                    @callBack="handleAdd"
                ></riskyc-index>
            </template>
            <template #fallback>
                <loading :visible="true" :show-percentage="false"></loading>
            </template>
        </Suspense> -->
    </div>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { Plus, Upload, RefreshLeft } from '@element-plus/icons-vue'
    import {
        getUserPermissions,
        listConsCode,
        listCustByConsCode,
        listAssetFixedpositionByUser,
        listAssetFixedPosition,
        deleteAssetFilePathList
    } from '@/api/project/reportList/reportListAll'
    import {
        ConscustInfoVo,
        ListResponse,
        CmAssectFixedPositionVo,
        SortOrderCumstom,
        VerifyCodes
    } from '@/types/index'

    import {
        sortObjDefault,
        downloadFile,
        makeElementTree,
        openUrl,
        removePendingAll,
        fetchRes
    } from '@common/utils/index'

    import { returnCodeRes } from '@/constant/index'

    import { dataList } from './scripts/labelData'
    import { reportListTableColumn, showTableColumn } from './scripts/tableData'
    import { useVerifyCreater } from './scripts/verifyCreater'
    // 异步导入
    const RiskycIndex = defineAsyncComponent(
        () => import('@/views/components/dialogCommon/riskyc/riskycIndex.vue')
    )
    const AdjustAmtIndex = defineAsyncComponent(
        () => import('@/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue')
    )
    // const RiskycIndex = defineAsyncComponent({
    //     // 加载函数
    //     loader: () => import('@/views/components/dialogCommon/riskyc/riskycIndex.vue'),
    //     // 加载异步组件时使用的组件
    //     // loadingComponent: Loading,
    //     // 展示加载组件前的延迟时间，默认为 200ms
    //     delay: 200,
    //     // 加载失败后展示的组件
    //     // errorComponent: ErrorComponent,
    //     // 如果提供了一个 timeout 时间限制，并超时了
    //     // 也会显示这里配置的报错组件，默认值是：Infinity
    //     timeout: 3000
    // })
    // import AdjustAmtIndex from '@/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue'
    // import RiskycIndex from '@/views/components/dialogCommon/riskyc/riskycIndex.vue'
    import { baseURL } from '@/api/mock'
    const store = useVerifyCreater()
    const { verifyMethod } = store
    const {
        investmentDepartment,
        ownerAccount,
        clientName,
        phoneNumber,
        insertCustomerAccountNumber,
        aBillPassesThroughNumber,
        reportCreationTime,
        tabsPanelList,
        chromeLink
    } = dataList

    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)

    /**
     * @description: 权限角色变量
     * @return {*}
     */
    const userPermissions = ref<string>('2')
    // 健康管理岗
    const isHealthManagementPost = computed(() => userPermissions.value === '0')
    // 法务岗
    const isLegalAffairs = computed(() => userPermissions.value === '1')
    const isInvestmentAdviser = computed(() => userPermissions.value === '2')
    // 大权限
    const isLargeAuthorityRole = computed(
        () => isHealthManagementPost.value || isLegalAffairs.value
    )

    /**
     * @description: 默认数据
     * @return {*}
     */
    const organizationList = ref<any[]>([])

    /**
     * @description: 默认调整字段展示
     * @param {*} val
     * @return {*}
     */
    const showInfo = (val: string) => {
        switch (userPermissions.value) {
            case '0':
                if (panelActiveName.value === 'myReport') {
                    return ['create', 'edit', 'preview', 'delete', 'download'].includes(val)
                }
                return ['preview', 'download'].includes(val)
            case '1':
                return ['preview', 'download'].includes(val)
            case '2':
                return ['create', 'edit', 'preview', 'delete', 'download'].includes(val)
            default:
                break
        }
    }

    /**
     * @description: 权限请求/投顾所属部门init
     * @return {*}
     */
    const fetchUserPermissions = () => {
        listLoading.value = true
        fetchRes(getUserPermissions(), {
            successCB: (res: any) => {
                if (
                    res.returnCode === returnCodeRes.SUCCESS ||
                    res.returnCode === returnCodeRes.CRM_SUCCESS
                ) {
                    const { permissions, organizations, consCodes } = res
                    userPermissions.value = permissions
                    organizationList.value = makeElementTree({
                        pid: '',
                        list: organizations,
                        pidFiled: 'parentOrgCode',
                        labelFiled: 'orgName',
                        valueFiled: 'orgCode'
                    })
                    // 默认选中第一个
                    if (organizationList.value[0]) {
                        queryForm.investmentDepartment = organizationList.value[0]?.value
                    }
                    if (consCodes[0]) {
                        ownerAccountSelectList.value = consCodes
                        queryForm.ownerAccount = consCodes[0].consCode
                    }
                }
                listLoading.value = false
            },
            errorCB: () => {
                userPermissions.value = ''
                organizationList.value = []
                listLoading.value = false
            },
            catchCB: () => {
                userPermissions.value = ''
                organizationList.value = []
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        investmentDepartment = ''
        ownerAccount = ''
        clientName = ''
        phoneNumber = ''
        insertCustomerAccountNumber = ''
        aBillPassesThroughNumber = ''
        reportCreationTime = {
            startDate: '',
            endDate: ''
        }
        sort = 'creDt'
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const panelActiveName = ref<string>('myReport')
    const tableData = ref<object[]>([])
    // table表格column数据展示
    const tableColumn = computed(() => {
        return panelActiveName.value === 'myReport'
            ? showTableColumn(['fileName', 'creDt'], reportListTableColumn)
            : showTableColumn(
                  reportListTableColumn.map(item => item.key),
                  reportListTableColumn
              )
    })
    // 大权限tabs切换接口联动
    const handleTabsChange = (tab: any, event: Event) => {
        panelActiveName.value = tab.props.name
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description: 列表请求
     * @return {*}
     */
    const queryList = async () => {
        listLoading.value = true
        let resObj: ListResponse | any
        let status = ''
        // eslint-disable-next-line no-case-declarations
        const queryFormTpl = {
            orgCode: queryForm.investmentDepartment,
            consCode: queryForm.ownerAccount,
            sort: queryForm.sort,
            order: transOrder(queryForm.order),
            endDate: queryForm.reportCreationTime.endDate,
            hboneNo: queryForm.aBillPassesThroughNumber,
            mobile: queryForm.phoneNumber,
            startDate: queryForm.reportCreationTime.startDate,
            conscustno: queryForm.insertCustomerAccountNumber,
            inputCustNo: queryForm.clientName
        }
        removePendingAll()
        try {
            switch (panelActiveName.value) {
                case 'allReport':
                    resObj = await listAssetFixedPosition({
                        ...pageObj.value,
                        ...queryFormTpl
                    }).catch(e => {
                        status = e.code
                    })
                    break
                case 'myReport':
                default:
                    if (isLegalAffairs.value) {
                        resObj = await listAssetFixedPosition({
                            ...pageObj.value,
                            ...queryFormTpl
                        }).catch(e => {
                            status = e.code
                        })
                    } else {
                        resObj = await listAssetFixedpositionByUser({
                            ...pageObj.value,
                            ...queryFormTpl
                        }).catch(e => {
                            status = e.code
                        })
                    }
                    break
            }
            if (status === 'ERR_CANCELED') {
                return (listLoading.value = true)
            }
            listLoading.value = false
            const { data } = resObj
            const { rows, page, total, size } = data
            tableData.value = rows
            pageObj.value.page = Number(page)
            pageObj.value.total = Number(total)
            pageObj.value.size = Number(size)
        } catch {
            tableData.value = []
            listLoading.value = false
        }
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    /**
     * @description: table表格选中数据
     * @param {*} sel
     * @param {*} row
     * @return {*}
     */
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: CmAssectFixedPositionVo[], row: object): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                if (item.creDt) {
                    item.creDtTime = new Date(item.creDt).getTime()
                }
                return item
            })
            .sort(sortObjDefault('creDtTime', transOrder(queryForm.order) === 'ASC' ? 1 : ''))
    }
    // 清空选择并刷新列表
    const clearSelection = (flag = false): void => {
        if (flag) {
            tableSelectList.value = []
        }
        queryList()
    }

    /**
     * @description: 投顾数据请求
     * @return {*}
     */
    const ownerAccountSelectList = ref<object[]>(ownerAccount.selectList)
    const queryListConsCode = async () => {
        try {
            const listConsCodeArr: any = await listConsCode({
                orgCode: queryForm.investmentDepartment
            })
            const { data } = listConsCodeArr
            const { consCodeList } = data
            ownerAccountSelectList.value = consCodeList
            if (consCodeList[0]) {
                queryForm.ownerAccount = consCodeList[0].consCode
            }
        } catch {
            ownerAccountSelectList.value = []
        }
    }

    /**
     * @description: 客户姓名搜索
     * @param {*} query
     * @return {*}
     */
    const clientNameLoading = ref<boolean>(false)
    const clientNameSelectList = ref<object[]>(clientName.selectList)
    const queryListOwnerAccount = async (query: string) => {
        if (!query) {
            clientNameSelectList.value = []
            return
        }
        try {
            clientNameLoading.value = true
            const listConsCodeArr: any = await listCustByConsCode({
                consCode: queryForm.ownerAccount,
                orgCode: queryForm.investmentDepartment,
                custName: query || queryForm.clientName
            })
            const { data } = listConsCodeArr
            const custList: ConscustInfoVo[] = data.custList
            clientNameSelectList.value = custList
            clientNameLoading.value = false
        } catch {
            clientNameSelectList.value = []
            clientNameLoading.value = false
        }
    }

    /**
     * @description: 部门选择后联动
     * @return {*}
     */
    const handleTreeSelect = () => {
        queryForm.clientName = ''
        // 判断当投顾所属部门选中
        nextTick(() => {
            queryListConsCode()
        })
    }

    /**
     * @description: 选择投顾清空后面所属投顾
     * @param {*} val
     * @return {*}
     */
    const handleOwnerAccountSelect = (val: any) => {
        queryForm.clientName = ''
        // 判断当投顾所属部门选中
        queryListOwnerAccount('')
    }

    /**
     * @description: 新增报告校验逻辑
     * @param {*} void
     * @return {*}
     */
    // 显示金额调整弹框，校验后提示
    const amountDialogVisible = ref<boolean>(false)
    const amountTransData = ref({
        conscustNo: '',
        amount: 0
    })
    const handleAdd = (): void => {
        // 校验
        verifyMethod(
            {
                conscustno: queryForm.insertCustomerAccountNumber,
                inputCustNo: queryForm.clientName,
                mobile: queryForm.phoneNumber,
                hboneNo: queryForm.aBillPassesThroughNumber,
                consCode: queryForm.ownerAccount
            },
            (val: VerifyCodes | any) => {
                switch (val.callBackFunc) {
                    case 'showRiskQuestionnaire':
                        amountTransData.value = {
                            conscustNo: val?.conscustNo || queryForm.insertCustomerAccountNumber,
                            amount: 0
                        }
                        dialogVisible.value = true
                        break
                    case 'showAmountAdjustment':
                        amountTransData.value = {
                            conscustNo: val?.conscustNo || queryForm.insertCustomerAccountNumber,
                            amount: 0
                        }
                        nextTick(() => {
                            amountDialogVisible.value = true
                        })

                        break
                    case 'successCallBack':
                        handleEdit({
                            assetId: val.assetId,
                            conscustno: val?.conscustNo || queryForm.insertCustomerAccountNumber
                        })
                        break
                    default:
                        break
                }
            }
        )
    }

    /**
     * @description: 预览
     * @param {*} params
     * @return {*}
     */
    const handlePreview = (params: any) => {
        const { conscustno, assetId, canOperate }: any = params
        if (canOperate === '1') {
            if (conscustno && assetId) {
                const searchParams = new URLSearchParams('')
                searchParams.set('conscustno', conscustno)
                searchParams.set('assetId', params.assetId)
                const searchStr = searchParams.toString()
                openUrl({ type: 1, path: `/assetPreview?${searchStr}` })
            }
        } else if (assetId) {
            openUrl({
                type: 3,
                path: `${baseURL}/assent/downAssentFile.do?id=${assetId}&type=1`
            })
        }
    }

    /**
     * @description: 编辑页跳转
     * @param {*} params
     * @return {*}
     */
    interface EditorParams {
        conscustno?: string
        assetId: string
    }
    const handleEdit = (params: EditorParams) => {
        const { conscustno, assetId }: any = params
        if (conscustno && assetId) {
            const searchParams = new URLSearchParams('')
            searchParams.set('conscustno', conscustno)
            searchParams.set('assetId', params.assetId)
            const searchStr = searchParams.toString()
            openUrl({ type: 1, path: `/assetEdit?${searchStr}` })
        }
    }

    /**
     * @description: 批量下载联动
     * @param {*} computed
     * @return {*}
     */
    // 选中下载数组
    const downLoadIds = computed(() => {
        return tableSelectList.value
            .map((item: any, index) => {
                if (item && item.coun === '1') {
                    return item.assetId
                }
            })
            .filter(item => item)
    })
    // 弹出无法下载数据列表
    const toastNormalizeList = (key: string) => {
        return tableSelectList.value
            .map((item: any, index) => {
                if (item && item[key] !== '1') {
                    return item
                }
            })
            .filter(item => item)
    }
    // 下载按钮展示
    const downloadFlag = computed(() => {
        const { length } = downLoadIds.value
        return length <= 0
    })
    // 下载操作
    const downloadLoading = ref<boolean>(false)
    const handleDownload = (val: any, isSinger?: boolean) => {
        const params = { assetIds: downLoadIds.value }
        const oplKey = '下载'
        let textKey = '批量'
        if (isSinger) {
            params.assetIds = [val] || []
            textKey = ''
        }
        const txtObj = {
            title: `确认${textKey}${oplKey}选中报告？`,
            successMsg: `${textKey}${oplKey}成功`,
            failMsg: `${textKey}${oplKey}失败请重试～～`,
            cancelMsg: `${textKey}${oplKey}取消`
        }
        const normalizeList = toastNormalizeList('coun')
        const { length: normalizeListNum } = normalizeList
        if (normalizeListNum > 0) {
            ElMessage({
                type: 'warning',
                message: `${normalizeList[0].fileName}${
                    normalizeListNum === 1 ? '' : '等报告'
                }未在预览页生成并下载过，暂不支持下载~`
            })
        }
        downloadLoading.value = true
        listLoading.value = true
        downloadFile(
            {
                url: '/asset/homepage/downloadassetlist',
                method: 'post',
                data: params
            },
            () => {
                downloadLoading.value = false
                listLoading.value = false
            },
            () => {
                ElMessage({
                    type: 'success',
                    message: txtObj.successMsg
                })
                listLoading.value = false
                clearSelection(true)
            }
        )
    }

    /**
     * @description: 批量删除联动
     * @param {*} computed
     * @return {*}
     */
    // 选中删除ids
    const delateIds = computed(() => {
        return tableSelectList.value
            .map((item: any, index) => {
                if (item) {
                    return item.assetId
                }
            })
            .filter(item => item)
    })
    // 选中删除标记
    const delateFlag = computed(() => {
        const { length } = delateIds.value
        return length <= 0
    })
    // 选中删除操作
    const handleDelete = (val: any, isSinger?: boolean) => {
        const params = { assetIds: delateIds.value }
        const oplKey = '删除'
        let textKey = '批量'
        if (isSinger) {
            params.assetIds = [val] || []
            textKey = ''
        }
        const txtObj = {
            title: `确认${textKey}${oplKey}选中报告？`,
            successMsg: `${textKey}${oplKey}成功`,
            failMsg: `${textKey}${oplKey}失败请重试～～`,
            cancelMsg: `${textKey}${oplKey}取消`
        }
        ElMessageBox.confirm(txtObj.title, '注意', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            customClass: 'blank-message-box',
            center: true
        })
            .then(() => {
                deleteAssetFilePathList(params)
                    .then(res => {
                        ElMessage({
                            type: 'success',
                            message: txtObj.successMsg
                        })
                        clearSelection(true)
                    })
                    .catch(res => {
                        ElMessage({
                            type: 'warning',
                            message: txtObj.failMsg
                        })
                    })
            })
            .catch(() => {
                ElMessage({
                    type: 'info',
                    message: txtObj.cancelMsg
                })
            })
    }

    /**
     * @description: table 排序
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    // 默认排序
    const defaultSort = computed(() => {
        return { prop: queryForm.sort, order: queryForm.order }
    })
    // 排序值映射
    const transOrder = (key: string) => {
        switch (key) {
            case 'ascending':
                return 'ASC'
            case 'descending':
                return 'DESC'
            default:
                return 'DESC'
        }
    }
    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }
    // 排序联动
    const handleSortChange = (val: SortParams) => {
        queryForm.order = val.order
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description 重置
     * @param resetQueryForm 重置
     */
    const resetQueryForm = () => {
        Object.assign(pageObj, new PageObj())
        Object.assign(queryForm, new QueryForm())
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description 提示
     */

    onBeforeMount(() => {
        fetchUserPermissions()
        // queryList()
    })
</script>
<style lang="less" scoped></style>
