<!--
 * @Author: xing.zhou01
 * @Date: 2022-03-26 15:35:12
 * @LastEditTime: 2022-03-28 15:32:31
 * @LastEditors: xing.zhou01
-->
<!-- 空view，解决多级路由嵌套的时候渲染多个layout的场景 -->
<template>
    <router-view v-slot="{ Component }">
        <keep-alive>
            <component :is="Component" />
        </keep-alive>
    </router-view>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'BlankView',
        computed: {
            key() {
                return this.$route.activeMenuName
            }
        }
    })
</script>
