/*
 * @Description: 资产配置报告列表
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:16:10
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:13:08
 * @FilePath: /crm-rpo-template/packages/crm-template/src/api/project/reportList/reportListAll.ts
 *
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import {
    ListAssetFixedpositionByUser,
    ListAssetFixedPosition,
    OperationParams,
    CheckCustAuthVo,
    ListCustByConsCode,
    ListConsCode
} from './type/apiType'

/**
 * @description: 获取登陆人界面权限
 * @return {*}
 */
export const getUserPermissions = () => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/getuserpermissions',
            method: 'get',
            loadingParams: {
                target: 'body'
            }
        })
    )
}

/**
 * @description: 查询指定客户资产配置报告/资产配置报告列表
 * @param {ListAssetFixedpositionByUser} params
 * @return {*}
 */
export const listAssetFixedpositionByUser = (params: ListAssetFixedpositionByUser) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/listassetfixedpositionbyuser',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * @description: 查询已生成资产配置报告列表，根据大权限人员查询部门下全部人员，已生成的资产配置报告
 * @param {ListAssetFixedPosition} params
 * @return {*}
 */
export const listAssetFixedPosition = (params: ListAssetFixedPosition) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/listassetfixedposition',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}

/**
 * @description: 批量删除资产配置报告
 * @param {DelateParams} params
 * @return {*}
 */
export const deleteAssetFilePathList = (params: OperationParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/deleteassetlist',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 批量下载
 * @param {DownloadParams} params
 * @return {*}
 */
export const downloadAssetList = (params: OperationParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/downloadassetlist',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 下载Google安装包
 * @return {*}
 */
export const downloadGoogle = () => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/downloadgoogle',
            method: 'post'
        })
    )
}

/**
 * @description: 验证客户创建资产配置权限 67720618
 * @param {CheckCustAuthVo} params
 * @return {*}
 */
export const checkCustAuth = (params: CheckCustAuthVo) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/checkcustauth',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.crm_table_middle'
            }
        })
    )
}
/**
 * @description: 查询投顾名下客户
 * @param {ListCustByConsCode} params
 * @return {*}
 */
export const listCustByConsCode = (params: ListCustByConsCode) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/listcustbyconscode',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 查询所属部门
 * @return {*}
 */
export const listOrg = () => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/listorg',
            method: 'post'
        })
    )
}

/**
 * @description: 查询所属投顾
 * @param {ListConsCode} params
 * @return {*}
 */
export const listConsCode = (params: ListConsCode) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/homepage/listconscode',
            method: 'post',
            data: params
        })
    )
}
