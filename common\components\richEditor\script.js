/* eslint-disable no-debugger */
// 上传图片
export class ImageUploader {
    constructor(loader, request, onImageSize) {
        this.loader = loader
        this.request = request
        this.onImageSize = onImageSize
    }

    async upload() {
        // this.getImageSize(this.loader.data)
        const file = await this.loader.file
        const compressRes = await compressImg(file, {})
        const { width, height, targetHeight, targetWidth, copressed } = compressRes
        console.log('上传图片大写:', width, height, targetHeight, targetWidth, copressed)
        await this.onImageSize({ width, height, targetHeight, targetWidth, copressed })
        const data = new FormData()
        data.append('upload', compressRes.file)
        data.append('stamp', Math.random())
        const url = await this.request(data)
        return { default: url || '' }
    }
}
export function getImageSize(loader) {
    return loader.file.then(res => {
        // eslint-disable-next-line no-debugger
        // debugger
        console.log(loader.data, res, loader.file)
        return Promise.resolve({ data: loader.data, res, file: loader.file })
    })
}
export function compressImg(
    file,
    { quality = 1, maxHeight = 920, maxWidth = 780, onAlert = () => undefined }
) {
    // 大小kb
    // const fileSize = parseInt((file.size / 1024).toFixed(2))

    return new Promise(resolve => {
        const fileType = file.type
        const reader = new FileReader()
        reader.onload = ({ target: { result: src } }) => {
            const image = new Image()
            image.onload = async () => {
                const canvas = document.createElement('canvas')
                const context = canvas.getContext('2d')
                // 真实宽高
                const targetWidth = image.width
                const targetHeight = image.height

                // 判断图片长宽是否超限制
                // if (targetHeight > maxHeight || targetWidth > maxWidth) {

                // }
                // 还可以对大小判断

                let width = targetWidth > maxWidth ? maxWidth : targetWidth
                let height = targetHeight > maxHeight ? maxHeight : targetHeight
                // const whRatio = targetWidth / targetHeight
                let needCopress = false
                let miniFile = file
                let canvasURL
                if (targetWidth > maxWidth) {
                    // 等比缩放到780宽度后 高度还大于最大高度920,就进行压缩
                    if ((maxWidth / targetWidth) * targetHeight > maxHeight) {
                        // 这个时候要压缩了
                        needCopress = true
                    }
                } else if (targetHeight > maxHeight) {
                    needCopress = true
                }
                if (needCopress) {
                    height = maxHeight
                    width = (targetWidth * maxHeight) / targetHeight
                    // 进行压缩处理
                    canvas.width = width
                    canvas.height = height

                    context.clearRect(0, 0, width, height)
                    context.drawImage(image, 0, 0, width, height)
                    canvasURL = canvas.toDataURL(fileType, quality)
                    const buffer = atob(canvasURL.split(',')[1])
                    let length = buffer.length
                    const bufferArray = new Uint8Array(new ArrayBuffer(length))
                    while (length--) {
                        bufferArray[length] = buffer.charCodeAt(length)
                    }
                    miniFile = new File([bufferArray], file.name, {
                        type: fileType
                    })
                }

                resolve({
                    targetWidth,
                    targetHeight,
                    width,
                    height,
                    copressed: needCopress,
                    file: miniFile,
                    origin: file,
                    beforeSrc: src,
                    afterSrc: canvasURL,
                    beforeKB: Number((file.size / 1024).toFixed(2)),
                    afterKB: Number((miniFile.size / 1024).toFixed(2))
                })
            }
            image.src = src
        }
        reader.readAsDataURL(file)
    })
}
