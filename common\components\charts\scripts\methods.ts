/**
 * echarts图表用到的公共方法
 */

import { chartColors } from './chartColors'

/**
 * @param {*} type 通用大类 【type-type2】二级分类
 * @param {*} keyWord 输出关键字
 */

export function getCurColor({ type, keyWord = 'color', colorType = 1 }: any) {
    const mapColor = [
        {
            code: 'fof104',
            name: '大类稳健FOF',
            color: '#004360',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    // color: 'rgba(248, 174, 112, 0.6)',
                    color: '#668EA0',
                    opcity: 0.6
                },
                {
                    code: 'HW',
                    name: '海外',
                    // color: 'rgba(248, 174, 112, 0.3)',
                    color: '#B2C6CF'
                }
            ]
        },
        {
            code: 'fof105',
            name: '大类进取FOF',
            color: '#D20000',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    // color: 'rgba(186, 73, 73, 0.6)',
                    color: '#E46666',
                    opcity: 0.6
                },
                {
                    code: 'HW',
                    name: '海外',
                    // color: 'rgba(186, 73, 73, 0.3)',
                    color: '#EC9898'
                }
            ]
        },
        {
            code: 'fof106',
            name: '大类平衡FOF',
            color: '#0070C0',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    // color: 'rgba(110, 132, 181, 0.6)',
                    color: '#66A9D9'
                },
                {
                    code: 'HW',
                    name: '海外',
                    // color: 'rgba(110, 132, 181, 0.3)',
                    color: '#B2D4EC'
                }
            ]
        }
    ]
    let colorList = [
        {
            code: '1',
            name: '多策略',
            color: '#E5E7EB'
        },
        {
            code: '0',
            name: '单策略',
            color: '#FFEEDE'
        },
        {
            code: 'FOF',
            name: '大类配置FOF',
            color: '#DB4C4D',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#E46666',
                    opcity: 0.6
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#EC9898'
                }
            ]
        },
        {
            code: 'fof104',
            name: '大类稳健FOF',
            color: '#31657D',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#668EA0',
                    opcity: 0.6
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#B2C6CF'
                }
            ]
        },
        {
            code: 'fof105',
            name: '大类进取FOF',
            color: '#DB4C4D',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#E46666',
                    opcity: 0.6
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#EC9898'
                }
            ]
        },
        {
            code: 'fof106',
            name: '大类平衡FOF',
            color: '#2E88C8',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#66A9D9'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#B2D4EC'
                }
            ]
        },
        {
            code: '101',
            name: '股票型',
            color: '#D20000',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#E46666'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#EC9898'
                }
            ]
        },
        {
            code: '102',
            name: '股权型',
            color: '#B33F56',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#D18C9A'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#E8C5CC'
                }
            ]
        },
        {
            code: '201',
            name: 'CTA策略',
            color: '#0070C0',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#66A9D9'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#B2D4EC'
                }
            ]
        },
        {
            code: '202',
            name: '另类策略',
            color: '#A065A8'
        },
        {
            code: '203',
            name: '宏观策略',
            color: '#EA7F25',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#F2B27C'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#F8D8BD'
                }
            ]
        },
        {
            code: '302',
            name: '市场中性',
            color: '#039BB3',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#68C3D1'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#B3E1E8'
                }
            ]
        },
        {
            code: '301',
            name: '固定收益',
            color: '#004360',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#668EA0'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#B2C6CF'
                }
            ]
        },
        {
            code: '303',
            name: '收益型保险',
            color: '#1F2766',
            child: [
                {
                    code: 'GN',
                    name: '国内',
                    color: '#797EA4'
                },
                {
                    code: 'HW',
                    name: '海外',
                    color: '#BBBED1'
                }
            ]
        },
        {
            code: '999',
            name: '其他',
            color: '#7F7F7F'
        }
    ]
    switch (colorType) {
        case 2:
            colorList = colorList.map(item => {
                const curArr = mapColor.findIndex(store => store.code === item.code)
                if (curArr > -1) {
                    return mapColor[curArr]
                }
                return item
            })
            break
        default:
            break
    }
    const typeList = type && type.includes('-') ? type.split('-') : [type]
    const firstItem: any = colorList.filter(item => typeList[0] === item.code)[0]
    if (typeList[1] && firstItem && firstItem.child) {
        const seckendItem: any = firstItem.child.filter((item: any) => typeList[1] === item.code)[0]
        return seckendItem[keyWord]
    }
    return firstItem[keyWord]
}
/**
 * 把16进制色值转换成rgba格式
 * @param str<String> 16进制色值，7位，如'#000000'
 * @param opacity<Number> 透明度，0-1
 * @return rgba色值，如rgba(0, 0, 0, .5)
 */
export function switchColorToRgba(str: string, opacity: string | number) {
    let sColor = str.toLowerCase()
    opacity = String(opacity) ? opacity : 1
    if (sColor) {
        if (sColor.length === 4) {
            let sColorNew = '#'
            for (let i = 1; i < 4; i += 1) {
                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
            }
            sColor = sColorNew
        }
        // 处理六位的颜色值
        const sColorChange = []
        for (let i = 1; i < 7; i += 2) {
            sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
        }
        return 'rgba(' + sColorChange.join(',') + ',' + opacity + ')'
    }
    return sColor
}

/**
 * 防抖
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
// export function debounce(func: Function, wait: number, immediate: boolean) {
//     let timeout: any, args: any, context: any, timestamp: any, result: any

//     const later = () => {
//         // 据上一次触发时间间隔
//         const last = +new Date() - timestamp

//         // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
//         if (last < wait && last > 0) {
//             timeout = setTimeout(later, wait - last)
//         } else {
//             timeout = null
//             // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
//             if (!immediate) {
//                 result = func.apply(context, args)
//                 if (!timeout) {
//                     context = args = null
//                 }
//             }
//         }
//     }

//     return (): any => {
//         context = debounce ?? null
//         timestamp = +new Date()
//         const callNow = immediate && !timeout
//         // 如果延时不存在，重新设定延时
//         if (!timeout) {
//             timeout = setTimeout(later, wait)
//         }
//         if (callNow) {
//             result = func.apply(context, args)
//             context = args = null
//         }

//         return result
//     }
// }

export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate: boolean
): T {
    let timeout: NodeJS.Timeout | null

    return ((...args: Parameters<T>): ReturnType<T> => {
        let result: any
        if (timeout) {
            clearTimeout(timeout)
        }
        timeout = setTimeout(() => {
            result = func(...args)
        }, wait)
        return result
    }) as T
}

/**
 * 对象深度合并
 * 如果target(也就是firstObj[key])存在，
 * 且是对象的话再去调用deepMergeObj，
 * 否则就是firstObj[key]里面没这个对象，需要与secondObj[key]合并
 */
export function deepMergeObj(firstObj: any, secondObj: any) {
    const firstObjCopy = { ...firstObj }
    const secondObjCopy = { ...secondObj }
    for (const key in secondObjCopy) {
        firstObjCopy[key] =
            firstObjCopy[key] && firstObjCopy[key].toString() === '[object Object]'
                ? deepMergeObj(firstObjCopy[key], secondObjCopy[key])
                : (firstObjCopy[key] = secondObjCopy[key])
    }
    return firstObjCopy
}

export function deepMergeObj2(firstObj: any, secondObj: any) {
    const firstObjCopy = { ...firstObj }
    const secondObjCopy = { ...secondObj }
    for (const key in secondObjCopy) {
        firstObjCopy[key] =
            firstObjCopy[key] && typeof firstObjCopy[key] === 'object'
                ? deepMergeObj(firstObjCopy[key], secondObjCopy[key])
                : (firstObjCopy[key] = secondObjCopy[key])
    }
    return firstObjCopy
}

/**
 * 日期格式化：yyyyMMdd to yyyy-mm-dd
 * @param date: yyyyMMdd
 * @param splitCharacter: '-', 分隔符，默认'-'
 */
export const dateFormat = (date: any, splitCharacter = '-') => {
    if (!date || date.length !== 'yyyyMMdd'.length) {
        return date
    }
    const pattern = /(\d{4})(\d{2})(\d{2})/
    return date.replace(pattern, `$1${splitCharacter}$2${splitCharacter}$3`)
}

/**
 * 日期格式化：yyyyMM to yyyy-mm
 * @param date: yyyyMM
 * @param splitCharacter: '-', 分隔符，默认'-'
 */
export const dateFormatYm = (date: any, splitCharacter = '-') => {
    if (!date || date.length !== 'yyyyMM'.length) {
        return date
    }
    const pattern = /(\d{4})(\d{2})/
    return date.replace(pattern, `$1${splitCharacter}$2`)
}

/**
 * 获取字符串形式的值
 */
export const getString = (val: number | string) => (val === 0 ? '0' : val ? '' + val : '')

/**
 * @desc 日期字符转换成季度格式（0215，0515，0815，1115）
 * @param date: 日期，string，yyyymmdd，以15结尾
 * @return yyyy第n季度
 */
export const convertDateToQuarter = (date: any) => {
    const labelFormat: any = {
        '02': '1',
        '05': '2',
        '08': '3',
        11: '4'
    }
    if (date && date?.length === 'yyyymmdd'.length && date?.endsWith('15')) {
        return `${date?.slice(0, 4)}年${labelFormat[date?.slice(4, 6)]}季度`
    }
    return ''
}

/**
 * @desc 日期字符转换成季度格式（0331，0630，0930，1231）
 * @param date: 日期，string，yyyymmdd，以月末结尾
 * @return yyyy第n季度
 */
export const convertDateToQuarter2 = (date: string) => {
    const labelFormat: any = {
        '03': '1',
        '06': '2',
        '09': '3',
        12: '4'
    }
    if (date && date.length === 'yyyymmdd'.length && (date.endsWith('30') || date.endsWith('31'))) {
        return `${date?.slice(0, 4)}年${labelFormat[date?.slice(4, 6)]}季度`
    }
    return ''
}

/**
 * @desc 根据数据条数获取颜色数据
 * @param len: 数据length
 * @param type: line | bar
 * @return formatColors(5) ["red1", "red2", "blue1", "blue2", "green1"]
 */
export const getColorsByLength = (len: number, type = 'bar') => {
    if (!len) {
        return []
    }
    const interval = Math.floor(len / 3)
    const fixNum = (len / 3).toFixed(2)

    // 区分折线面积图和柱状面积图的颜色
    const colors = type === 'bar' ? chartColors.stackBarColors : chartColors.stackLineColors

    return colors.reduce((acc, cur, idx) => {
        let range = interval
        if (!fixNum.endsWith('00')) {
            range = interval + 1
            if (idx === 1) {
                range = fixNum.endsWith('33') ? interval : interval + 1
            } else if (idx === 2) {
                range = interval
            }
        }

        return [...acc, ...cur.slice(0, range)]
    }, [])
}

/**
 * @desc 根据serieData获取y轴最大值最小值、间隔
 * @param serieData: []
 * @param otherParams: {
 *     leftIsStack: Boolean, // 左边轴是否是叠加图
 *     rightIsStack: Boolean, // 右边轴是否是叠加图
 *     leftRoundRatio: Number, // 左边轴取整的系数，默认10
 *     rightRoundRatio: Number, // 右边轴取整的系数，默认10
 *     leftSplitNumber: Number, // 左边轴分段的段数，默认5
 *     rightSplitNumber: Number, // 右边轴分段的段数，默认5
 *     customLeftMinVal: 0, // 自定义左边y轴最小值，未传递的话根据类目数据动态计算
 *     customLeftMaxVal: 0, // 自定义左边y轴最大值，未传递的话根据类目数据动态计算
 *     customRightMinVal: 0, // 自定义右边y轴最小值，未传递的话根据类目数据动态计算
 *     customRightMaxVal: 0 // 自定义右边y轴最大值，未传递的话根据类目数据动态计算
 * }
 * @return minMaxValues {
    left: {
        min: 0, // 左边y轴最小值
        max: 0, // 左边y轴最大值
        interval: 0 // 左边y轴间隔
    },
    right: {
        min: 0, // 右边y轴最小值
        max: 0, // 右边y轴最大值
        interval: 0 // 右边y轴间隔
    }
 }
 */
export const getYAxisMinMaxValues = (
    seriesData: any[],
    {
        leftIsStack = false,
        rightIsStack = false,
        leftRoundRatio = 10,
        rightRoundRatio = 10,
        leftSplitNumber = 5,
        rightSplitNumber = 5,
        customLeftMinVal = undefined,
        customLeftMaxVal = undefined,
        customRightMinVal = undefined,
        customRightMaxVal = undefined
    }: any
) => {
    if (!seriesData?.length) {
        return
    }

    const minMaxValues: any = {
        left: {
            min: 0, // 左边y轴最小值
            max: 0, // 左边y轴最大值
            interval: 0 // 左边y轴间隔
        },
        right: {
            min: 0, // 右边y轴最小值
            max: 0, // 右边y轴最大值
            interval: 0 // 右边y轴间隔
        }
    }

    const leftList = seriesData?.filter((item: any) => !item.yAxisIndex) ?? []
    const leftData =
        leftList.reduce((acc: any, cur: any) => {
            const curList = cur?.data?.filter((n: any) => getString(n) && n !== '-') ?? []
            return [...acc, ...curList]
        }, []) ?? []
    const rightList: any = seriesData
        ? seriesData.filter((item: any) => item?.yAxisIndex === 1)
        : []
    const rightData =
        rightList?.reduce((acc: any, cur: any) => {
            const curList = cur?.data?.filter((n: any) => getString(n) && n !== '-') ?? []
            return [...acc, ...curList]
        }, []) ?? []

    const leftMinVal = getString(customLeftMinVal) ? customLeftMinVal : Math.min(...leftData)
    const leftMaxVal = getString(customLeftMaxVal) ? customLeftMaxVal : Math.max(...leftData)

    const rightMinVal = getString(customRightMinVal) ? customRightMinVal : Math.min(...rightData)
    const rightMaxVal = getString(customRightMaxVal) ? customRightMaxVal : Math.max(...rightData)

    minMaxValues.left.min = (
        Math.floor(Number(leftMinVal) / leftRoundRatio) * leftRoundRatio
    ).toFixed(2)
    minMaxValues.left.max = (
        Math.ceil(Number(leftMaxVal) / leftRoundRatio) * leftRoundRatio
    ).toFixed(2)
    minMaxValues.left.interval = Math.ceil(
        (minMaxValues.left.max - minMaxValues.left.min) / leftSplitNumber
    )

    minMaxValues.right.min = (
        Math.floor(Number(rightMinVal) / rightRoundRatio) * rightRoundRatio
    ).toFixed(2)
    minMaxValues.right.max = (
        Math.ceil(Number(rightMaxVal) / rightRoundRatio) * rightRoundRatio
    ).toFixed(2)
    minMaxValues.right.interval = Math.ceil(
        (Number(minMaxValues.right.max) - minMaxValues.right.min) / rightSplitNumber
    )

    return minMaxValues
}

/**
 * 数字list相加，[1, 2, 3] => 6
 */
export const addNums = (arr: number[]) => {
    if (!arr?.length) {
        return 0
    }

    return arr.reduce((acc: any, cur: any) => {
        return acc + Number(cur)
    }, 0)
}

/**
 * 添加红涨绿跌类名
 */
export const addRedGreenClass = (val: any) => {
    if (!val || val === '0' || val === '0.00') {
        return ''
    }
    return val > 0 ? 'red2' : val < 0 ? 'green2' : ''
}

/**
 * 获取文本块的宽高
 * @param text 文本
 * @param fontSize 代表汉字的大小，英文字会自动按照默认值
 * @returns {{width: *, height: *}}
 */
export const getTxtBlockSize = (text: string, fontSize: any) => {
    const span = document.createElement('span')
    const result = {
        width: span.offsetWidth,
        height: span.offsetHeight
    }
    span.style.visibility = 'hidden'
    span.style.fontSize = fontSize || '14px'
    document.body.appendChild(span)

    if (typeof span.textContent !== 'undefined') {
        span.textContent = text || '国'
    } else {
        span.innerText = text || '国'
    }

    result.width = span.offsetWidth - result.width
    result.height = span.offsetHeight - result.height
    span?.parentNode?.removeChild(span)
    return result
}

// 判断对象是否为空
export const hasValueInObj = (obj: any) => {
    if (!obj) {
        return false
    }
    const arr = Object.keys(obj)
    return arr.length > 0
}

/**
 * 乘法
 * @param arg1
 * @param arg2
 */
export const digitMul = (arg1 = '', arg2 = '') => {
    let m = 0
    const s1 = arg1.toString()
    const s2 = arg2.toString()
    try {
        m += s1.split('.')[1].length
    } catch (e) {
        m = 0
    }
    try {
        m += s2.split('.')[1].length
    } catch (e) {
        m = 0
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
}

/**
 * 除法
 * @param arg1
 * @param arg2
 */
export const digitDiv = (arg1: any, arg2: any) => {
    let t1
    let t2
    let r1 = 0
    let r2 = 0
    try {
        t1 = arg1.toString().split('.')[1].length
    } catch (e) {
        t1 = 0
    }
    try {
        t2 = arg2.toString().split('.')[1].length
    } catch (e) {
        t2 = 0
    }

    r1 = Number(arg1.toString().replace('.', ''))
    r2 = Number(arg2.toString().replace('.', ''))
    return digitMul(String(r1 / r2), String(Math.pow(10, t2 - t1)))
}

/**
 * 计算图表legend的高度
 * @param legends <[aa, bb]> 图表条目
 * @param chartWidth <Number> 图表宽度
 * @param legendTop <Number> legend顶部距离图表的高度，默认25
 * @param legendBottom <Number> legend底部距离图表的高度
 */
export const calcLegendHeight = ({
    legends,
    chartWidth,
    legendTop = 25,
    legendBottom = 0
}: any) => {
    // legend条目的总宽度=每个条目的宽度总和
    const totalWidth = legends.reduce((acc: any, cur: any) => {
        // 每一个条目的宽度=条目字符数*字体大小+条目间隙
        const itemWidth = (cur?.length || 0) * 12 + 48
        return acc + itemWidth
    }, 0)
    // 行数
    const rowNum = Math.ceil(totalWidth / chartWidth)
    // legend高度=行数*每行文字高度+每行间隔（间隔=（行数-1）* 5）+legend距离图表的高度
    const legendHeight = rowNum * 12 + (rowNum - 1) * 5 + legendTop + legendBottom
    return {
        legendHeight,
        rowNum
    }
}
