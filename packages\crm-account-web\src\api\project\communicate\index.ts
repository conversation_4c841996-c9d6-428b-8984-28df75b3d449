import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import type {
    QueryAssetReportReq,
    AddCommunicateReq,
    QueryHealthReq,
    QueryUserReq
} from './type/apiType'

/**
 * 获取客户沟通记录新增页初始化数据
 * @param params 请求参数
 * @returns
 */
export function getVisitInit() {
    return axiosRequest(
        paramsMerge({
            url: '/customer/communicate/visitInit',
            method: 'post',
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * 查询拜访当日客户的健康情况
 * @param data 请求参数
 * @returns
 */
export function getHealth(data: QueryHealthReq) {
    return axiosRequest(
        paramsMerge({
            baseURL: window._msApiPrefix_report,
            url: '/api/report/health/detail/getCustMarketHealth',
            method: 'post',
            data: data,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * 查询IPS报告列表
 * @param data 请求参数
 * @returns
 */
export function queryAssetReport(data: QueryAssetReportReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/communicate/queryassetreport',
            method: 'post',
            data
        })
    )
}

/**
 * 用户搜索
 * @param data 请求参数
 * @returns
 */
export function searchUser(data: QueryUserReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/visitMinutes/searchUser',
            method: 'post',
            data
        })
    )
}

/**
 * 新增客户沟通记录
 * @param data 请求参数
 * @returns
 */
export function addCommunicate(data: AddCommunicateReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/communicate/add',
            method: 'post',
            data
        })
    )
}
