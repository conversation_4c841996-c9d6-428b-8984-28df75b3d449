/*
 * @Description: 固定路由配置
 * @Author: chao<PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 13:52:35
 * @FilePath: /crm-web/packages/crm-performance-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { name: 'stockList' },
        children: [
            {
                path: '/hkproduct/tradeinfo',
                name: 'stockList',
                meta: {
                    title: '存量分成'
                },
                component: () => import('@/views/performance/stockSplit/stockList.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
