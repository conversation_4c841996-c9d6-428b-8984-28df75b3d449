---
description: 
globs: 
alwaysApply: false
---
## 项目结构说明

```text
├── build                             # 编译和打包的相关配置
├── husky                             # git提交校验
├── federation                        # federation联邦配置 待扩展
├── common                            # 公共组件抽取
│   ├── api                           # 接口定义
│   ├── assets                        # 静态资源（img,css,font,icon）
│   ├── components                    # 可复用的直观组件(UI Components)
│   ├── config                        # 公共配置项
│   ├── constant                      # 全局常量
│   ├── types                         # 公共ts类
│   └── utils                         # 公共方法
├── packages                          # 多包容器
│   ├── crm-cli                       # cli工具 待扩展
│   ├── crm-ui                        # ui组件 待扩展
│   ├── crm-utils                     # utils 待扩展
│   ├── crm-wechat-web                # 企微项目
│   ├── crm-account-web               # 投顾客户项目
│   ├── crm-performance-web           # 绩效管理
│   └── crm-template                  # 包基础模版
│           ├─src                     # 程序源文件
│           │  ├── api                # 接口定义
│           │  ├── assets             # 静态资源（img,css,font,icon）
│           │  ├── constant           # 全局常量
│           │  ├── page               # 页面通用组件
│           │  ├── routers            # 路由配置
│           │  ├── stores             # pina数据状态管理
│           │  ├── views              # 页面
│           │  ├── types              # ts类
│           │  ├── App.vue            # 入口组件
│           │  └── main.ts            # 入口js
│           ├── components.d.ts       # 自动注册组件设置
│           ├── auto-imports.d.ts     # 自动导入组件设置
│           ├── .eslintrc-auto-import.d.ts # eslint自动导入组件设置
│           ├── vite.config.ts        # vite基础配置
│           ├── tsconfig.json         # ts配置
│           ├── tsconfig.node.json    # node ts配置
│           └── vite-env.d.ts         # vite自定义模块
├── package.json                      # node.js依赖配置
├── pnpm-workspace.yaml               # pnpm 公共空间
├── tsconfig.json                     # ts配置
├── tsconfig.node.json                # node ts配置
├── README.md                         # 项目说明文档
└── yarn.lock                         # 项目依赖的具体版本信息锁定文件 提交时需删除
```
