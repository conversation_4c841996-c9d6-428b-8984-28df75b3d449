/*
 * @Description: 自定义发送任务 ts类型定义
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:21:21
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-02 13:24:15
 * @FilePath: /crm-web/packages/crm-wechat-web/src/api/project/customSend/type/apiReqType.d.ts
 *
 */
export {}
declare module './apiReqType' {
    interface CustSendParams {
        /**
         * 导入类型 1-按投顾客户号
         */
        importType: string
        /**
         * 消息类型
         */
        msgType: string
        /**
         * 推送时间
         */
        pushTime?: string
        /**
         * 推送类型 1-只推送企微任务 2-均推送卡片 3-针对添加客户推任务，针对未添加客户推卡片消息
         */
        pushType: string
        /**
         * 序列号
         */
        sequenceNo: string
        /**
         * 内容
         */
        'cardMessageDTO.content'?: string
        /**
         * 链接地址
         */
        'cardMessageDTO.hrefUrl'?: string
        /**
         * 标题
         */
        'cardMessageDTO.title'?: string
        /**
         * 内容
         */
        'wechatTaskDTO.content'?: string
        /**
         * 链接描述
         */
        'wechatTaskDTO.hrefDesc'?: string
        /**
         * 链接标题
         */
        'wechatTaskDTO.hrefTitle'?: string
        /**
         * 链接地址
         */
        'wechatTaskDTO.hrefUrl'?: string
        /**
         * 标题
         */
        'wechatTaskDTO.title'?: string
    }
    export { CustSendParams }
}
