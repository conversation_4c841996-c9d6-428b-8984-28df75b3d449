import Plugin from '@ckeditor/ckeditor5-core/src/plugin'
import ButtonView from '@ckeditor/ckeditor5-ui/src/button/buttonview'
import ChartIcon from './images/chart.svg'
const IconStr = 'insertChart'

export default class InsertChart extends Plugin {
    init() {
        const editor = this.editor
        const config = editor.config.get('insertChart')
        editor.ui.componentFactory.add(IconStr, locale => {
            // const command = editor.commands.get(IconStr)

            const view = new ButtonView(locale)

            view.set({
                label: '插入图表',
                icon: ChartIcon,
                tooltip: true,
                isToggleable: true,
                isEnabled: config.canInsert
            })
            view.bind('isOn', 'isEnabled')
            view.on('execute', () => {
                editor.config.get('insertChart').insertCb(config.canInsert, config)
            })

            return view
        })
    }
}
