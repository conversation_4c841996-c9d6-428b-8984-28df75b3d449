/*
 * @Description: 文件初始化
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-21 18:23:44
 * @FilePath: /crm-rpo-template/packages/crm-template/src/main.ts
 *
 */
import { createApp } from 'vue'
import '@common/assets/css/reset.less'
import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/message-box/style/css'
import 'element-plus/es/components/loading/style/css'
import 'element-plus/es/components/notification/style/css'
import '@common/assets/css/mixins.less'

import App from './App.vue'
import router from './routers/index'
import { createPinia } from 'pinia'
import VueDOMPurifyHTML from 'vue-dompurify-html'

// import 'default-prevent-events'
import 'virtual:svg-icons-register'
const app = createApp(App)

/*全局公共方法绑定到vue实例，调用方法：this.$$.oneMethod()*/
// import globalMethods from '@/utils/globalMethods' // 全局公共方法
// app.config.globalProperties.$$ = globalMethods
// app.config.globalProperties.$globalConstant = globalConstant // 全局常量，调用方法：this.$globalConstant.key
// app.config.globalProperties.$openUrl = openUrl
app.use(router)
app.use(createPinia())
app.use(VueDOMPurifyHTML)
app.mount('#app')
