<!--
 * @Description: button基础组件
 * @Author: chaohui.wu
 * @Date: 2023-04-04 18:52:50
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-13 18:43:09
 * @FilePath: /crm-web/common/components/moduleBase/BaseButton.vue
 * 
-->
<template>
    <el-button
        :class="[
            'base_button',
            { is_link: link },
            { no_under_line: noUnderLine },
            { button_radius: radius },
            { button_linear: linear },
            { button_linear_back: linearBack },
            {
                'btn-bold': bold
            },
            customClass
        ]"
        :style="btnStyle"
        :loading="loading || btnLoading"
        v-bind="$attrs"
        @click="btnClick"
    >
        <slot />
    </el-button>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    defineOptions({
        name: 'BaseButton'
    })

    const props = withDefaults(
        defineProps<{
            clickFunc?: Function
            loading?: boolean
            radius?: boolean
            linear?: boolean
            linearBack?: boolean
            bold?: boolean
            link?: boolean
            noUnderLine?: boolean
            linkColor?: string
            customClass?: string
        }>(),
        {
            clickFunc: undefined,
            loading: false,
            radius: false,
            linear: false,
            linearBack: false,
            bold: false,
            link: false,
            noUnderLine: false,
            linkColor: '@font_link',
            customClass: ''
        }
    )

    const btnStyle = computed(() => {
        const style: any = {}
        if (props.link) {
            style.color = props.linkColor
        }
        return style
    })

    const btnLoading = ref<boolean>(false)
    const btnClick = () => {
        if (typeof props.clickFunc === 'function') {
            btnLoading.value = true
            props
                .clickFunc()
                .then((res: any) => {
                    if (res) {
                        ElMessage({
                            message: res,
                            type: 'success',
                            duration: 2000
                        })
                    }
                    btnLoading.value = false
                })
                .catch((err: any) => {
                    if (err) {
                        ElMessage({
                            message: err,
                            type: 'warning',
                            duration: 2000
                        })
                    }
                    btnLoading.value = false
                })
        }
    }
</script>

<style lang="less" scoped>
    .base_button {
        &.el-button {
            color: #231815;
            background-color: transparent;
            border-radius: 0;

            &:active,
            .is-active {
                color: @theme_main;
                background-color: @bg_main_hover;
                border-color: @border_hover;
            }

            &:focus {
                background-color: #ffffff;
                border-color: @border_hover;
            }

            &:hover {
                color: @theme_main;
                background-color: @bg_main_hover;
                border-color: @bg_main_hover;
            }

            &.is-disabled {
                color: @font_color;
                background-color: #c1c1c9;
                border-color: #c1c1c9;
            }

            &.is-plain {
                &:hover {
                    color: #c82e30;
                    background-color: #fff7f7;
                    border-color: #ead8d8;
                }

                &:active,
                &.is-active {
                    color: #c82e30;
                    background-color: #f9e7e7;
                    border-color: #ead8d8;
                }

                &.is-disabled {
                    color: @font_color_03;
                    background-color: @font_color_01;
                    border-color: #e5e5e9;
                }
            }

            &:focus-visible {
                outline: 0;
            }

            &.button_radius {
                border-radius: 5px;
            }

            &.btn-bold {
                // 众口难调 到底是加粗不加粗
                font-weight: 700;
            }

            &--small {
                --el-button-size: 20px;
                // padding: 0 10px;
                .el-icon {
                    font-size: 10px;
                }
            }

            &--default {
                --el-button-size: 26px;

                .el-icon {
                    font-size: 16px;
                }
            }

            &.tinny-btn {
                box-sizing: border-box;
                height: 20px;
                padding: 3px 7px;
                font-size: 12px;
                line-height: 20px;
                border-radius: 4px;
            }

            &.small-btn {
                box-sizing: border-box;
                height: 24px;
                padding: 3px 7px;
                font-size: 12px;
                line-height: 24px;
                border-radius: 4px;
            }

            &.default-btn {
                box-sizing: border-box;
                height: 26px;
                padding: 3px 7px;
                font-size: 13px;
                line-height: 26px;
                border-radius: 3px;
            }

            &.btn {
                &--white {
                    @curFontColor: #d0021b;

                    color: @font_color_01;
                    background-color: @curFontColor;
                    border: 1px solid @curFontColor;
                }

                &--red {
                    @curFontColor: #d0021b;

                    color: @curFontColor;
                    border: 1px solid @curFontColor;

                    &_blank {
                        background-color: transparent;
                    }
                }

                &--gray {
                    @curFontColor: #71717b;

                    color: @curFontColor;
                    border: 1px solid @curFontColor;
                }
            }

            // 链接类按钮样式
            &.is_link {
                position: relative;
                padding: 0 5px;
                color: @font_link;
                background: transparent;
                border: none;
                border-radius: 0;

                &:hover {
                    color: @font_link;
                }

                &:hover::after {
                    position: absolute;
                    bottom: 2px;
                    left: 0;
                    width: 100%;
                    height: 1px;
                    content: '';
                    background-color: @font_link;
                }

                &.is-disabled {
                    color: @font_color_03 !important;

                    &:hover::after {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 0;
                        content: '';
                        background-color: @theme_main;
                    }
                }
                // 没有下划线的链接按钮
                &.no_under_line {
                    &:hover::after {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 0;
                        content: '';
                        background-color: @theme_main;
                    }
                }
            }
        }
    }
</style>
