<!--
 * @Description: 添加用户信息
 * @Author: chaohui.wu
 * @Date: 2023-09-12 15:22:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-02-18 16:29:35
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addCustom.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="970px"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        class-name="dialog-table"
        @keyup.enter.stop="handleClose"
    >
        <table-wrapper
            class-name="crm-dialog-table-wraper"
            search-btn-txt="添加"
            :show-operation-left="true"
            icon-type="add"
            @searchFn="addCustList"
        >
            <template #searchArea>
                <label-item label="客户所属投顾" col-type="w100">
                    <ReleatedSelect
                        ref="formerSelect"
                        v-model="queryForm.constObj"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCode"
                        :default-cons-code="consCode"
                    ></ReleatedSelect>
                </label-item>
                <label-item label="客户范围" col-type="w100">
                    <crm-checkbox
                        v-model="queryForm.customRangeLabel"
                        :option-list="customRangeList"
                        value-format="value"
                        all-txt="全部"
                        need-all
                        :default-all-checked="false"
                        :is-indeterminate="
                            handleCheckedChange(queryForm.customRangeLabel, customRangeList)
                        "
                        @checkAll="checkAllList"
                        @change="handleCheckedChange(queryForm.customRangeLabel, customRangeList)"
                    ></crm-checkbox>
                </label-item>
                <label-item label="客户分配原因" col-type="w100">
                    <crm-select
                        v-model="queryForm.reasonCode"
                        placeholder="请选择客户分配原因"
                        label-format="label"
                        value-format="key"
                        :option-list="CUST_REASON"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="客户原投顾" col-type="w100">
                    <ReleatedSelect
                        ref="formerSelect"
                        v-model="queryForm.formerConstObj"
                        :organization-list="formerOrganizationList"
                        :cons-list-default="formerConsultList"
                        :default-org-code="formerOrgCode"
                        :default-cons-code="formerConsCode"
                    ></ReleatedSelect>
                </label-item>
                <label-item label="选择客户" col-type="w100">
                    <el-select
                        v-model="queryForm.custNoList"
                        size="small"
                        multiple
                        filterable
                        clearable
                        remote
                        remote-show-suffix
                        reserve-keyword
                        collapse-tags
                        :max-collapse-tags="1"
                        :remote-method="remoteMethod"
                        placeholder="输入搜索"
                        :style="{ width: '180px' }"
                        :loading="loading"
                        @click="getDefaultOption"
                    >
                        <el-option v-if="showAll" label="全部" value="all" @click="selectAll" />
                        <el-option
                            v-for="(item, index) in custOptions"
                            :key="`option-tpl-${index}`"
                            :label="item.label"
                            :value="item.value"
                            class="cust-option"
                        />
                    </el-select>
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    size="small"
                    :radius="true"
                    :disabled="isDelateDisabled"
                    plain
                    @click="handleDelate"
                    >批量移除</crm-button
                >
            </template>
            <template #operationLeft>
                <p class="txt-cust">已选客户: {{ listData?.length ?? 0 }}个</p>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :columns="columnList"
                    :data="listData"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    :no-index="false"
                    :show-operation="false"
                    operation-width="200"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                </base-table>
            </template>
            <!-- <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[100]"
                    @change="handleCurrentChange"
                />
            </template> -->
        </table-wrapper>
        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="handleClose"
                    >关 闭</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn">确 认</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import {
        deepClone,
        fetchRes,
        message,
        excludeArr,
        addUnit,
        formatTableValue,
        dateTrans
    } from '@common/utils/index'
    import { useVisible } from '../scripts/hooks/useVisible'
    import ReleatedSelect from './releatedSelect.vue'
    import CustDetail from './custDetail.vue'
    import { queryCustInfo } from '@/api/project/stockSplit/stockSplitList'

    import { CUST_REASON } from '@/constant/index'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            consultList?: any[]
            orgCode?: string
            consCode?: string
            formerOrgCode?: string
            formerConsCode?: string
            organizationList?: any[]
            formerOrganizationList?: any[]
            formerConsultList?: any[]
            transData?: {
                title: string
                type: string
                id: string
            }
        }>(),
        {
            visibleCus: false,
            orgCode: '0',
            consCode: '',
            formerOrgCode: '0',
            formerConsCode: '',
            consultList: () => [],
            organizationList: () => [],
            formerOrganizationList: () => [],
            formerConsultList: () => [],
            transData: () => {
                return {
                    title: '添加客户',
                    type: 'add',
                    id: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'custCallBack', params: { val: string[]; optionList: object[] }): void
    }>()

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        id = ''
        constObj = {
            orgCode: '',
            consCode: ''
        }
        formerConstObj = {
            orgCode: '',
            consCode: ''
        }
        formerConsCode = ''
        formerOrgCode = ''
        newlyConsCode = ''
        newlyOrgCode = ''
        reasonCode = ''
        gdcjlabel = ''
        lsclLabel = ''
        custNoList = []
        customRangeLabel = ['gdcjlabel', 'lsclLabel']
    }
    const queryForm = ref(new QueryForm())

    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    /**
     * @description: 多选组件
     * @param {*} curCheckedList
     * @param {*} checkList
     * @return {*}
     */
    const handleCheckedChange = (curCheckedList: any[], checkList: object[]): boolean => {
        if (curCheckedList.length > 0 && curCheckedList.length < checkList.length) {
            return true
        }
        return false
    }
    const checkAllList = (val: []) => {
        return (queryForm.value.customRangeLabel = val)
    }

    const listData = ref<any>([])
    /**
     * @description: 新增客户数据
     * @return {*}
     */
    const addCustList = () => {
        //筛选当前选中数据
        const { custNoList }: any = queryForm.value || { custNoList: [] }
        const currentList = custOptions.value?.filter((item: any) =>
            custNoList.includes(item.value)
        )
        const arrTpl = [...currentList, ...listData.value]
        const mapObj = new Map()
        listData.value = arrTpl.filter(item => {
            if (!mapObj.has(item.value)) {
                mapObj.set(item.value, item)
                return true
            }
        })
        // 重置选择客户选项
        queryForm.value.custNoList = []
    }

    const customRangeList = ref([
        {
            value: 'gdcjlabel',
            label: '高端成交'
        },
        {
            value: 'lsclLabel',
            label: '公募存量(1万+)'
        }
    ])

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const columnList = computed(() => {
        return [
            {
                key: 'custNo',
                label: '投顾客户号',
                minWidth: 100,
                formatter: formatTableValue
            },
            {
                key: 'custName',
                label: '客户姓名',
                minWidth: 100,
                formatter: formatTableValue
            },
            {
                key: 'retailAmount',
                label: '公募存量',
                minWidth: 100,
                formatter: (a: any, b: any, c: any) =>
                    addUnit({ val: c, fixed: 0, unit: '', blankPlaceholder: '-' })
            },
            {
                key: 'premiumAmount',
                label: '私募存量',
                minWidth: 100,
                formatter: (a: any, b: any, c: any) =>
                    addUnit({ val: c, fixed: 0, unit: '', blankPlaceholder: '-' })
            }
        ]
    })

    /**
     * @description: 获取用户信息
     * @param {*} query
     * @return {*}
     */
    const custOptions = ref<{ label: string; value: string }[]>([])
    const loading = ref(false)
    const getCustInfo = ({ query = '' }) => {
        const { constObj, formerConstObj, reasonCode, gdcjlabel, lsclLabel, customRangeLabel } =
            queryForm.value || {}
        const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj || {}
        const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj || {}
        loading.value = true
        fetchRes(
            queryCustInfo({
                newlyConsCode,
                newlyOrgCode,
                formerConsCode,
                formerOrgCode,
                fuzzyTerm: query,
                reasonCode,
                gdcjlabel: customRangeLabel?.includes('gdcjlabel') ? '1' : '0',
                lsclLabel: customRangeLabel?.includes('lsclLabel') ? '1' : '0'
            }),
            {
                successCB: (res: any) => {
                    // 请求成功
                    const { rows } = res || { rows: [] }
                    const custList = rows.map((item: any) => {
                        return {
                            ...item,
                            label: `${item.custName} ${item.premiumAmount} ${item.retailAmount} `,
                            value: item.custNo
                        }
                    })
                    custOptions.value = custList
                    loading.value = false
                },
                errorCB: () => {
                    custOptions.value = []
                    loading.value = false
                },
                catchCB: () => {
                    custOptions.value = []
                    loading.value = false
                },
                successTxt: '',
                failTxt: '获取客户信息失败!',
                fetchKey: ''
            }
        )
    }

    /**
     * @description: 获取客户下拉列表初始化数据
     * @return {*}
     */
    const showAll = ref(false)
    const getDefaultOption = () => {
        //
        const { constObj, formerConstObj, reasonCode, gdcjlabel, lsclLabel, customRangeLabel } =
            queryForm.value || {}
        const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj || {}
        const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj || {}
        showAll.value = false
        fetchRes(
            queryCustInfo({
                newlyConsCode,
                newlyOrgCode,
                formerConsCode,
                formerOrgCode,
                fuzzyTerm: '',
                reasonCode,
                gdcjlabel: customRangeLabel?.includes('gdcjlabel') ? '1' : '0',
                lsclLabel: customRangeLabel?.includes('lsclLabel') ? '1' : '0'
            }),
            {
                successCB: (res: any) => {
                    // 请求成功
                    const { rows } = res || { rows: [] }
                    const custList = rows.map((item: any) => {
                        return {
                            ...item,
                            label: `${item.custName} ${item.premiumAmount} ${item.retailAmount} `,
                            value: item.custNo
                        }
                    })
                    if (custList?.length > 0) {
                        showAll.value = true
                    }
                    custOptions.value = custList
                },
                errorCB: () => {
                    custOptions.value = []
                },
                catchCB: () => {
                    custOptions.value = []
                },
                successTxt: '',
                failTxt: '获取客户信息失败!',
                fetchKey: ''
            }
        )
    }
    // 树形下拉列表选中全部
    const selectAll = () => {
        const { custNoList }: any = queryForm.value || { custNoList: [] }
        //将当前下拉框的数据填充到客户选中中
        const allStrList = custOptions.value.map((item: any) => item.value)
        // 合并去重复
        const listTpl: any = Array.from(new Set([...allStrList, ...custNoList]))
        queryForm.value.custNoList = listTpl.filter((item: any) => item !== 'all')
    }

    /**
     * @description: table表格选中数据
     * @param {*} sel
     * @param {*} row
     * @return {*}
     */
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: any[], row: object): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel).map((item: any) => {
            return item.custNo
        })
    }

    /**
     * @description: 删除
     * @return {*}
     */
    const handleDelate = (): void => {
        // 获取选中投顾客户号
        listData.value = listData.value.filter(
            (item: any) => !tableSelectList.value.includes(item.custNo)
        )
        // table选项置空
        tableSelectList.value = []
    }

    /**
     * @description: 删除按钮联动
     * @param {*} computed
     * @return {*}
     */
    const isDelateDisabled = computed(() => tableSelectList.value?.length === 0)

    /**
     * @description: 远程方法
     * @param {*} query
     * @return {*}
     */
    const remoteMethod = (query: string) => {
        if (query) {
            getCustInfo({ query })
        }
    }

    /**
     * @description: 确定回填
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = () => {
        // 确定后回填数据
        const listTpl = listData?.value?.map((item: any) => item.custNo)
        message({
            type: 'success',
            message: '添加客户成功'
        })
        // 写法变更，兼容当数据量过大时数据更新不及时导致的页面报错
        handleClose()
        emit('custCallBack', { val: listTpl, optionList: unref(listData.value) })
    }

    onMounted(() => {
        // 初始化
        queryForm.value.constObj.consCode = props?.consCode
        queryForm.value.constObj.orgCode = props?.orgCode
    })
</script>

<style lang="less" scoped>
    .is-no-data {
        padding: 15vh 0;
        text-align: center;
    }

    .txt-cust {
        padding-left: 15px;
    }

    .txt-red {
        margin-left: 15px;
        font-size: 12px;
        color: red;
    }

    .cust-list-box {
        display: flex;
        align-items: center;
    }

    :deep(.el-select__tags) {
        .el-tag__content {
            max-width: 28px;
            overflow: hidden;
        }
    }

    .search-box {
        width: 40px;
        height: 24px;
        padding-left: 15px;
        font-size: 18px;
        color: @font_link_more;
        cursor: pointer;
    }

    .crm_dialog_form {
        width: 100%;
        height: 100%;

        .el-form-item {
            margin-bottom: 10px;

            :deep(.el-input-number) {
                text-align: left;
            }
        }

        :deep(.el-input) {
            .el-input__wrapper {
                padding-left: 10px;

                .el-input__inner {
                    text-align: left;
                }
            }
        }
    }
</style>
