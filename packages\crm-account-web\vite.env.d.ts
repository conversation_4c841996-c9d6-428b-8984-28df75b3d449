/*
 * @Description:
 * @Author: chao<PERSON>.wu
 * @Date: 2023-07-25 18:59:48
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 16:23:36
 * @FilePath: /crm-web/packages/crm-wechat-web/vite.env.d.ts
 *
 */

declare module '*.vue' {
    import { DefineComponent } from 'vue'
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
    const component: DefineComponent<{}, {}, any>
    export default component
}

declare module 'js-cookie'
// 环境变量 TypeScript的智能提示
interface ImportMetaEnv {
    VITE_APP_TITLE: string
    VITE_APP_PORT: string
    VITE_APP_BASE_API: string
}

interface ImportMeta {
    readonly env: ImportMetaEnv
}
