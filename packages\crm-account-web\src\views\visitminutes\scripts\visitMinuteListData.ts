/**
 * @Description: 拜访记录列表数据
 * @Author: auto-generated
 * @Date: 2023-11-13
 */
import { queryAuthOrgTree, queryAllOrgTree } from '@/api/project/visitminutes/index'
import { makeElementTree, arrayToTree, fetchSync } from '@common/utils/index'

export const useVisitMinuteListData = defineStore('visitMinuteListStore', {
    state: () => {
        return {
            organizationList: [], // 组织架构
            consultList: [], // 投顾列表
            orgCodeDefault: '', // 默认组织架构
            consCodeDefault: '', // 默认投顾
            formerOrgCodeDefault: '',
            formerConsCodeDefault: '',
            formerOrganizationList: [],
            formerConsultList: []
        }
    },
    actions: {
        /**
         * @description: 设置公共数据
         * @param {*} data
         * @return {*}
         */
        setCommonData({
            organizationList,
            consultList,
            formerConsultList,
            formerOrganizationList
        }: any) {
            if (organizationList) {
                this.organizationList = organizationList
            }
            if (consultList) {
                this.consultList = consultList
            }
            if (formerOrganizationList) {
                this.formerOrganizationList = formerOrganizationList
            }
            if (formerConsultList) {
                this.formerConsultList = formerConsultList
            }
        },
        fetchPageInit(type = '') {
            if (type === 'all') {
                fetchSync(queryAuthOrgTree({ module: '020153', notAuthor: '1' }), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        // 获取组织架构数据
                        const consListTpl =
                            consultList?.length > 1
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        // 将数据存储到pinia
                        this.setCommonData({
                            organizationList: arrayToTree(organizationList),
                            consultList: consListTpl
                        })
                        this.orgCodeDefault = organizationList[0].orgCode ?? ''
                        this.consCodeDefault = consListTpl[0].consCode ?? ''
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            } else {
                fetchSync(queryAllOrgTree({}), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        const consListTpl =
                            consultList?.length > 0
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        // 将数据存储到pinia
                        this.setCommonData({
                            formerOrganizationList: makeElementTree({
                                pid,
                                list: organizationList,
                                pidFiled: 'parentOrgCode',
                                labelFiled: 'orgName',
                                valueFiled: 'orgCode'
                            }),
                            formerConsultList: consListTpl
                        })
                        this.formerOrgCodeDefault = organizationList[0]?.orgCode ?? ''
                        this.formerConsCodeDefault = consListTpl[0]?.consCode ?? ''
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            }
        },
        getPageInit() {
            return Promise.all([this.fetchPageInit(), this.fetchPageInit('all')])
        }
    }
})
