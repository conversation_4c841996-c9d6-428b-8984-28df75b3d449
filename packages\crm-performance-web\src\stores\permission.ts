/*
 * @Description: 权限管理
 * @Author: chaohui.wu
 * @Date: 2023-09-14 13:02:33
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 13:11:50
 * @FilePath: /crm-web/packages/crm-performance-web/src/stores/permission.ts
 *
 */
import { fetchRes, fetchAll, fetchSync, messageBox, dateTrans } from '@common/utils/index'
import { getMenuPermission } from '@/api/base/menuRoles'
import { STOCK_SPLIT_PERMISSION } from '@/constant/index'
import { CGIRes } from '@/types'
import { defineStore } from 'pinia'

export const usePermission = defineStore('permissionList', {
    state: () => {
        return {
            permissionArr: []
        }
    },
    getters: {
        isPremission: state => {
            return (key: string) => {
                return state.permissionArr.some(
                    (item: any) =>
                        item?.operateCode === STOCK_SPLIT_PERMISSION.get(key) &&
                        item?.display === '1'
                )
            }
        }
    },
    actions: {
        getMenuRoles(params: { menuCode: string }) {
            fetchRes(getMenuPermission(params), {
                successCB: (res: any) => {
                    const { rows } = res || { rows: [] }
                    this.permissionArr = rows
                },
                errorCB: () => {
                    this.permissionArr = []
                },
                catchCB: () => {
                    this.permissionArr = []
                },
                successTxt: '',
                failTxt: ''
            })
        }
    }
})
