<!--
 * @Description: 增量分成列表页
 * @Author: chaohui.wu
 * @Date: 2023-03-16 11:15:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-20 16:31:57
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/stockList.vue
 * @notice: 后期需要抽取hooks
-->
<template>
    <div class="report-list-module">
        <table-wrapper class-name="crm_wraper" @searchFn="queryList">
            <template #searchArea>
                <label-item label="客户姓名">
                    <crm-input
                        v-model="queryForm.custName"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="客户编号">
                    <crm-input
                        v-model="queryForm.custNo"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="一账通号">
                    <crm-input
                        v-model="queryForm.hboneNo"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="证件号">
                    <crm-input
                        v-model="queryForm.idNo"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <!-- 待补充产品名称搜索控件，搜索加下拉 -->
                <label-item label="产品名称">
                    <crm-select
                        v-model="queryForm.productCode"
                        placeholder="请选择产品"
                        label-format="fundName"
                        value-format="fundCode"
                        filterable
                        clearable
                        remote
                        remote-show-suffix
                        :remote-method="queryFundList"
                        :loading="clientNameLoading"
                        :option-list="fundList"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="交易状态">
                    <crm-select
                        v-model="queryForm.tradeStatus"
                        placeholder="请选择交易状态"
                        label-format="label"
                        value-format="key"
                        :option-list="TRADE_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="预约状态">
                    <crm-select
                        v-model="queryForm.orderStatus"
                        placeholder="请选择签约状态"
                        label-format="label"
                        value-format="key"
                        :option-list="ORDER_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="打款状态">
                    <crm-select
                        v-model="queryForm.payStatus"
                        placeholder="请选择打款状态"
                        label-format="label"
                        value-format="key"
                        :option-list="PAY_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="预计交易日期">
                    <date-range
                        v-model="queryForm.tradeDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
                <label-item label="交易类型">
                    <crm-select
                        v-model="queryForm.tradeType"
                        placeholder="请选择交易类型"
                        label-format="label"
                        value-format="key"
                        :option-list="TRADE_TYPE"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="是否线上签约">
                    <crm-select
                        v-model="queryForm.isOnlineSign"
                        label-format="label"
                        value-format="key"
                        :option-list="IS_ONLINE_SIGN"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="签约状态">
                    <crm-select
                        v-model="queryForm.signStatus"
                        label-format="label"
                        value-format="key"
                        :option-list="SIGN_STATUS"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="签约时间">
                    <date-range
                        v-model="queryForm.signDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button size="small" :radius="true" plain @click="resetQueryForm"
                    >清空</crm-button
                >
                <crm-button size="small" :radius="true" :icon="Download" plain @click="handleExport"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :columns="stockListTableColumn"
                    :data="tableList"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="80"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                    <template #operation="{ scope }">
                        <el-button
                            size="small"
                            :text="true"
                            link
                            @click="
                                handleShow(
                                    scope.row.id,
                                    scope.row.custNo,
                                    scope.row.signStatus,
                                    'audt'
                                )
                            "
                            >查看</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[20, 50]"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>
        <ExplainStock v-model="explainDialogVisiable"></ExplainStock>
        <TradeInfo
            v-if="addStockVisiable"
            v-model="addStockVisiable"
            :trans-data="stockObj"
            @callBack="queryList"
        ></TradeInfo>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Upload, Download, RefreshLeft, Delete } from '@element-plus/icons-vue'
    import ExplainStock from './components/explainStock.vue'
    import TradeInfo from './components/tradeInfo.vue'
    import { getFundList, queryHktradeConfigList } from '@/api/project/stockSplit/stockSplitList'
    import { downloadFile, message, excludeArr } from '@common/utils/index'
    import {
        TRADE_STATUS,
        ORDER_STATUS,
        TRADE_TYPE,
        PAY_STATUS,
        IS_ONLINE_SIGN,
        SIGN_STATUS
    } from '@/constant/index'
    import { ElMessage } from 'element-plus'
    import { HkTradeInfoVo } from '@/types/tableColumn'
    import { fetchRes, messageBox } from '@common/utils/index'
    import { stockListTableColumn, showTableColumn } from './scripts/tableData'
    import { useStockListData } from './scripts/stockListData'
    import { useTableList } from './scripts/hooks/useTableList'
    // 投顾管理层
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const { orgCodeDefault, consCodeDefault, formerOrgCodeDefault, formerConsCodeDefault } =
        storeToRefs(stockListStore)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        custName = ''
        custNo = ''
        // formerConsCode = ''
        // formerOrgCode = '''
        hboneNo = ''
        idNo = ''
        productCode = ''
        tradeStatus = ''
        orderStatus = '12'
        payStatus = ''
        tradeType = ''
        isOnlineSign = ''
        signStatus = ''
        signDt = {
            startDate: '',
            endDate: ''
        }
        tradeDt = {
            startDate: '',
            endDate: ''
        }
        signStartDt = ''
        signEndDt = ''
        payStartDt = ''
        payEndDt = ''
    }

    /**
     * @description: 列表传参
     * @return {*}
     */
    const fetchListParams = () => {
        const { signDt, tradeDt } = queryForm.value || {}
        const { startDate: signStartDt, endDate: signEndDt } = signDt
        const { startDate: payStartDt, endDate: payEndDt } = tradeDt
        // 处理请求接口数据
        return {
            ...pageObj.value,
            ...queryForm.value,
            signStartDt,
            signEndDt,
            payStartDt,
            payEndDt
        }
    }

    // hooks
    const { tableList, pageObj, queryForm, queryList, handleCurrentChange, handleReset } =
        useTableList({
            fetchListParams, // 接口参数
            fetchList: queryHktradeConfigList, // table列表查询接口ß
            queryFormParams: QueryForm
        })

    /**
     * @description: 重置
     * @param {*} void
     * @return {*}
     */
    const resetQueryForm = (): void => {
        handleReset(() => {})
    }

    /**
     * @description: 基于条件导出
     * @return {*}
     */
    const handleExport = () => {
        const tradeIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })

        const { signDt, tradeDt } = queryForm.value || {}
        const { startDate: signStartDt, endDate: signEndDt } = signDt
        const { startDate: payStartDt, endDate: payEndDt } = tradeDt
        downloadFile(
            {
                url: '/dtmsorder/hkproduct/export',
                method: 'post',
                loadingParams: {
                    isCust: true
                },
                data: {
                    ...pageObj.value,
                    ...queryForm.value,
                    signStartDt,
                    signEndDt,
                    payStartDt,
                    payEndDt,
                    tradeIdList
                }
            },
            (val: string) => true,
            (val: string) => {
                // 成功回调
                message({
                    type: 'success',
                    message: val || '导出成功'
                })
            }
        )
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault, formerOrgCodeDefault, formerConsCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.value.constObj.orgCode = orgCodeDefault.value
                queryForm.value.constObj.consCode = consCodeDefault.value
            }
            if (formerOrgCodeDefault.value || formerConsCodeDefault.value) {
                queryForm.value.formerConstObj.orgCode = formerOrgCodeDefault.value
                queryForm.value.formerConstObj.consCode = formerConsCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const stockObj = ref({
        title: '',
        id: '',
        type: '',
        custNo: ''
    })

    /**
     * @description: 新增/编辑/审核弹框
     * @return {*}
     */
    const addStockVisiable = ref<boolean>(false)

    const handleShow = (val: any, val1: any, val2: any, type = 'add'): void => {
        if (val2 !== '1') {
            ElMessage({
                message: '该笔交易未签约，请完成签约后查看详情',
                showClose: true,
                type: 'error'
            })
        } else {
            switch (type) {
                case 'audt':
                    stockObj.value = {
                        title: '好买香港产品-交易单',
                        id: val,
                        custNo: val1,
                        type: 'audt'
                    }
                    break
                default:
                    break
            }
            addStockVisiable.value = true
        }
    }

    /**
     * @description: table表格选中数据
     * @param {*} sel
     * @param {*} row
     * @return {*}
     */
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: HkTradeInfoVo[]): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                return item
            })
            .filter(item => item)
    }

    /**
     * 调用获取基金的接口
     */
    const clientNameLoading = ref<boolean>(false)
    const fundList = ref<object[]>()
    const dialogVisible = ref<boolean>(false)
    const queryFundList = async (query: string) => {
        const queryFormTpl = {
            fundCode: query,
            pageNo: 1,
            pageSize: 10
        }
        if (queryFormTpl.fundCode !== '') {
            fetchRes(getFundList(queryFormTpl), {
                successCB: (res: any) => {
                    const { fundVOList } = res
                    // 获取待筛选的基金数据
                    fundList.value = fundVOList
                },
                errorCB: (res: any) => {
                    dialogVisible.value = false
                    ElMessage({
                        type: 'error',
                        message: res?.description || '请求失败'
                    })
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        }
    }

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    /**
     * @description 提示
     */
    onMounted(() => {
        getPageInit()
        // 获取当前页面权限
        // getMenuRoles({ menuCode })
    })
</script>
<style lang="less" scoped></style>
