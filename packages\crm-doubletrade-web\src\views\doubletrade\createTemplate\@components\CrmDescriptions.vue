<template>
    <el-descriptions :title="tabname" :column="columns" border class="container">
        <el-descriptions-item v-if="!!propsData.isEdit" label="模版ID">
            <CrmInput
                v-model="formData.id"
                placeholder="请输入模版ID"
                :disabled="true"
                style="width: 240px"
            />
        </el-descriptions-item>

        <el-descriptions-item label="模板名称">
            <CrmInput
                v-model="formData.tempName"
                placeholder="请输入模版名称"
                style="width: 240px"
                :disabled="readonly"
            />
        </el-descriptions-item>

        <el-descriptions-item label="客户类型">
            <CrmSelect
                v-model="formData.custType"
                :option-list="custTypeOptions"
                placeholder="请选择客户类型"
                style="width: 240px"
                :disabled="readonly"
            />
        </el-descriptions-item>

        <el-descriptions-item label="成单方式">
            <CrmSelect
                v-model="formData.preType"
                :option-list="preTypeoptions"
                placeholder="请选择成单方式"
                style="width: 240px"
                :disabled="readonly"
            />
        </el-descriptions-item>

        <el-descriptions-item label="模版适用类型">
            <div class="temp-type-container">
                <CrmRadio
                    v-model="formData.tempType"
                    :option-list="tempTypeOptions"
                    :disabled="readonly"
                    @change="handleTempType"
                />

                <div v-if="formData.tempType === '1'">
                    <CrmSearchCode v-show="!readonly" v-model="formData.searchCode" />
                    <div class="download-content">
                        <el-upload
                            v-show="!readonly"
                            v-model:file-list="formData.uploadParams"
                            :on-success="handleUploadSuccess"
                            method="post"
                            :on-error="handleUploadError"
                            class="upload-demo"
                            :action="handleUpload('/doubletrade/template/uploadfundcodes')"
                            :limit="1"
                        >
                            <el-button
                                :disabled="formData.uploadParams?.length > 0"
                                type="primary"
                                size="small"
                                >上传文件</el-button
                            >
                        </el-upload>

                        <a
                            v-show="!readonly"
                            href="https://static.howbuy.com/crm-product/product/samplefile/template/%E5%8F%8C%E5%BD%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx"
                            class="download-excel"
                            download
                            >下载模版</a
                        >
                        <el-button
                            type="primary"
                            size="small"
                            @click="showFundDialogVisible = true"
                        >
                            查看
                        </el-button>
                        <crm-dialog
                            v-model="showFundDialogVisible"
                            width="350px"
                            title="查看基金代码"
                            :slot-list="['default', 'footer']"
                            :close-on-click-modal="false"
                        >
                            <el-table :data="fundCodeArr" style="width: 100%">
                                <el-table-column prop="fundcode" label="基金代码" width="200" />
                                <el-table-column v-show="!readonly" label="操作" width="100">
                                    <template #default="scope">
                                        <el-button
                                            v-show="!readonly"
                                            type="text"
                                            size="small"
                                            @click="handleDeleteFundCode(scope.row)"
                                            >删除</el-button
                                        >
                                    </template>
                                </el-table-column>
                            </el-table>
                            <template #footer>
                                <div class="dialog-footer">
                                    <crm-button
                                        type="primary"
                                        plain
                                        size="small"
                                        @click="showFundDialogVisible = false"
                                        >关 闭</crm-button
                                    >
                                </div>
                            </template>
                        </crm-dialog>
                    </div>
                </div>

                <div v-else>
                    <!-- 产品风险等级 -->
                    <div class="risk-level">
                        <span class="risk-level-label">产品风险等级</span>
                        <CrmCheckbox
                            v-model="formData.proRiskLevel"
                            :need-all="false"
                            :option-list="proRiskLevelOptions"
                            :disabled="readonly"
                            @check="handleRiskLevel"
                        />
                    </div>
                    <div class="productype">
                        <span class="productype-label">产品类型</span>
                        <CrmRadio
                            v-model="formData.proType"
                            :disabled="readonly"
                            :option-list="productypeOptions"
                        />
                    </div>
                    <!-- 产品类型 -->
                </div>
                <div v-show="showProRangeBtn">
                    <ViewProductMoal :props-data="propsDataSub" />
                </div>
            </div>
        </el-descriptions-item>
    </el-descriptions>
</template>

<script setup lang="ts">
    import { computed, onMounted, defineExpose, ref } from 'vue'
    import {
        custTypeOptions,
        preTypeoptions,
        tempTypeOptions,
        proRiskLevelOptions,
        productypeOptions,
        tableColumn
    } from '../config/templateConst'
    import ViewProductMoal from './ViewProductMoal.vue'
    import CrmSearchCode from './CrmSearchCode.vue'
    import { ElMessage } from 'element-plus'
    // eslint-disable-next-line vue/require-prop-types
    const props = defineProps(['propsData'])
    console.log('%c Line:96 🍅 props', 'color:#42b983', props.propsData.isEdit)
    const showFundDialogVisible = ref<boolean>(false)
    const readonly = computed(() => {
        return props.propsData.type === 'show' || props.propsData.type === 'check'
    })
    const showProRangeBtn = computed(() => {
        return formData.value.tempType === '2'
    })
    // eslint-disable-next-line vue/return-in-computed-property
    const tabname = computed(() => {
        if (props.propsData.type === 'add') {
            return '新增双录模版'
        }
        if (props.propsData.type === 'show') {
            return '查看双录模版'
        }
        if (props.propsData.type === 'check') {
            return '审核双录模版'
        }
        if (props.propsData.type === 'update') {
            return '修改双录模版'
        }
    })

    const formData = ref<any>({
        id: null, //模板id
        tempName: null, //模板名称
        preType: null, // 成单方式
        tempType: '1', // 模版适用类型
        custType: null, //客户类型
        uploadParams: [
            // {
            //     name: 'element-plus-logo.svg',
            //     url: 'https://element-plus.org/images/element-plus-logo.svg'
            // }
        ], //上传参数
        proRiskLevel: [], //产品级别
        proType: null, //产品类型
        searchCode: null, //搜索的产品代码
        fundCodes: []
    })

    const proRiskLevel = computed(() => formData.value.proRiskLevel)
    const proType = computed(() => formData.value.proType)
    const tempType = computed(() => formData.value.tempType)
    const propsDataSub = ref({
        proRiskLevel,
        proType,
        tempType
    })
    const uploadCode = ref<any>([]) //上传之后解析的code

    // 产品代码集合
    const fundCodeList = computed(() => {
        return [
            ...(uploadCode.value?.map((item: any) => item.fundcode) || []),
            formData.value.searchCode,
            ...formData.value.fundCodes
        ]
    }) //产品代码和上传产品集合
    const fundCodeArr = computed(() => {
        const arr = fundCodeList.value.filter(item => item)
        return Array.from(new Set(arr)).map((item: any) => {
            return {
                fundcode: item
            }
        })
    })
    const loading = ref(false)
    const productOptions = ref([])

    // 表格列
    const columns = computed(() => {
        return props.propsData.isEdit ? 2 : 3
    })

    // 切换适用模板类型
    function handleTempType(value: any) {
        // 清空
        if (value === '2') {
            // 产品代码，上传文件
            formData.value = {
                ...formData.value,
                searchCode: null,
                uploadParams: []
            }
        } else {
            // 风险等级，产品类型
            formData.value = {
                ...formData.value,
                proRiskLevel: [],
                proType: null
            }
        }
    }

    // 获取当前选中的产品风险等级
    function handleRiskLevel(status: any, value: any) {
        console.log('%c Line:137 🍩 value', 'color:#93c0a4', value)
        console.log('%c Line:137 🍏 status', 'color:#e41a6a', status)
    }

    // onMounted(() => {
    //     document.querySelector('.el-icon--close-tip').innerHTML = ''
    // })

    // 上传文件成功
    function handleUploadSuccess(res: any) {
        if (res.code === 'C010000') {
            uploadCode.value = res.data.list
            ElMessage({
                message: '导入成功！',
                showClose: true,
                type: 'success'
            })
        } else if (res.code === 'C011099') {
            // 空数据
            ElMessage({
                message: res.description,
                showClose: true,
                type: 'error'
            })
        } else if (res.code === 'C011100') {
            // 数据超过3000条
            ElMessage({
                message: '一次最多可导入3千条产品数据，超过请分次导入',
                showClose: true,
                type: 'error'
            })
        } else if (res.code === 'C011101') {
            // 将res.data.list元素中的fundcode字段按,拼接成字符串
            const fundcodeStr = res.data.list.map((item: any) => item.fundcode).join(',')
            // 存在异常数据
            ElMessage({
                message: '导入失败!存在产品' + fundcodeStr + '不适用于双录',
                showClose: true,
                type: 'error'
            })
        } else {
            ElMessage({
                message: '未知异常',
                showClose: true,
                type: 'error'
            })
        }
    }

    function handleDeleteFundCode(row: any) {
        uploadCode.value = uploadCode.value?.filter(
            (item: any) => item.fundcode.toString() !== row.fundcode.toString()
        )
        if (formData.value.searchCode === row.fundcode) {
            formData.value.searchCode = null
        }
        const index = formData.value.fundCodes.indexOf(row.fundcode)
        if (index !== -1) {
            formData.value.fundCodes.splice(index, 1)
        }
    }

    /**
     * 上传
     * @param url
     */
    function handleUpload(url: string) {
        return window._msApiPrefix + url
    }

    function handleUploadError(error: any) {
        ElMessage({
            message: '上传出错！',
            showClose: true,
            type: 'error'
        })
    }

    defineExpose({
        formData,
        fundCodeList
    })
</script>

<style lang="less" scoped>
    .container {
        padding: 20px;
    }
    .el-descriptions {
        margin-top: 20px;
    }
    .risk-level {
        display: inline-flex;
        .risk-level-label {
            margin-right: 5px;
            display: inline-block;
            width: 92px;
        }
    }
    .productype-label {
        margin-right: 5px;
        display: inline-block;
        width: 92px;
    }
    .upload-demo {
        margin: 10px 0;
        // display: inline-flex;
    }
    .download-content {
        position: relative;
        .download-excel {
            position: absolute;
            top: 0px;
            left: 82px;
            color: #60acf8;
            cursor: pointer;
        }
    }
    .temp-type-container {
        // border: 1px #9c9fa3 solid;
        position: relative;
        width: 500px;
    }
</style>

<style lang="less">
    .crm-checkbox .el-checkbox .el-checkbox__label {
        min-width: 30px !important;
    }
</style>
