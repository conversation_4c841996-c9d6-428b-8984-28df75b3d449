<!--
 * @Description: 企微任务-新增自定义发送
 * @Author: chaohui.wu
 * @Date: 2023-07-26 14:54:02
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-30 09:46:31
 * @FilePath: /crm-web/packages/crm-wechat-web/src/views/wxMicroTask/custSend/custSendIdx.vue
 *  
-->
<template>
    <div class="cust-send-index-module">
        <el-form
            ref="formCustSendRef"
            :model="formCustSend"
            :rules="rules"
            size="default"
            status-icon
        >
            <section class="module-box">
                <p class="title-txt">消息配置</p>
                <el-form-item label="导入类型：" prop="importType">
                    <crm-radio
                        v-model="formCustSend.importType"
                        :option-list="[{ label: '按投顾客户号', value: '0' }]"
                    ></crm-radio>
                    <p class="txt-red">导入说明：按投顾客户号，文件第一列须是投顾客户号</p>
                </el-form-item>
                <el-form-item label="选择上传文件（Excel 2003）：" prop="custFile">
                    <div class="cust-file">
                        <div class="file-left">
                            <base-button
                                :radius="true"
                                :bold="false"
                                custom-class="small-btn"
                                @click="selectFile"
                                >选择文件</base-button
                            >
                            <p class="file-name">{{ formCustSend.custFile }}</p>
                        </div>
                        <div class="file-right">
                            <base-button
                                :radius="true"
                                :bold="false"
                                custom-class="default-btn"
                                @click="showPreviewVisible"
                                >预览发送对象</base-button
                            >
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="引用参数：" prop="titleList">
                    <ul class="cust-list">
                        <li
                            v-for="(item, index) in formCustSend?.titleList"
                            :key="`titleList-${index}`"
                            class="cust-item"
                        >
                            <base-button
                                :radius="true"
                                :bold="false"
                                custom-class="small-btn"
                                @click="concatStr(item)"
                                >{{ item }}</base-button
                            >
                        </li>
                    </ul>
                </el-form-item>
                <el-form-item label="消息类型：" prop="msgType">
                    <crm-select
                        v-model="formCustSend.msgType"
                        placeholder="请选择消息类型"
                        label-format="name"
                        value-format="code"
                        :option-list="formCustSend?.msgTypeList"
                        :style="{ width: '180px' }"
                    />
                    <!-- <ul class="msg-type-list">
                        <li
                            v-for="(item, index) in formCustSend.msgTypeList"
                            :key="`msgTypeList-${index}`"
                            class="msg-type-item"
                        >
                            <base-button :radius="true" :bold="false" custom-class="small-btn">{{
                                item.name
                            }}</base-button>
                        </li>
                    </ul> -->
                </el-form-item>
            </section>
            <section class="module-box">
                <p class="title-txt">消息内容</p>
                <el-form-item label="推送类型：" prop="pushType">
                    <div class="send-box">
                        <crm-radio
                            v-model="formCustSend.pushType"
                            :option-list="[
                                { label: '只推送企微任务，未添加客户不推', value: '1' },
                                { label: '均推送卡片消息', value: '2' },
                                {
                                    label: '针对添加客户推任务，针对未添加客户推卡片消息',
                                    value: '3'
                                }
                            ]"
                        ></crm-radio>
                    </div>
                    <aside class="notice-box">
                        <p class="notice-txt">
                            说明：需要满足客户微信添加了投顾企业微信且客户微信绑定了一账通号，才能生成企微任务。
                        </p>
                    </aside>
                </el-form-item>
                <div class="send-msg-box">
                    <aside v-if="showModule('taskModule')" class="aside-left">
                        <el-form-item
                            label="标题（企微任务）："
                            prop="taskTitle"
                            label-width="150px"
                        >
                            <crm-input
                                v-model="formCustSend.taskTitle"
                                maxlength="60"
                                placeholder="请填写企微任务标题"
                                show-word-limit
                                type="text"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            label="内容（企微任务）："
                            prop="taskContent"
                            label-width="150px"
                        >
                            <crm-input
                                v-model="formCustSend.taskContent"
                                maxlength="1000"
                                placeholder="请填写企业微信任务内容"
                                show-word-limit
                                type="textarea"
                                resize="none"
                                :autosize="{ minRows: 8, maxRows: 8 }"
                                @focus="setKeyId('taskContent')"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item label="链接标题：" prop="taskUrlTitle" label-width="150px">
                            <crm-input
                                v-model="formCustSend.taskUrlTitle"
                                maxlength="60"
                                placeholder="请填写企微链接标题"
                                show-word-limit
                                type="text"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item label="链接描述：" prop="taskUrlDes" label-width="150px">
                            <crm-input
                                v-model="formCustSend.taskUrlDes"
                                maxlength="60"
                                placeholder="请填写企业微信任务链接描述"
                                show-word-limit
                                type="text"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item label="跳转链接：" prop="taskUrl" label-width="150px">
                            <crm-input v-model="formCustSend.taskUrl"></crm-input>
                        </el-form-item>
                    </aside>
                    <aside v-if="showModule('cardModule')" class="aside-right">
                        <el-form-item
                            label="标题（卡片消息）："
                            prop="cardMsgTitle"
                            label-width="150px"
                        >
                            <crm-input
                                v-model="formCustSend.cardMsgTitle"
                                maxlength="60"
                                placeholder="请填写卡片消息标题"
                                show-word-limit
                                type="text"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item label="内容（卡片消息）：" prop="cardMsg" label-width="150px">
                            <!-- <div v-html="formCustSend.cardMsg"></div> -->
                            <crm-input
                                id="cardMsg"
                                v-model="formCustSend.cardMsg"
                                maxlength="1000"
                                placeholder="请填写卡片消息内容"
                                show-word-limit
                                type="textarea"
                                :autosize="{ minRows: 8, maxRows: 8 }"
                                resize="none"
                                @focus="setKeyId('cardMsg')"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item label="跳转链接：" prop="cardUrl" label-width="150px">
                            <crm-input v-model="formCustSend.cardUrl"></crm-input>
                        </el-form-item>
                    </aside>
                </div>
            </section>
            <section class="module-box">
                <p class="title-txt">发送时间</p>
                <el-form-item label="定时推送：" prop="sendTime">
                    <div class="cust-send-box">
                        <crm-checkbox
                            v-model="formCustSend.sendTimModle"
                            :option-list="[{ label: '', value: '1' }]"
                            item-width="20px"
                        ></crm-checkbox>
                        <div v-show="formCustSend.sendTimModle[0] === '1'" class="send-date-box">
                            <div class="date-box">
                                <crm-date-picker v-model="formCustSend.sendDate"></crm-date-picker>
                            </div>
                            <div class="time-box">
                                <el-time-picker
                                    v-model="formCustSend.sendTime"
                                    placeholder="请选择时间"
                                    :default-value="defaultTime"
                                    format="HH:mm"
                                />
                            </div>
                        </div>
                    </div>
                </el-form-item>
            </section>
            <div class="submit-box">
                <base-button
                    :radius="true"
                    :bold="false"
                    custom-class="small-btn"
                    @click="submitForm(formCustSendRef)"
                >
                    提交
                </base-button>
            </div>
        </el-form>
        <crm-import-file
            v-if="chooseFileVisible"
            v-model="chooseFileVisible"
            :params-data="{
                importType: formCustSend?.importType,
                sequenceNo: formCustSend?.sequenceNo
            }"
            :upload-url="`/wechat/customizesend/checkfile`"
            @callBack="insertFileName"
        />
        <PreviewSend
            v-if="previewVisible"
            v-model="previewVisible"
            :params-data="{
                importType: formCustSend?.importType,
                sequenceNo: formCustSend?.sequenceNo
            }"
        ></PreviewSend>
    </div>
</template>

<script setup lang="ts">
    import { dateTrans } from '@common/utils/index'
    import type { FormInstance, FormRules } from 'element-plus'
    import PreviewSend from './components/previewSend.vue'
    import { getInitData, submitSend, confirmRepeat } from '@/api/project/customSend/customSendList'
    import { returnCodeRes } from '@/constant/index'
    import {
        sortObjDefault,
        downloadFile,
        makeElementTree,
        openUrl,
        removePendingAll,
        fetchRes,
        messageBox,
        message
    } from '@common/utils/index'

    // 获取路由参数
    const route = useRoute()
    const { sequenceNo }: any = route.query || {}

    const defaultTime = computed(() => {
        return new Date()
    })

    class FormData {
        sequenceNo = sequenceNo
        importType = '0'
        custFile = ''
        titleList: string[] = []
        msgTypeList: any[] = []
        msgType = ''
        pushType = '1'
        taskTitle = ''
        taskContent = ''
        taskUrlTitle = ''
        taskUrlDes = ''
        taskUrl = ''
        cardMsgTitle = ''
        cardMsg = ''
        cardUrl = ''
        sendTimModle: string[] = []
        sendDate: Date | string = dateTrans(defaultTime.value, 'yyyyMMDD')
        sendTime: Date = defaultTime.value
    }
    // 您的客户：${{客户姓名}}，投顾客户号：${{投顾客户号}}，将于${{生日}}，生日，此条消息可直接转发至客户微信，供客户选择生日礼品。
    // \n 如何转发? \n ①添加客户微信→ ①点击详情→①点击右上角→①转发→①创建新聊天→①我的客户。转发至客户微信即可。
    const formCustSend = ref(new FormData())
    const formCustSendRef = ref<FormInstance>()

    const chooseFileVisible = ref(false)
    const selectFile = () => {
        chooseFileVisible.value = true
    }

    const insertFileName = (val: any) => {
        const { name, titleList, verifyCode } = val || {}
        // 校验是否合法 0000-成功 0002-表头校验 0003-文件格式校验 0004-投顾客户号系统中不存在 0005-存在重复客户
        switch (verifyCode) {
            case '0002':
            case '0003':
            case '0004':
                break
            case '0005':
                // 若导入文件中，有重复投顾客户号：则弹窗提示确认”存在重复客户，是否确认去除重复项？“，点击确定则关闭弹窗
                messageBox(
                    {
                        content: `存在重复客户，是否确认去除重复项？`
                    },
                    // 确定
                    () => {
                        console.log({ sequenceNo: formCustSend.value.sequenceNo })
                        // 去重复接口调取
                        fetchRes(confirmRepeat({ sequenceNo: formCustSend.value.sequenceNo }), {
                            successCB: (res: any) => {
                                // 成功初始化
                                if (res) {
                                    // 如果成功关闭导入文件隐藏导入文件框
                                    chooseFileVisible.value = false
                                    // 提示弹框导入成功
                                    message({
                                        type: 'success',
                                        message: '文件上传成功'
                                    })
                                    // 成功
                                    // 回调文件名称
                                    if (name) {
                                        formCustSend.value.custFile = name
                                    }
                                    // 重新赋值titleList
                                    if (titleList?.length > 0) {
                                        formCustSend.value.titleList = titleList
                                    }
                                }
                            },
                            successTxt: '',
                            failTxt: '文件上传失败请重试！',
                            fetchKey: ''
                        })
                    },
                    // 取消
                    () => {
                        return true
                    }
                )
                break
            case '0000':
                // 成功
                // 回调文件名称
                if (name) {
                    formCustSend.value.custFile = name
                }
                // 重新赋值titleList
                if (titleList?.length > 0) {
                    formCustSend.value.titleList = titleList
                }
                break
            default:
                break
        }
    }

    const previewVisible = ref<boolean>(false)
    const showPreviewVisible = () => {
        if (!formCustSend.value?.custFile) {
            return messageBox({
                content: `请先上传Excel文件导入发送对象`
            })
        }
        previewVisible.value = true
    }

    /**
     * @description: 校验规则
     * @return {*}
     */
    const rules = reactive<FormRules<FormData>>({
        importType: [{ required: true, message: '请输入导入类型', trigger: ['change', 'blur'] }],
        custFile: [
            {
                required: true,
                message: '请先上传Excel文件导入发送对象',
                trigger: ['change', 'blur']
            }
        ],
        // titleList: [{ required: true, message: '引用参数不能为空', trigger: ['change', 'blur'] }],
        msgType: [{ required: true, message: '消息类型不能为空', trigger: ['change', 'blur'] }],
        taskTitle: [
            { required: true, message: '请输入企微任务标题', trigger: ['change', 'blur'] },
            { min: 1, max: 60, message: '标题长度需要1-60之间', trigger: ['change', 'blur'] }
        ],
        taskContent: [
            { required: true, message: '请输入企微任务内容', trigger: ['change', 'blur'] },
            { min: 1, max: 1000, message: '内容长度需要1-1000之间', trigger: ['change', 'blur'] }
        ],
        // taskUrlTitle: [
        //     { required: true, message: '请输入企微任务链接标题', trigger: ['change', 'blur'] },
        //     { min: 1, max: 60, message: '链接标题长度需要1-60之间', trigger: ['change', 'blur'] }
        // ],
        // taskUrlDes: [
        //     { required: true, message: '请输入企微任务链接描述', trigger: ['change', 'blur'] },
        //     { min: 1, max: 60, message: '链接描述长度需要1-60之间', trigger: ['change', 'blur'] }
        // ],
        cardMsgTitle: [
            { required: true, message: '请输入卡片消息标题', trigger: ['change', 'blur'] },
            {
                min: 1,
                max: 60,
                message: '卡片消息标题长度需要1-60之间',
                trigger: ['change', 'blur']
            }
        ],
        cardMsg: [
            { required: true, message: '请输入卡片消息内容', trigger: 'blur' },
            {
                min: 1,
                max: 1000,
                message: '卡片消息内容长度需要1-1000之间',
                trigger: ['change', 'blur']
            }
        ],
        cardUrl: [{ required: true, message: '请输入卡片跳转链接', trigger: ['change', 'blur'] }]
    })

    // taskMsg
    const curId = ref('cardMsg')
    const setKeyId = (val: string) => {
        curId.value = val
    }

    /**
     * @description: 点击插入
     * @param {*} val
     * @return {*}
     */
    const concatStr = (val: string) => {
        if (!['cardMsg'].includes(curId.value)) {
            return
        }
        return insertInputTxt(curId.value, '${{' + val + '}}')
    }

    /**
     * @description: 插入文本
     * @param {*} id
     * @param {*} insertTxt
     * @return {*}
     */
    const insertInputTxt = (id: string, insertTxt: string) => {
        // 插入字符串
        const elInput: any = document.querySelector(`#${id}`) || null // 获取dom
        const startPos = elInput?.selectionStart
        const endPos = elInput?.selectionEnd
        if (startPos === undefined || endPos === undefined) {
            return
        }
        const txt = elInput.value
        const result = txt.substring(0, startPos) + insertTxt + txt.substring(endPos)
        elInput.value = result
        // 这里比较重要 **给最终绑定的参数 进行赋值
        if (id === 'cardMsg') {
            formCustSend.value.cardMsg = result // 赋值
        }
        elInput.focus()
        nextTick(() => {
            elInput.selectionStart = startPos + insertTxt.length
            elInput.selectionEnd = startPos + insertTxt.length
        })
    }

    /**
     * @description: 提交
     * @param {*} formEl
     * @return {*}
     */
    const submitForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        // 提示弹框确定
        messageBox(
            {
                content: `确定提交？`
            },
            async () => {
                // 校验form表单
                await formEl.validate((valid, fields) => {
                    if (valid) {
                        const { sendTimModle, sendTime, sendDate } = formCustSend.value
                        // 判断定时发送的时间是否大于当前时间
                        const curTime = new Date().getTime()
                        const sendDateTpl = dateTrans(sendDate, 'YYYY-MM-DD')
                        const sendTimeHours = new Date(sendTime).getHours()
                        const sendTimeMin = new Date(sendTime).getMinutes()
                        const sendTimeTpl = new Date(
                            `${sendDateTpl} ${sendTimeHours}:${sendTimeMin}:00`
                        ).getTime()
                        const sendTimeParma = dateTrans(sendTimeTpl, 'yyyyMMDDHHmm')
                        // 定时推送
                        if (sendTimModle[0] === '1') {
                            if (sendTimeTpl < curTime) {
                                // 定时时间超时，请重新选择
                                messageBox({
                                    content: `定时时间超时，请重新选择`
                                })
                                return
                            }
                        }
                        // 提交接口
                        const paramTpl = {
                            importType: formCustSend.value.importType,
                            msgType: formCustSend.value.msgType,
                            pushType: formCustSend.value.pushType,
                            wechatTaskDTO: {
                                title: formCustSend.value.taskTitle,
                                content: formCustSend.value.taskContent,
                                hrefTitle: formCustSend.value.taskUrlTitle,
                                hrefDesc: formCustSend.value.taskUrlDes,
                                hrefUrl: formCustSend.value.taskUrl
                            },
                            cardMessageDTO: {
                                title: formCustSend.value.cardMsgTitle,
                                content: formCustSend.value.cardMsg,
                                hrefUrl: formCustSend.value.cardUrl
                            },
                            pushTime: sendTimModle[0] === '1' ? sendTimeParma : '',
                            sequenceNo: formCustSend.value.sequenceNo
                        }
                        fetchRes(submitSend(paramTpl), {
                            successCB: (res: any) => {
                                // 成功初始化
                                const { verifyCode, verifyDesc } = res || {}
                                switch (verifyCode) {
                                    case '0000':
                                        message({
                                            type: 'success',
                                            message: verifyDesc || '发送成功'
                                        })
                                        break
                                    case '0002':
                                        messageBox({
                                            content: `暂无可生成企微任务的客户，请重新选择发送对象`
                                        })
                                        break
                                    case '0001':
                                    default:
                                        message({
                                            type: 'error',
                                            message: verifyDesc || '发送失败请重试'
                                        })
                                        break
                                }
                            },
                            errorCB: (res: any) => {
                                // 校验失败
                                messageBox({
                                    content: `${res.description}`
                                })
                            },
                            catchCB: (res: any) => {
                                // 请求失败
                                messageBox({
                                    content: `${res.description}`
                                })
                            },
                            successTxt: '',
                            failTxt: '',
                            fetchKey: ''
                        })
                    } else {
                        console.log('error submit!', fields)
                    }
                })
            },
            () => true
        )
    }

    /**
     * @description: 重置
     * @param {*} formEl
     * @return {*}
     */
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * @description: 显示对应模块
     * @param {*} moduleFlag
     * @return {*}
     */
    const showModule = (moduleFlag: string) => msgModule.value.includes(moduleFlag)

    /**
     * @description: 模块展示联动
     * @param {*} computed
     * @return {*}
     */
    const msgModule = computed(() => {
        switch (formCustSend.value.pushType) {
            case '2':
                return ['cardModule']
            case '3':
                return ['taskModule', 'cardModule']
            case '1':
            default:
                return ['taskModule']
        }
    })

    /**
     * @description: 初始化并赋值
     * @return {*}
     */
    const initSendMsg = () => {
        fetchRes(getInitData({ sequenceNo }), {
            successCB: (res: any) => {
                // 成功初始化
                if (res) {
                    const {
                        msgTypeList,
                        titleList,
                        sequenceNo: sequenceNoTpl,
                        custFileNameNum
                    } = res || {}
                    formCustSend.value.msgTypeList = msgTypeList
                    formCustSend.value.titleList = titleList
                    formCustSend.value.sequenceNo = sequenceNoTpl || sequenceNo
                    formCustSend.value.custFile = sequenceNo ? `${custFileNameNum}` : ''
                }
            },
            errorCB: () => {
                formCustSend.value = new FormData()
            },
            catchCB: () => {
                formCustSend.value = new FormData()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        initSendMsg()
    })
</script>

<style lang="less" scoped>
    .cust-send-index-module {
        box-sizing: border-box;
        min-height: 100vh;
        padding: 15px;

        .title-txt {
            font-size: 16px;
            font-weight: bold;
        }

        .txt-red {
            color: @font_color_07;
        }

        .send-box {
            width: 300px;
        }

        .notice-box {
            width: 300px;
        }

        .cust-file {
            display: flex;
            align-items: center;

            .file-left {
                display: flex;
                align-items: center;

                .file-name {
                    min-width: 100px;
                    margin-left: 10px;
                    font-size: 12px;
                    color: @font_color;
                }
            }

            .file-right {
                display: flex;
                align-items: center;
                margin-left: 30px;
            }
        }

        .cust-list {
            display: flex;
            align-items: center;

            .cust-item {
                box-sizing: border-box;
                margin-right: 10px;
            }
        }

        .send-msg-box {
            display: flex;
            justify-content: space-between;

            .aside-left {
                flex: 1;
                margin-right: 50px;
            }

            .aside-right {
                flex: 1;
            }
        }

        .cust-send-box {
            display: flex;
            align-items: center;
            line-height: normal;

            .send-date-box {
                display: flex;
                align-items: center;

                .date-box {
                    width: 120px;
                }

                .time-box {
                    display: flex;
                    align-items: center;
                    width: 90px;
                    height: 24px;

                    :deep(.el-input) {
                        --el-input-height: 24px;

                        .el-input__inner {
                            height: 24px;
                            font-size: 12px;
                            line-height: 24px;
                            border-radius: 2px;
                        }
                    }
                }
            }
        }

        .submit-box {
            display: flex;
            align-items: center;
            justify-content: center;

            .base_button {
                width: 100px;
            }
        }
    }
</style>
