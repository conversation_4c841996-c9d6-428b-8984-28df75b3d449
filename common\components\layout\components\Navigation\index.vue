<template>
    <div ref="navBar" class="navigationBar">
        <el-menu
            :key="menuList.length"
            class="menuBar"
            mode="horizontal"
            :ellipsis="false"
            :default-active="defaultActiveMenu"
        >
            <template v-for="item in menuList" :key="item.id">
                <menu-item :menu-info="item" />
            </template>

            <el-menu-item v-if="hideMenuList.length > 0" index="99999" class="menu-more-item">
                <div
                    class="menu-more-box"
                    @mouseenter="handleMouseEnter"
                    @mouseleave="handleMouseLeave"
                >
                    <div class="menu-more-item">
                        <svg-icons name="menu-more-icon-left" :size="15" />
                        <!-- <i class="iconfont icon_more_square menu-more-icon-left" /> -->
                        <span>更多</span>
                        <!-- <el-icon :size="12"><arrow-down /></el-icon> -->
                    </div>
                    <more-menu v-model:visible="visible" :menu-list="hideMenuList" />
                </div>
            </el-menu-item>
        </el-menu>
    </div>
</template>

<script>
    import { useUserStore } from '../../stores/user'
    import MenuItem from './components/MenuItem.vue'
    import MoreMenu from './components/MoreMenu.vue'
    import { deepClone, flatTree } from '@common/utils/index.js'

    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'Navigation',
        components: { MenuItem, MoreMenu },
        setup() {
            const userStore = useUserStore()
            return { userStore }
        },
        data() {
            return {
                menuList: [],
                dataMenuList: [],
                hideMenuList: [],
                flatMenuData: null,
                visible: false,
                timeOut: null
            }
        },
        computed: {
            // ...mapGetters(['user']),

            // 根据路由选中导航菜单
            defaultActiveMenu({ $route }) {
                const list = this.flatMenuData || []
                const currentMenu = list.find(item => item.path === $route.path)
                return String(currentMenu?.id) || ''
            }
        },
        watch: {
            'userStore.menus'(newVal) {
                this.setMenuData(newVal || [])
                this.setMenuList()
            }
        },
        mounted() {
            this.setMenuData()
            this.$nextTick(() => {
                this.setMenuList()
            })
            window.addEventListener('resize', this.setMenuList)
        },
        unmounted() {
            window.removeEventListener('resize', this.setMenuList)
            this.clearMyTimeout()
        },
        methods: {
            setMenuData(newMenus = this.userStore.menus) {
                const menus = deepClone(newMenus) || []
                const menuList = this.formatMenusData(menus)
                this.dataMenuList = menuList.filter(item => item.name !== 'error404')
                this.flatMenuData = flatTree(menus)
            },

            // 设置显示菜单（根据页面宽度、显示不下的放到更多里面）
            setMenuList() {
                let clientWidth = this.$refs.navBar?.clientWidth || 0
                clientWidth = clientWidth - 80 // 父div左右两侧padding: 0 20px
                let showMenuList = [] // 显示的菜单列表
                const hideMenuList = [] // 超出显示范围后隐藏的菜单列表
                if (clientWidth > 0) {
                    let totalWidth = this.getMenuItemWidth('更多') // 更多的宽度
                    this.dataMenuList.map(item => {
                        totalWidth += this.getMenuItemWidth(item.title)
                        if (totalWidth < clientWidth) {
                            showMenuList.push(item)
                        } else {
                            hideMenuList.push(item)
                        }
                    })
                } else {
                    showMenuList = this.dataMenuList
                }

                this.menuList = showMenuList
                this.hideMenuList = hideMenuList
            },

            /**
             * @description: 获取每个菜单项的宽度
             * @param {*} word 菜单中文名称（如有英文单词，暂时按中文单词宽度计算）
             * @return {*} 菜单项的宽度
             */
            getMenuItemWidth(word) {
                // 26 为左侧icon宽度(width:24px + margin-right: 2px)
                // 14 为每个中文字的宽度14px
                // 18 为右侧icon宽度(width: 12px + margin-left: 4px + margin-right: 2px )
                // 12 为菜单项左右两侧的padding: 0 6px;
                const num = word.length
                const itemWidth = 26 + 14 * num + 18 + 12
                return itemWidth
            },

            // 格式化菜单数据
            formatMenusData(menus = []) {
                const newMeunList = []
                if (menus) {
                    menus.forEach(item => {
                        if (item.isShow === '1') {
                            const children = item?.children || []
                            item.children = children.filter(subItem => subItem.isShow === '1')
                            newMeunList.push(item)
                        }
                    })
                }
                return newMeunList
            },

            /*
             * 显示、隐藏更多菜单
             */
            handleMouseEnter() {
                this.setMyTimeout(true, 300)
            },
            handleMouseLeave() {
                this.setMyTimeout(false, 300)
            },
            setMyTimeout(visible, time) {
                if (!this.timeOut) {
                    this.timeOut = setTimeout(() => {
                        this.visible = visible
                        this.clearMyTimeout()
                    }, time)
                } else {
                    this.clearMyTimeout()
                }
            },
            clearMyTimeout() {
                clearTimeout(this.timeOut)
                this.timeOut = null
            }
        }
    })
</script>

<style lang="less">
    @import 'index';

    .crm-web-app {
        .navigationBar {
            position: relative;
            overflow: hidden;

            .el-menu-item.is-active {
                color: @font_color_01 !important;
            }
        }

        .menu-more-item {
            position: static;
            display: flex;
            align-items: center;
            height: 52px;
            padding-top: 2px;
            line-height: 52px;
            border-bottom: 2px solid transparent;

            &:focus {
                color: @font_color_01 !important;
                background-color: #3b3e5b !important;
            }

            .menu-more-box {
                position: static;
            }

            .menu-more-icon-left {
                display: inline-block;
                width: 24px;
                font-size: 17px;
                vertical-align: middle;
            }

            .menu-more-icon-arrow {
                display: inline-block;
                width: 12px;
                margin-top: 20px;
                margin-left: 4px;
                font-size: 12px;
                vertical-align: top;
            }
        }
    }
</style>
