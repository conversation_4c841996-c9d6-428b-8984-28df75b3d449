import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    QueryBeisenPosLevelConfigWebReq,
    InsertBeisenPosLevelConfigWebReq,
    UpdateBeisenPosLevelConfigWebReq,
    BeisenPosLevelConfigWebIdReq
} from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigQuery = (params: QueryBeisenPosLevelConfigWebReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/query',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 导出接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigExport = (params: QueryBeisenPosLevelConfigWebReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 新增接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigInsert = (params: InsertBeisenPosLevelConfigWebReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/insertOrUpdate',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 修改接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigUpdate = (params: UpdateBeisenPosLevelConfigWebReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/insertOrUpdate',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 删除接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigDelete = (params: BeisenPosLevelConfigWebIdReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询单个接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const beisenPosLevelConfigDetail = (params: BeisenPosLevelConfigWebIdReq): any => {
    return axiosRequest(
        paramsMerge({
            url: '/performance/beisenposlevel/detail',
            method: 'post',
            data: params
        })
    )
}
