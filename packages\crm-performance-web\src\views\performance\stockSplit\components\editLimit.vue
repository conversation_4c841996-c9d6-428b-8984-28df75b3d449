<!--
 * @Description: 操作明细/客户明细
 * @Author: chao<PERSON>.wu
 * @Date: 2023-04-07 10:56:38
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 18:17:55
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/custDetail.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="600px"
        height="60%"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :before-close="handleClose"
        :close-on-click-modal="false"
    >
        <div class="stock-form-module">
            <el-form
                ref="dialogForm"
                class="crm_dialog_form"
                label-width="140px"
                :model="formList"
                :rules="formRules"
                :disabled="false"
                :scroll-to-error="true"
                validate-on-rule-change
                @submit.prevent
            >
                <el-form-item label="投顾客户号：" :inline-message="true">
                    <el-input
                        v-model="formList.custNo"
                        size="small"
                        :disabled="true"
                        style="width: 180px"
                    ></el-input>
                </el-form-item>
                <el-form-item label="客户姓名：" :inline-message="true">
                    <el-input
                        v-model="formList.custName"
                        size="small"
                        :disabled="true"
                        style="width: 180px"
                    ></el-input>
                </el-form-item>
                <el-form-item label="分配上限_原：" prop="upperLimit" :inline-message="false">
                    <el-input-number
                        v-model="formList.upperLimit"
                        :precision="2"
                        :step="1"
                        :min="0"
                        :max="100"
                        size="small"
                        type="number"
                        style="width: 180px"
                        :value-on-clear="null"
                        :controls="false"
                    >
                        <template #append>%</template>
                    </el-input-number>
                    <span class="txt-red">示例：30%，输入30</span>
                </el-form-item>
            </el-form>
        </div>

        <template #footer>
            <crm-button plain size="small" :radius="true" @click="dialogHandleClose"
                >关 闭</crm-button
            >
            <crm-button size="small" :radius="true" @click="confirmFn(dialogForm)"
                >保 存</crm-button
            >
        </template>
    </crm-dialog>
</template>

<script setup lang="ts">
    import type { FormInstance, FormRules } from 'element-plus'
    import {
        deepClone,
        fetchRes,
        addUnit,
        formatTableValue,
        dateTrans,
        excludeArr
    } from '@common/utils/index'
    import { useVisible } from '../scripts/hooks/useVisible'
    import { saveUpperLimit } from '@/api/project/stockSplit/stockSplitList'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            transData?: {
                id: string
                title: string
                custNo: string
                custName: string
                upperLimit: string
            }
        }>(),
        {
            visibleCus: true,
            transData: () => {
                return {
                    id: '',
                    title: '',
                    custNo: '',
                    custName: '',
                    upperLimit: ''
                }
            }
        }
    )

    const formRules = computed<FormRules>(() => {
        const rulesMap: any = {
            upperLimit: [
                {
                    required: false,
                    pattern: /^([1-9]\d*(\.\d*[1-9])?)|(0\.[1-9]\d*|[1-9]\d*(\.\d+)?)$/,
                    message: '支持录入正数，四舍五入保留两位小数',
                    trigger: ['blur', 'change']
                }
            ]
        }

        // 过滤表单条件
        // const objTpl: any = {}
        // rulesList.value.map((item: string) => {
        //     if (rulesMap[item]) {
        //         objTpl[item] = rulesMap[item]
        //     }
        //     return item
        // })
        // 基于条件联动校验规则
        return rulesMap
    })

    class FormList {
        id = ''
        upperLimit = ''
    }

    const formList = ref<any>(new FormList())

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'editCallback'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props,
        formList
    })

    /**
     * @description: 关闭弹框
     * @return {*}
     */
    const dialogForm = ref<FormInstance>()
    const dialogHandleClose = () => {
        // 关闭弹框后逻辑
        handleClose()
    }

    /**
     * @description: 保存
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = async (formEl: FormInstance | undefined, flag?: string | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                fetchRes(
                    saveUpperLimit({
                        id: formList.value.id,
                        custNo: formList.value.custNo,
                        upperLimit: formList.value.upperLimit
                    }),
                    {
                        successCB: (res: any) => {
                            const { returnCode, description } = res
                            dialogVisible.value = false
                            // 重置
                            formEl.resetFields()
                            emit('editCallback')
                        },
                        successTxt: '保存成功',
                        failTxt: '保存失败请重试',
                        fetchKey: ''
                    }
                )
            }
        })
    }

    onMounted(() => {
        formList.value = {
            id: props.transData.id,
            upperLimit: props.transData.upperLimit,
            custNo: props.transData.custNo,
            custName: props.transData.custName
        }
    })
</script>

<style lang="less" scoped>
    .txt-red {
        margin-left: 15px;
        font-size: 12px;
        color: red;
    }
</style>
