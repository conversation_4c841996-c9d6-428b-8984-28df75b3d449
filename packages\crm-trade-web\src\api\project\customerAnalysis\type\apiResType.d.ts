/*
 * @Description: 客户定位分析 返回结果 ts类型
 * @Author: chaohui.wu
 * @Date: 2023-03-25 23:44:46
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-19 13:44:42
 * @FilePath: /crm-asset-web/src/api/project/customerAnalysis/type/apiResType.d.ts
 *
 */

export {}
declare module './apiResType' {
    /**
     * 标签
     */
    interface BaseLabelVo {
        /**
         * 标签展示文本，进取型、适合R3及以下产品
         */
        label: string
        /**
         * 标签类型，扩展字段，用于后期颜色等展示
         */
        labelType: string
    }
    interface BaseInfoData {
        [x: string]: string
        labelList: BaseLabelVo[]
        /**
         * 客户号
         */
        conscustno: string
        /**
         * 客户姓名
         */
        custName?: string
        /**
         * 性别，1-男，0-女
         */
        gender?: string
        /**
         * 一账通账号
         */
        hboneNo: string
        /**
         * 客户类型，0：机购；1：个人
         */
        investType?: string
        tips?: string[]
    }

    interface RadarData {
        /**
         * 展示名称，1,2,3
         * 1-人生阶段，2-财富水平，3-投资经验
         */
        key: string
        /**
         * 对应，人生阶段：1-“人生奋斗期”、2-“财富创造期”、3-“财富沉淀期”、4-“人生享受期”
         * 财富水平：1-“温饱”、2-“小康”、3-“殷实”、4-“富裕”、5-“豪门”
         * 投资经验：1-“缺乏”、2-“有限”、3-“一般”、4-“较丰富”、5-“丰富”
         */
        level: string
        /**
         * 最大分值，3
         */
        maxScore: number
        /**
         * 得分，【0-3】
         */
        score: number
        /**
         * 展示标题，人生阶段，财富水平，投资经验
         */
        title: string
    }
    interface RiskInfoData {
        /**
         * 投资态度，【0-3】主观风险偏好
         */
        attitude: number
        /**
         * 客户风险承受力，【0-3】【0，2）=低，【2，2.5】=中，（2.5，3】=高
         */
        custRisk: string
        /**
         * 投资预期，【0-3】主观风险偏好
         */
        expectations: number
        /**
         * 是否做过问卷，做过问卷，并且问卷类型非零售的1-调整，0-未调整
         */
        haveActualKyc: string
        /**
         * 定位分析，定位分析第一段话术
         */
        positioningAnalysis: string
        /**
         * 定位分析，定位分析第二段话术
         */
        positioningAnalysisMore: string
        /**
         * 产品偏好，【0-3】主观风险偏好
         */
        productInterests: number
        radarList: RadarData[]
        /**
         * 客户风险等级
         */
        riskLevel: string
        /**
         * 客户风险等级对应枚举
         */
        riskLevelStr: string
        /**
         * 是否调整，问卷是否调整过1-调整，0-未调整
         */
        showAdjustKyc: string
        /**
         * 主观风险偏好，【0-3】【0，2）=低，【2，2.5】=中，（2.5，3】=高
         */
        subjectiveRisk: string
        /**
         * 是否展示，1-展示，0不展示
         */
        unfold: string
    }
    export { BaseInfoData, RiskInfoData }
}
