<!--
* author: di.zong
* 通用按钮组件
 -->
<template>
    <div class="cust-button">
        <el-button
            :class="[
                { is_link: link },
                { no_under_line: noUnderLine },
                { button_radius: radius },
                { button_linear: linear },
                { button_linear_back: linearBack },
                {
                    'btn-bold': bold
                },
                customClass
            ]"
            :style="btnStyle"
            :loading="loading || btnLoading"
            v-bind="$attrs"
            @click="btnClick"
        >
            <slot />
        </el-button>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    export default defineComponent({
        name: 'ButtonCust',
        props: {
            clickFunc: {
                type: Function,
                default: null
            },
            loading: {
                type: Boolean,
                default: false
            },
            radius: {
                type: Boolean,
                default: false
            },
            linear: {
                // 只在主要按钮模式下生效
                type: Boolean,
                default: false
            },
            linearBack: {
                // 只在主要按钮模式下生效
                type: Bo<PERSON><PERSON>,
                default: false
            },
            bold: {
                // 只在主要按钮模式下生效
                type: Boolean,
                default: false
            },

            link: {
                // 是否是链接按钮
                type: Boolean,
                default: false
            },
            linkColor: {
                // 链接按钮的颜色
                type: String,
                default: '@font_link'
            },
            noUnderLine: {
                // 链接按钮不带下划线
                type: Boolean,
                default: false
            },
            customClass: {
                type: [String],
                default: ''
            }
        },
        data() {
            return {
                btnLoading: false
            }
        },
        computed: {
            btnStyle() {
                const style = {}
                if (this.link) {
                    style.color = this.linkColor
                }
                return style
            }
        },
        methods: {
            btnClick() {
                if (typeof this.clickFunc === 'function') {
                    this.btnLoading = true
                    this.clickFunc()
                        .then(res => {
                            if (res) {
                                ElMessage({
                                    message: res,
                                    type: 'success',
                                    duration: 2000
                                })
                            }
                            this.btnLoading = false
                        })
                        .catch(err => {
                            if (err) {
                                ElMessage({
                                    message: err,
                                    type: 'warning',
                                    duration: 2000
                                })
                            }
                            this.btnLoading = false
                        })
                }
            }
        }
    })
</script>
<style lang="less" scoped>
    .cust-button {
        :deep(.el-button) {
            margin-right: 2px;
            // 主要按钮样式
            font-size: 12px;
            font-weight: normal;
            color: @font_color_01;
            vertical-align: baseline;
            background-color: @theme_main;
            border-color: @theme_main;
            border-radius: 0;

            &.el-button--small {
                height: 26px;
                padding: 0 10px;
                line-height: 26px;

                :deep(.el-icon) {
                    font-size: 12px;
                }
            }

            &.el-button--default {
                height: 26px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 26px;

                :deep(.el-icon) {
                    font-size: 16px;
                }
            }

            &.button_radius {
                border-radius: 5px;
            }

            &:focus-visible {
                outline: 0;
            }

            &.btn-bold {
                // 众口难调 到底是加粗不加粗
                font-weight: 700;
            }
            // 渐变类的按钮
            &.button_linear {
                background-image: linear-gradient(-13deg, #d52123 0%, #ea754c 100%);
                border: none;

                &:hover {
                    background-image: linear-gradient(-13deg, #c51719 0%, #d45c32 100%);
                }

                &.is-disabled {
                    background-image: none;
                }
            }

            &.button_linear_back {
                background-image: linear-gradient(-13deg, #3b3e5b 0%, #575b83 100%);
                border: none;

                &:hover {
                    background-image: linear-gradient(-13deg, #292c44 0%, #4d5179 100%);
                }

                &.is-disabled {
                    background-image: none;
                }
            }

            &.is-loading {
                .iconfont {
                    display: none;
                }
            }

            &:focus {
                color: @font_color_01;
                background-color: @theme_main;
            }

            &:active {
                background-color: @theme_main;
            }

            &:hover {
                color: @font_color_01;
                background-color: @theme_main_hover;
                border-color: @theme_main_hover;
            }

            &.is-disabled {
                color: @font_color_01;
                background-color: #c1c1c9;
                border-color: #c1c1c9;
            }

            i {
                // font-size: 12px;
                margin-right: 5px;
            }
            // 朴素按钮样式
            &.is-plain {
                color: #231815;
                background-color: @font_color_01;
                border: 1px solid #dedede;

                &.tinny-btn {
                    @curFontColor: #d0021b;

                    height: 16px;
                    padding: 3px 7px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 16px;
                    color: @curFontColor;
                    border: 1px solid @curFontColor;
                    border-radius: 4px;

                    &:hover {
                        color: #c82e30;
                        background-color: #fff7f7;
                        border-color: #ead8d8;
                    }

                    &:active {
                        color: #c82e30;
                        background-color: #f9e7e7;
                        border-color: #ead8d8;
                    }
                }

                &:hover {
                    color: #c82e30;
                    background-color: #fff7f7;
                    border-color: #ead8d8;
                }

                &:active {
                    color: #c82e30;
                    background-color: #f9e7e7;
                    border-color: #ead8d8;
                }

                &.is-disabled {
                    color: @font_color_03;
                    background-color: @font_color_01;
                    border-color: #e5e5e9;
                }
            }

            // 链接类按钮样式
            &.is_link {
                position: relative;
                padding: 0 5px;
                color: @font_link;
                background: transparent;
                border: none;
                border-radius: 0;

                &:hover {
                    color: @font_link;
                }

                &:hover::after {
                    position: absolute;
                    bottom: 2px;
                    left: 0;
                    width: 100%;
                    height: 1px;
                    content: '';
                    background-color: @font_link;
                }

                &.is-disabled {
                    color: @font_color_03!important;

                    &:hover::after {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 0;
                        content: '';
                        background-color: @theme_main;
                    }
                }
                // 没有下划线的链接按钮
                &.no_under_line {
                    &:hover::after {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 0;
                        content: '';
                        background-color: @theme_main;
                    }
                }
            }

            &.el-button--search {
                color: #797979;
                background-color: @font_color_01;
                border-color: #e1e0e0;
                border-radius: 12px;

                &:hover {
                    color: #686767;
                    background-color: @bg_main;
                    border-color: #c4c4c4;
                }

                &:active {
                    color: #4a4a4d;
                    background-color: #e2e4ea;
                    border-color: #c4c4c4;
                }
            }
        }

        .iconfont {
            height: 23px;
        }
    }
</style>
