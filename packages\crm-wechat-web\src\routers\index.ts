/*
 * @Description: 路由
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-07-21 18:08:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 15:11:25
 * @FilePath: /crm-rpo-template/packages/crm-template/src/routers/index.ts
 *
 */
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import pageRouter from './page'
import viewsRouter from './views'

// 对RouteRecordRaw类型进行扩展
export type AddRouteRecordRaw = RouteRecordRaw & {
    hidden?: boolean
}

const router = createRouter({
    history: createWebHashHistory(),
    routes: [...viewsRouter, ...pageRouter] as AddRouteRecordRaw[]
})

export default router
