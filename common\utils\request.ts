/*
 * @Description: axios请求封装
 * @Author: chaohui.wu
 * @Date: 2023-07-27 13:21:08
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-10 11:24:05
 * @FilePath: /crm-web/common/utils/request.ts
 *
 */
import axios from 'axios'
import type { AxiosResponse, AxiosStatic } from 'axios'
import {
    getLocalItem,
    setLocalItem,
    logoutWithoutRequest,
    removeToken,
    setToken,
    showLoading,
    hideLoading,
    message
} from './index.js'
import { responseCode } from '../constant/index'
const pending: any = [] // 上次请求的参数，用于跟当前请求参数作比较来阻断重复请求
const CancelToken = axios.CancelToken // axios提供的用于取消请求的api
const removePending = (config: any) => {
    for (const i in pending) {
        if (pending[i].requestConfig === config) {
            // 在当前请求在数组中存在时执行取消函数
            pending[i].cancelFn() // 执行取消操作
            pending.splice(i, 1) // 根据具体情况决定是否在这里就把pending去掉
        }
    }
}
// tab切换数据同步
export const removePendingAll = () => {
    pending.forEach((item: any, index: number) => {
        item.cancelFn()
    })
    pending.splice(0, pending.length)
}

/**
 * 处理接口返回码
 */
const handleResponseCode = (res: any, callbacks: any) => {
    if (!res) {
        return
    }
    const { successCB, errorCB } = callbacks
    const { code } = res
    if (
        code === responseCode.SUCCESS ||
        code === responseCode.CRM_SUCCESS ||
        code === responseCode.DS_SUCCESS
    ) {
        successCB && successCB(res)
    } else if (code === responseCode.UNLOGIN) {
        // 接口未登录
        successCB && successCB(res)
        const { origin, port, pathname } = window.location
        // http://crm.it32.k8s.howbuy.com/crm-sys/login
        if (process.env.NODE_ENV === 'production') {
            removeToken()
            window.localStorage.removeItem('USER_INFO')
            window.localStorage.removeItem('USER_MENU')
            window.localStorage.removeItem('hb_crm_token')
            window.location.href = `${origin}/crm-sys/login`
        }
    } else if (code === responseCode.RELOGIN || code === responseCode.CRM_SYS_NOACCESS) {
        errorCB && errorCB(res)
    } else {
        errorCB && errorCB(res)
    }
}

/**
 * axios接口请求封装
 * @params ...apiConfig axios请求层的参数，包含: url, params/data，method等
 * @params callbacks: 回调{ errorCB }
 * @params 请求相关的其它参数 { timeout: 超时时间, baseURL: 地址前缀，默认process.env.VUE_APP_BASE_API }
 */
interface NavigatorTpl extends Navigator {
    name: string
    msSaveOrOpenBlob?: any
    msSaveBlob?: any
}
declare global {
    interface Window {
        _msApiPrefix: string
        _msApiPrefix_report: string
        _msCrmAssetWebPrefix: string
        _msViewer: string
        _msViewerApi: string
        jsonCallBack: any
        DocsAPI: any
    }
    interface AxiosStatic {
        jsonp: any
    }
}
export const axiosRequest = ({
    callbacks = {},
    baseURL = window._msApiPrefix,
    timeout = 20000,
    loadingParams = '',
    canclePending = false,
    ...apiConfig
}: any) =>
    new Promise((resolve, reject) => {
        const { errorCB }: any = callbacks || {}
        const service = axios.create({
            baseURL,
            timeout,
            withCredentials: true // 允许携带cookie
        })

        // 设置请求头参数
        service.interceptors.request.use((config: any) => {
            if (loadingParams) {
                showLoading({ ...loadingParams })
            }
            const requestConfig = JSON.stringify(apiConfig)
            // 在一个axios发送前执行一下判定操作，在removePending中执行取消操作
            removePending(requestConfig)
            if (!canclePending) {
                // 本次axios请求的配置添加cancelToken
                config.cancelToken = new CancelToken(function executor(c: any) {
                    pending.push({
                        requestConfig,
                        cancelFn: c
                    })
                })
            }

            // 表示在配置中的设置头消息的字段Authorization为从本地获取的token值
            const token: any = getLocalItem('hb_crm_token')
            if (token) {
                config.headers.Authorization = token
            }
            return config
        })

        service.interceptors.response.use(
            (response: any): any => {
                setTimeout(() => {
                    hideLoading()
                }, 200)
                // 在一个axios响应后再执行一下取消操作，把已经完成的请求从pending中移除
                removePending(JSON.stringify(apiConfig))

                // 【投研系统】每次请求需要更新token
                const newToken = response?.headers?.token
                if (newToken) {
                    setToken(newToken)
                    // setLocalItem('hb_crm_token', newToken)
                }

                const res: any = response?.data || {}
                const successCB = callbacks?.successCB || null
                handleResponseCode(res, {
                    successCB: (res: any) => {
                        successCB && successCB(res)
                        resolve(res)
                    },
                    errorCB: (err: any) => {
                        if (errorCB) {
                            errorCB(err)
                        } else {
                            reject(err)
                        }
                    }
                })
            },
            error => {
                setTimeout(() => {
                    hideLoading()
                }, 200)
                // 如果是被拦截掉的重复请求，不执行错误回调
                if (errorCB) {
                    errorCB(error)
                } else {
                    try {
                        reject(error)
                    } catch (e) {
                        console.log(e)
                    }
                }
            }
        )

        return service({
            method: 'post',
            ...apiConfig
        })
    })

/**
 * axios jsonp接口封装 替代$.ajax
 * @params url, data 链式调用
 */
export const axiosJsonp = ({ url, data }: { url: string; data: any }): any => {
    const axiosTpl: any = axios
    axiosTpl.jsonp = () => {
        if (!url) {
            return
        }
        return new Promise((resolve, reject) => {
            window.jsonCallBack = (result: any) => {
                resolve(result)
            }
            const JSONP = document.createElement('script')
            JSONP.type = 'text/javascript'
            const urlObj = new URLSearchParams(`?callback=jsonCallBack`)
            if (data) {
                for (const key in data) {
                    if (data[key]) {
                        urlObj.set(key, data[key])
                    }
                }
            }
            JSONP.src = `${url}?${urlObj.toString()}`
            document.getElementsByTagName('head')[0].appendChild(JSONP)
            setTimeout(() => {
                document.getElementsByTagName('head')[0].removeChild(JSONP)
            }, 500)
        })
    }
    return axiosTpl
}
