/*
 * @Description: 配置-存量分成列表
 * @Author: chaohui.wu
 * @Date: 2023-08-03 11:33:10
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 14:59:33
 * @FilePath: /crm-web/packages/crm-performance-web/src/api/project/stockSplit/type/apiReqType.d.ts
 *
 */

export {}
declare module './apiReqType' {
    interface StockConfigParam {
        /**
         * 客户姓名
         */
        custName?: string
        /**
         * 客户编号
         */
        custNo?: string
        /**
         * 一账通号
         */
        hboneNo?: string
        /**
         * 基金名称
         */
        productCode?: string
        /**
         * 交易状态
         */
        tradeStatus?: string
        /**
         * 预约状态
         */
        orderStatus?: string
        /**
         * 付款状态
         */
        payStatus?: string
        /**
         * 交易日期
         */
        payStartDt?: string
        payEndDt?: string
        /**
         * 交易类型
         */
        tradeType?: string
        isOnlineSign?: string
        signStatus?: string
        signStartDt?: string
        signEndDt?: string
        /**
         * 页号
         */
        pageNo?: number
        /**
         * 每页大小
         */
        pageSize?: number
    }
    interface TradeInfoReq {
        /**
         * 交易单号
         */
        tradeId?: string
        /**
         * 投顾客户号
         */
        custNo?: string
    }

    interface FundInfoReq {
        /**
         * 基金代码/名称
         */
        fundCode?: string
    }

    /**
     * SaveStockSplitConfigRequest
     */
    interface SaveConfigReq {
        /**
         * 激活时间 yyyyMMdd
         */
        activeDt?: string
        /**
         * 激活状态:是否激活1是0否
         */
        activeFlag?: string
        /**
         * 计算结束日期 yyyyMMdd
         */
        calEndDt?: string
        /**
         * 计算起始日期 yyyyMMdd
         */
        calStartDt?: string
        /**
         * 层级
         * 1-理财师、
         * 3-分总、
         * 4-区域执行副总、
         * 5-区域总、
         * 6-销售总监
         */
        configLevel?: string
        /**
         * 分成类型
         * 2：育成、
         * 3：异动-协商、
         * 4：异动-规则、
         * 5：重复划转-协商、
         * 6：重复划转-规则
         * 7: 重复划转-新老划断
         * 8: 接管-单客户
         * 9：接管-人力
         */
        configType?: string
        /**
         * 客户号列表
         */
        custNoList?: string[]
        /**
         * 计入比例_原
         */
        formerCalRate?: number
        /**
         * 原投顾
         */
        formerConsCode?: string
        /**
         * 锁定存续D
         */
        lockDurationAmt?: number
        /**
         * 计入比例_新
         */
        newlyCalRate?: number
        /**
         * 新投顾
         */
        newlyConsCode?: string
        /**
         * 备注
         */
        remark?: string
        /**
         * 是否生效 1-是 0-否
         */
        validFlag?: string
    }
    interface CustInfoParam {
        consCode: string
        custNo: string
        custName: string
        /**
         * 模糊搜索 字符串
         */
        fuzzyTerm: string
        /**
         * 客户分配原因
         *
         * 11-同行分配,1-重复客户,2-日常划转,99-其他,3-离职划转,4-在途新人入职,5-客户400呼入,10-分享链接,6-Leads分配,7-20w手动划转,8-深潜,9-其他（客服）
         */
        reasonCode: string
        formerConsCode: string
        formerOrgCode: string
        newlyConsCode: string
        newlyOrgCode: string
        /**
         * 标签-高端成交  (1-是|选中)
         */
        gdcjlabel: string
        /**
         * 标签-公募存量1万及以上的客户  (1-是|选中)
         */
        lsclLabel: string
    }

    export { StockConfigParam, SaveConfigReq, CustInfoParam, TradeInfoReq, FundInfoReq }
}
