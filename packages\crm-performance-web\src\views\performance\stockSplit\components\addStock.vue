<!--
 * @Description: 添加页/编辑/审核
 * @Author: chaohui.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-02-18 16:32:51
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addStock.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="900px"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :before-close="dialogHandleClose"
        :close-on-click-modal="false"
        @keyup.enter.stop="confirmFn(dialogForm)"
    >
        <div class="stock-form-module">
            <el-form
                ref="dialogForm"
                class="crm_dialog_form"
                label-width="140px"
                :model="formList"
                :rules="formRules"
                :disabled="false"
                :scroll-to-error="true"
                validate-on-rule-change
                @submit.prevent
            >
                <el-form-item
                    v-if="isShowFlag('configType')"
                    label="分成类型"
                    prop="configType"
                    :inline-message="true"
                >
                    <crm-select
                        v-model="formList.configType"
                        :disabled="isShowFlag('configType', '1')"
                        placeholder="请选择分成类型"
                        label-format="label"
                        value-format="key"
                        :option-list="excludeArr(CONFIG_TYPE, [''])"
                        :style="{ width: '180px' }"
                        @change="initLevel"
                    />
                    <!-- @change="resetdefaultKey('all')" -->
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('validFlag')"
                    label="是否生效"
                    prop="validFlag"
                    :inline-message="true"
                >
                    <crm-select
                        v-model="formList.validFlag"
                        placeholder="请选择是否生效"
                        :disabled="isShowFlag('validFlag', '1')"
                        label-format="label"
                        value-format="key"
                        :option-list="excludeArr(VALID_FLAG, [''])"
                        :style="{ width: '180px' }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('configLevel')"
                    label="层级"
                    prop="configLevel"
                    :inline-message="true"
                >
                    <crm-select
                        v-model="formList.configLevel"
                        placeholder="请选择层级"
                        :disabled="isShowFlag('configLevel', '1')"
                        label-format="label"
                        value-format="key"
                        :option-list="configLevelMap"
                        :style="{ width: '180px' }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('constObj')"
                    label="新管理层/投顾"
                    prop="constObj"
                    :inline-message="true"
                >
                    <ReleatedSelect
                        v-model="formList.constObj"
                        :organization-list="organizationList || []"
                        :cons-list-default="
                            moduleTyping.isEdit ? formList.constObj.consList : consultList
                        "
                        :default-org-code="formList.constObj.orgCode"
                        :default-cons-code="formList.constObj.consCode"
                        :disabled="isShowFlag('constObj', '1')"
                    ></ReleatedSelect>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('formerConstObj')"
                    label="原管理层/投顾"
                    prop="formerConstObj"
                    :inline-message="true"
                >
                    <ReleatedSelect
                        v-model="formList.formerConstObj"
                        :organization-list="formerOrganizationList || []"
                        :cons-list-default="
                            moduleTyping.isEdit
                                ? formList.formerConstObj.consList
                                : formerConsultList
                        "
                        :default-org-code="formList.formerConstObj.orgCode"
                        :default-cons-code="formList.formerConstObj.consCode"
                        :disabled="isShowFlag('formerConstObj', '1')"
                    ></ReleatedSelect>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('relatedConsCodeList')"
                    label="涉及投顾"
                    prop="relatedConsCodeList"
                    :inline-message="true"
                >
                    <aside class="cust-list-box">
                        <!-- :remote-method="remoteMethod"
                        placeholder="输入客户号搜索" 
                        filterable
                        remote
                    -->
                        <el-select-v2
                            v-model="formList.relatedConsCodeList"
                            size="small"
                            multiple
                            clearable
                            :placeholder="
                                moduleTyping.isAdd || moduleTyping.isEdit
                                    ? '点击右侧搜索按钮添加'
                                    : ' '
                            "
                            :options="relatedOptions"
                            remote-show-suffix
                            reserve-keyword
                            collapse-tags
                            :max-collapse-tags="3"
                            :style="{ width: '320px' }"
                            :disabled="
                                relatedOptions?.length === 0 ||
                                isShowFlag('relatedConsCodeList', '1')
                            "
                        >
                            <!-- <el-option
                                v-for="(item, index) in relatedOptions"
                                :key="`option-tpl-${index}`"
                                :label="item.label"
                                :value="item.value"
                            /> -->
                        </el-select-v2>
                        <div
                            v-show="moduleTyping.isAdd || moduleTyping.isEdit"
                            class="search-box"
                            @click="showAddRelated"
                        >
                            <el-icon><Search /></el-icon>
                        </div>
                    </aside>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('activeFlag')"
                    label="激活状态"
                    prop="activeFlag"
                    :inline-message="true"
                >
                    <crm-select
                        v-model="formList.activeFlag"
                        placeholder="请选择激活状态"
                        label-format="label"
                        value-format="key"
                        :disabled="isShowFlag('activeFlag', '1')"
                        :option-list="excludeArr(VALID_FLAG, [''])"
                        :style="{ width: '180px' }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('activeDt')"
                    label="激活时间"
                    prop="activeDt"
                    :inline-message="true"
                >
                    <crm-date-picker
                        v-model="formList.activeDt"
                        placeholder="请选择激活时间"
                        show-format="YYYY-MM-DD"
                        value-format="YYYYMMDD"
                        class-name="w180"
                        :disabled="isShowFlag('activeDt', '1')"
                        :limit-less-than-current="false"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('calStartDt')"
                    label="计算起始日"
                    prop="calStartDt"
                    :inline-message="true"
                >
                    <crm-date-picker
                        v-model="formList.calStartDt"
                        placeholder="请选择计算起始日"
                        show-format="YYYY-MM-DD"
                        value-format="YYYYMMDD"
                        class-name="w180"
                        :disabled="isShowFlag('calStartDt', '1')"
                        :disabled-date="{ endDate: formList.calEndDt }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('calEndDt')"
                    label="计算截止日"
                    prop="calEndDt"
                    :inline-message="true"
                >
                    <crm-date-picker
                        v-model="formList.calEndDt"
                        placeholder="请选择计算截止日"
                        show-format="YYYY-MM-DD"
                        value-format="YYYYMMDD"
                        class-name="w180"
                        :disabled="isShowFlag('calEndDt', '1')"
                        :disabled-date="{
                            startDate: formList.calStartDt
                        }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('custNoList')"
                    label="客户"
                    prop="custNoList"
                    :inline-message="true"
                >
                    <aside v-if="!moduleTyping.isCheck" class="cust-list-box">
                        <!-- :remote-method="remoteMethod"
                        placeholder="输入客户号搜索" 
                        filterable
                        remote
                    -->
                        <!-- <el-select
                            v-model="formList.custNoList"
                            size="small"
                            multiple
                            clearable
                            placeholder="点击右侧搜索按钮添加"
                            remote-show-suffix
                            reserve-keyword
                            collapse-tags
                            :max-collapse-tags="3"
                            :style="{ width: '320px' }"
                            :disabled="custOptions?.length === 0 || isShowFlag('custNoList', '1')"
                        >
                            <el-option
                                v-for="(item, index) in custOptions"
                                :key="`option-tpl-${index}`"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select> -->
                        <el-select-v2
                            v-model="formList.custNoList"
                            size="small"
                            multiple
                            clearable
                            placeholder="点击右侧搜索按钮添加"
                            remote-show-suffix
                            reserve-keyword
                            collapse-tags
                            :max-collapse-tags="3"
                            :options="custOptions"
                            :style="{ width: '320px' }"
                            :disabled="custOptions?.length === 0 || isShowFlag('custNoList', '1')"
                        >
                            <!-- <el-option
                                v-for="(item, index) in custOptions"
                                :key="`option-tpl-${index}`"
                                :label="item.label"
                                :value="item.value"
                            /> -->
                        </el-select-v2>
                        <div
                            v-show="moduleTyping.isAdd || moduleTyping.isEdit"
                            class="search-box"
                            @click="showAddCustom"
                        >
                            <el-icon><Search /></el-icon>
                        </div>
                    </aside>
                    <aside v-else>
                        <base-button
                            size="small"
                            :text="true"
                            :underline="false"
                            link
                            @click="showCustomDetail"
                            >明细</base-button
                        >
                    </aside>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('formerCalRate')"
                    label="计入比例_原（%）"
                    prop="formerCalRate"
                    :inline-message="false"
                >
                    <el-input-number
                        v-model="formList.formerCalRate"
                        :precision="0"
                        :step="1"
                        :min="0"
                        :max="100"
                        size="small"
                        type="number"
                        style="width: 180px"
                        :value-on-clear="null"
                        :controls="false"
                        :disabled="isShowFlag('formerCalRate', '1')"
                    >
                        <template #append>%</template>
                    </el-input-number>
                    <span class="txt-red">填写示例30%</span>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('newlyCalRate')"
                    label="计入比例_新（%）"
                    prop="newlyCalRate"
                    :inline-message="false"
                >
                    <el-input-number
                        v-model="formList.newlyCalRate"
                        :precision="0"
                        :step="1"
                        :min="0"
                        :max="100"
                        size="small"
                        type="number"
                        style="width: 180px"
                        :value-on-clear="null"
                        :controls="false"
                        :disabled="isShowFlag('newlyCalRate', '1')"
                    >
                    </el-input-number>
                    <span class="txt-red">填写示例30%</span>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('lockDurationAmt')"
                    label="锁定存续D（万）"
                    prop="lockDurationAmt"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.lockDurationAmt"
                        :placeholder="`请输入锁定存续（万）`"
                        :formatter="(value:string) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="(value:any)  => value.replace(/\$\s?|(,*)/g, '')"
                        :disabled="isShowFlag('lockDurationAmt', '1')"
                        :clearable="true"
                        class-name="w180"
                        onkeypress="return event.keyCode >= 4 && event.keyCode <= 57 "
                        @blur="handleCurNum('lockDurationAmt')"
                    >
                        <!-- :slot-list="['append']"
                    <template #append>万</template> -->
                    </crm-input>
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('remark')"
                    label="备注"
                    prop="remark"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.remark"
                        :clearable="true"
                        maxlength="300"
                        placeholder="请填写备注"
                        show-word-limit
                        type="textarea"
                        :disabled="isShowFlag('remark', '1')"
                        :style="{ width: '320px' }"
                        :autosize="{ minRows: 6, maxRows: 6 }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="isShowFlag('audtAdvice')"
                    label="审核意见"
                    prop="audtAdvice"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.audtAdvice"
                        :clearable="true"
                        maxlength="300"
                        show-word-limit
                        type="textarea"
                        :disabled="isShowFlag('audtAdvice', '1')"
                        :style="{ width: '320px' }"
                        :autosize="{ minRows: 1, maxRows: 6 }"
                        resize="none"
                    />
                </el-form-item>
            </el-form>
            <CustDetail
                v-if="detailStockVisiable"
                v-model="detailStockVisiable"
                :trans-data="stockObj"
            ></CustDetail>
            <!-- 添加客户页 -->
            <AddCustom
                v-if="addCustomStockVisiable"
                v-model="addCustomStockVisiable"
                :organization-list="organizationList"
                :consult-list="formList?.constObj?.consList"
                :org-code="formList?.constObj?.orgCode"
                :cons-code="formList?.constObj?.consCode"
                :former-organization-list="formerOrganizationList"
                :former-consult-list="formerConsultList"
                @custCallBack="mergeCustList"
            ></AddCustom>
            <!-- 新增涉及投顾页面 -->
            <AddRelated
                v-if="showAddRelatedVisiable"
                v-model="showAddRelatedVisiable"
                :organization-list="formerOrganizationList"
                :trans-data="addRelatedObj"
                @callBack="mergeRelatedList"
            ></AddRelated>
        </div>
        <template #footer>
            <div v-if="moduleTyping.isCheck">
                <crm-button plain size="small" :radius="true" @click="confirmFn(dialogForm, '0')"
                    >不通过</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn(dialogForm, '1')"
                    >通过</crm-button
                >
            </div>
            <div v-else>
                <crm-button plain size="small" :radius="true" @click="dialogHandleClose"
                    >关 闭</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn(dialogForm)"
                    >确 认</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import type { FormInstance, FormRules } from 'element-plus'
    import { Key, Search } from '@element-plus/icons-vue'
    import {
        deepClone,
        fetchRes,
        message,
        messageBox,
        excludeArr,
        fetchAll,
        zeroTrans
    } from '@common/utils/index'
    import ReleatedSelect from './releatedSelect.vue'
    import CustDetail from './custDetail.vue'
    import AddCustom from './addCustom.vue'
    import AddRelated from './addRelated.vue' // 新增添加涉及投顾页面
    import {
        queryCustInfo,
        queryEdit,
        queryAudt,
        updateConfig,
        addSave,
        audtiList,
        queryConsultant
    } from '@/api/project/stockSplit/stockSplitList'

    import {
        CONFIG_TYPE,
        VALID_FLAG,
        CONFIG_LEVEL,
        CONFIG_TYPE_SELECT,
        CONFIG_LEVEL_SELECT
    } from '@/constant/index'
    import { useDiaVisible } from '../scripts/hooks/useDiaVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            consCustNo?: string
            consCode?: string
            orgCode?: string
            formerConsCode?: string
            formerOrgCode?: string
            consultList?: any[]
            organizationList?: any[]
            formerConsultList?: any[]
            formerOrganizationList?: any[]
            transData?: {
                title: string
                type: string
                id: string
            }
        }>(),
        {
            visibleCus: true,
            consCustNo: '',
            consCode: '',
            orgCode: '',
            formerConsCode: '',
            formerOrgCode: '',
            consultList: () => [],
            organizationList: () => [],
            formerConsultList: () => [],
            formerOrganizationList: () => [],
            transData: () => {
                return {
                    title: '新增',
                    type: 'add',
                    id: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    /**
     * @description: 详情
     * @return {*}
     */
    const stockObj = ref({
        title: '客户详情',
        id: props?.transData?.id ?? '',
        type: 'customDetail',
        custList: [],
        isAudit: false
    })
    const detailStockVisiable = ref<boolean>(false)
    const showCustomDetail = () => {
        if (moduleTyping.value.isCheck) {
            stockObj.value.type = 'check'
            stockObj.value.custList = formList.value.custList
            stockObj.value.isAudit = true
        }
        detailStockVisiable.value = true
    }

    /**
     * @description: 添加客户信息
     * @return {*}
     */
    const addCustomStockVisiable = ref<boolean>(false)
    const showAddCustom = () => {
        addCustomStockVisiable.value = true
    }

    /**
     * @description: 添加关联投顾信息弹框
     * @return {*}
     */
    const addRelatedObj = ref()
    const showAddRelatedVisiable = ref<boolean>(false)
    const showAddRelated = () => {
        console.log(unref(formList.value.relatedConsCodeList))
        //传参到添加页面，过滤删掉的
        addRelatedObj.value = {
            selectedCons: unref(relatedOptions).filter(item =>
                unref(formList.value.relatedConsCodeList).includes(item.value)
            )
        }
        showAddRelatedVisiable.value = true
    }

    /**
     * @description: form校验规则
     * @return {*}
     */
    const listStr = [
        'configType',
        'validFlag',
        'configLevel',
        'formerConstObj',
        'constObj',
        'activeFlag',
        'calStartDt',
        'calEndDt',
        'custNoList',
        'formerCalRate',
        'newlyCalRate'
    ]
    const rulesList = ref(listStr)

    const formRules = computed<FormRules>(() => {
        const rulesMap: any = {
            configType: [
                { required: true, message: '请选择分成类型数据', trigger: ['blur', 'change'] }
            ],
            validFlag: [{ required: true, message: '请选择是否生效', trigger: ['blur', 'change'] }],
            configLevel: [{ required: true, message: '请选择层级', trigger: ['blur'] }],
            formerConstObj: [
                { required: true, message: '请选择原管理层/投顾', trigger: ['blur', 'change'] }
            ],
            constObj: [
                { required: true, message: '请选择新管理层/投顾', trigger: ['blur', 'change'] }
            ],
            activeFlag: [
                { required: true, message: '请选择激活状态', trigger: ['blur', 'change'] }
            ],
            calStartDt: [
                { required: true, message: '请选择计算起始日期', trigger: ['blur', 'change'] }
            ],
            calEndDt: [
                { required: true, message: '请选择计算截止日期', trigger: ['blur', 'change'] }
            ],
            custNoList: [{ required: true, message: '数据不能为空', trigger: ['blur', 'change'] }],
            formerCalRate: [
                { required: true, message: '请选择计入比例原', trigger: ['blur', 'change'] }
            ],
            newlyCalRate: [
                { required: true, message: '请选择计入比例新', trigger: ['blur', 'change'] }
            ],
            overseasRatio: [
                {
                    required: true,
                    pattern: /^([1-9]\d*(\.\d*[1-9])?)|(0\.[1-9]\d*|[1-9]\d*(\.\d+)?)$/,
                    message: '支持录入正数，四舍五入保留两位小数',
                    trigger: ['change', 'blur']
                }
            ]
        }

        if (moduleTyping.value.isCheck) {
            return {}
        }
        // 过滤表单条件
        // const objTpl: any = {}
        // rulesList.value.map((item: string) => {
        //     if (rulesMap[item]) {
        //         objTpl[item] = rulesMap[item]
        //     }
        //     return item
        // })
        // 基于条件联动校验规则
        return rulesMap
    })

    class FormList {
        id = ''
        configType = ''
        constObj = {
            orgCode: '',
            consCode: '',
            consList: []
        }
        formerConstObj = {
            orgCode: '',
            consCode: '',
            consList: []
        }
        calDt = {
            startDate: '',
            endDate: ''
        }
        configLevel = ''
        custNo = ''
        auditStatus = ''
        validFlag = '1'
        activeFlag = ''
        calStartDt = ''
        calEndDt = ''
        custNoList = []
        custList = []
        relatedConsCodeList = [] // 涉及投顾
        activeDt = '' // 激活时间
        formerCalRate = null
        newlyCalRate = null
        lockDurationAmt = ''
        remark = ''
        audtAdvice = ''
    }

    const formList = ref<any>(new FormList())
    /**
     * @description: hooks
     * @return {*}
     */
    const { dialogVisible, moduleTyping, getParams, handleClose, verifyMethods } = useDiaVisible({
        emit,
        props,
        formList
    })

    /**
     * @description: 关闭弹框
     * @return {*}
     */
    const dialogForm = ref<FormInstance>()
    const dialogHandleClose = () => {
        // 关闭弹框后逻辑
        handleClose({
            msgInfo: '取消',
            callBack: () => {
                if (!dialogForm.value) {
                    return
                }
                dialogForm.value.resetFields()
            }
        })
    }

    /**
     * @description: 层级枚举联动
     * @param {*} computed
     * @return {*}
     */
    const configLevelMap = computed(() => {
        switch (formList.value.configType) {
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
                return excludeArr(CONFIG_LEVEL, [''])
            default:
                return excludeArr(CONFIG_LEVEL, ['', '1'])
        }
    })

    /**
     * @description: form表单禁用联动
     * @param {*} computed
     * @return {*}
     */
    const formDisableKeyArr = computed<string[]>(() => {
        let curStrList = ['activeFlag', 'activeDt']
        const editDisableArr = ['configLevel', 'audtAdvice']
        if (moduleTyping.value.isAdd) {
            curStrList = curStrList.filter(item => !curStrList.includes(item))
        } else if (moduleTyping.value.isEdit) {
            curStrList = [...editDisableArr]
        } else if (moduleTyping.value.isCheck) {
            curStrList = [
                ...listStr,
                'formerConstObj',
                'constObj',
                'activeFlag',
                'activeDt',
                'formerCalRate',
                'newlyCalRate',
                'lockDurationAmt',
                'remark'
            ]
        }
        switch (formList.value.configType) {
            case CONFIG_TYPE_SELECT.JG_RL:
                // 当 「分成类型」=接管-人力 时，该字段可编辑，必填项；
                return curStrList
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
            default:
                // 当「分成类型」=！接管-人力 时，该字段置灰不可编辑，非必填项
                return curStrList
        }
    })

    /**
     * @description: 监听配置类型变化，更新校验规则
     * @return {*}
     */
    watch(
        () => formList.value.configType,
        newConfigType => {
            switch (newConfigType) {
                case CONFIG_TYPE_SELECT.JG_RL:
                    // 当 「分成类型」=接管-人力 时，该字段可编辑，必填项；
                    rulesList.value = listStr
                    break
                case CONFIG_TYPE_SELECT.CFHZ_XS:
                case CONFIG_TYPE_SELECT.CFHZ_GZ:
                case CONFIG_TYPE_SELECT.CFHZ_XLHD:
                default:
                    // 当「分成类型」=！接管-人力 时，该字段置灰不可编辑，非必填项
                    const curStrList = formDisableKeyArr.value
                    rulesList.value = listStr.filter(item => !curStrList.includes(item))
                    break
            }
        },
        { immediate: true }
    )

    /**
     * @description: form表单展示联动
     * @param {*} computed
     * @return {*}
     */
    const formShowKeyArr = computed((): string[] => {
        const listKey = [
            'configType',
            'validFlag',
            'configLevel',
            'formerConstObj',
            'relatedConsCodeList',
            'constObj',
            'activeFlag',
            'activeDt',
            'calStartDt',
            'calEndDt',
            'custNoList',
            'formerCalRate',
            'newlyCalRate',
            'lockDurationAmt',
            'remark',
            'audtAdvice'
        ]
        const { configType, configLevel } = formList.value || {}
        // add
        let curStrList = ['activeFlag', 'activeDt', 'audtAdvice']
        const ratroList = ['formerCalRate', 'newlyCalRate']
        // edit
        if (moduleTyping.value.isEdit) {
            curStrList = ['activeFlag', 'activeDt']
        }
        // audt
        if (moduleTyping.value.isCheck) {
            curStrList = []
        }
        let showKeyArr = []
        switch (configType) {
            case CONFIG_TYPE_SELECT.YC:
                // 当 「分成类型」=育成，该字段置灰不可编辑，非必填；当「分成类型」！=育成，必填
                curStrList.push('custNoList')
                showKeyArr = listKey.filter(item => !curStrList.includes(item))
                break
            case CONFIG_TYPE_SELECT.JG_RL:
                showKeyArr = listKey
                break
            case CONFIG_TYPE_SELECT.YD_XS:
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
            default:
                // 当「分成类型」=！接管-人力 时，该字段置灰不可编辑，非必填项
                showKeyArr = listKey.filter(item => !curStrList.includes(item))
                break
        }

        switch (configType) {
            case CONFIG_TYPE_SELECT.YC:
            case CONFIG_TYPE_SELECT.YD_XS:
            case CONFIG_TYPE_SELECT.CFHZ_XS:
                break
            default:
                showKeyArr = showKeyArr.filter(item => !ratroList.includes(item))
                break
        }

        return showKeyArr
    })

    /**
     * @description: 展示选项或者禁止选择
     * @param {*} curKey
     * @param {*} type 1 禁止 默认展示
     * @return {*}
     */
    const isShowFlag = (curKey: string, type?: '1') => {
        switch (type) {
            case '1':
                return unref(formDisableKeyArr).includes(curKey)
            default:
                return formShowKeyArr.value.includes(curKey)
        }
    }

    /**
     * @description: 清空level选项
     * @return {*}
     */
    const initLevel = () => {
        formList.value.configLevel = ''
    }

    /**
     * @description: 层级联动交互
     * @return {*}
     */
    // const initConfigLevel = () => {
    //     if (moduleTyping.value.isAdd) {
    //         // 切换层级后重置客户默认下拉列表
    //         custOptions.value = []
    //         formList.value.custNoList = []
    //     }
    // }

    /**
     * @description: 获取用户信息
     * @param {*} query
     * @return {*}
     */
    const custOptions = ref<{ label: string; value: string }[]>([])
    const getCustInfo = ({ query = '' }) => {
        const { constObj, fuzzyTerm } = formList.value || {}
        const { orgCode: newlyOrgCode, consCode: newlyConsCode } = constObj || {}
        // if (formList.value.configLevel !== CONFIG_LEVEL_SELECT.AFP) {
        //     return (custOptions.value = [])
        // }
        fetchRes(queryCustInfo({ newlyConsCode, newlyOrgCode, fuzzyTerm: query }), {
            successCB: (res: any) => {
                // 请求成功
                const { rows } = res || { rows: [] }
                const custList = rows.map((item: any) => {
                    return {
                        label: `${item.custName} ${item.premiumAmount} ${item.retailAmount} `,
                        value: item.custNo
                    }
                })
                custOptions.value = custList
            },
            errorCB: () => {
                custOptions.value = []
            },
            catchCB: () => {
                custOptions.value = []
            },
            successTxt: '',
            failTxt: '获取客户信息失败!',
            fetchKey: ''
        })
    }

    /**
     * @description: 重复数组
     * @param {*} tempArr
     * @return {*}
     */
    const repatArr = (tempArr: any[], key: string) => {
        const newArr = []
        for (let i = 0; i < tempArr.length; i++) {
            if (newArr.indexOf(tempArr[i][key]) === -1) {
                newArr.push(tempArr[i][key])
            } else {
                tempArr.splice(i, 1)
            }
        }
        return tempArr
    }

    /**
     * @description: 合并去重复当前选中用户
     * @return {*}
     */
    const mergeCustList = ({
        val = [],
        optionList = []
    }: {
        val: string[]
        optionList: object[]
    }) => {
        const { custNoList } = formList.value || { custNoList: [] }
        formList.value.custNoList = Array.from(new Set([...custNoList, ...val]))
        const optionTpl =
            unref(optionList)?.map((item: any) => {
                return {
                    label: `${item.custName} ${item.premiumAmount} ${item.retailAmount} `,
                    value: item.custNo
                }
            }) || []
        const listTpl = ref([...optionTpl, ...custOptions.value])
        custOptions.value = repatArr(listTpl.value, 'value')
    }

    /**
     * @description: 合并去重复当前选中涉及客户数据
     * @return {*}
     */
    const relatedOptions = ref<{ label: string; value: string }[]>([])
    const mergeRelatedList = ({
        val = [],
        optionList = []
    }: {
        val: string[]
        optionList: object[]
    }) => {
        const { relatedConsCodeList } = formList.value || { relatedConsCodeList: [] }
        formList.value.relatedConsCodeList = Array.from(
            new Set([...relatedConsCodeList, ...val.map((item: any) => item.consCode)])
        )
        const optionTpl =
            unref(optionList)?.map((item: any) => {
                return {
                    label: `${item.consName} `,
                    value: item.consCode
                }
            }) || []
        relatedOptions.value = repatArr([...optionTpl, ...relatedOptions.value], 'value')
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [
            () => props?.formerConsCode,
            () => props?.formerOrgCode,
            () => props?.consCode,
            () => props?.orgCode
        ],
        (newVal, oldVal) => {
            const { formerOrgCode, formerConsCode, consCode, orgCode } = props || {}
            if (moduleTyping.value.isAdd) {
                if (formerOrgCode || formerConsCode) {
                    formList.value.formerConstObj.orgCode = formerOrgCode
                    formList.value.formerConstObj.consCode = formerConsCode
                }
                if (consCode || orgCode) {
                    formList.value.constObj.orgCode = orgCode
                    formList.value.constObj.consCode = consCode
                }
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description: 获取机构下投顾
     * @return {*}
     */
    const getConsCode = (orgCode: string, type = 1) => {
        fetchRes(queryConsultant({ orgCode, recursive: '1' }), {
            successCB: (res: any) => {
                const { rows } = res || { rows: [] }
                if (type === 1) {
                    formList.value.formerConstObj.consList = rows
                } else {
                    formList.value.constObj.consList = rows
                }
            },
            errorCB: () => {
                if (type === 1) {
                    formList.value.formerConstObj.consList = []
                } else {
                    formList.value.constObj.consList = []
                }
            },
            catchCB: () => {
                if (type === 1) {
                    formList.value.formerConstObj.consList = []
                } else {
                    formList.value.constObj.consList = []
                }
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(queryEdit({ id: props.transData.id }), {
            successCB: (res: any) => {
                // 编辑初始化
                const {
                    calEndDt,
                    calStartDt,
                    newlyConsCode,
                    newlyOrgCode,
                    formerConsCode,
                    formerOrgCode,
                    lockDurationAmt,
                    detailList,
                    formerCalRate,
                    newlyCalRate,
                    relatedConsList
                } = res || {}
                custOptions.value =
                    detailList?.map((item: any) => {
                        return {
                            label: `${item.custName}`,
                            value: item.custNo
                        }
                    }) || []
                relatedOptions.value =
                    relatedConsList?.map((item: any) => {
                        return {
                            label: `${item.consName}`,
                            value: item.consCode
                        }
                    }) || []
                formList.value = {
                    ...res,
                    constObj: {
                        orgCode: newlyOrgCode,
                        consCode: newlyConsCode
                    },
                    formerConstObj: {
                        orgCode: formerOrgCode,
                        consCode: formerConsCode
                    },
                    calDt: {
                        startDate: calStartDt,
                        endDate: calEndDt
                    },
                    formerCalRate: zeroTrans({ val: formerCalRate }),
                    newlyCalRate: zeroTrans({ val: newlyCalRate }),
                    custNoList: detailList?.map((item: any) => item.custNo),
                    lockDurationAmt:
                        lockDurationAmt === ''
                            ? ''
                            : zeroTrans({
                                  val: Number(lockDurationAmt) / 10000,
                                  fixed: 2
                              }),
                    relatedConsCodeList: relatedConsList?.map((item: any) => item.consCode)
                }
                // 初始化投顾
                fetchAll([getConsCode(formerOrgCode, 1), getConsCode(newlyOrgCode, 2)], {})
                // getConsCode(formerOrgCode, 1)
                // // 防止系统触发操作
                // setTimeout(() => {
                //     getConsCode(newlyOrgCode, 2)
                // }, 100)
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 审核初始化
     * @return {*}
     */
    const audtList = () => {
        fetchRes(queryAudt({ id: props.transData.id }), {
            successCB: (res: any) => {
                // 编辑初始化
                const {
                    calEndDt,
                    calStartDt,
                    newlyConsCode,
                    newlyOrgCode,
                    formerConsCode,
                    formerOrgCode,
                    lockDurationAmt,
                    detailList,
                    formerCalRate,
                    newlyCalRate,
                    relatedConsList
                } = res || {}
                relatedOptions.value =
                    relatedConsList?.map((item: any) => {
                        return {
                            label: `${item.consName}`,
                            value: item.consCode
                        }
                    }) || []
                formList.value = {
                    ...res,
                    constObj: {
                        orgCode: newlyOrgCode,
                        consCode: newlyConsCode
                    },
                    formerConstObj: {
                        orgCode: formerOrgCode,
                        consCode: formerConsCode
                    },
                    calDt: {
                        startDate: calStartDt,
                        endDate: calEndDt
                    },
                    formerCalRate: zeroTrans({ val: formerCalRate }),
                    newlyCalRate: zeroTrans({ val: newlyCalRate }),
                    custNoList: detailList?.map((item: any) => item.id),
                    custList: detailList,
                    lockDurationAmt:
                        lockDurationAmt === ''
                            ? ''
                            : zeroTrans({
                                  val: Number(lockDurationAmt) / 10000,
                                  fixed: 2
                              }),
                    relatedConsCodeList: relatedConsList?.map((item: any) => item.consCode)
                }
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 失去焦点重置数字输入框
     * @param {*} key
     * @return {*}
     */
    const handleCurNum = (key: string) => {
        const numTrans = Number(formList.value[key].replace(/\$\s?|(,*)/g, ''))
        formList.value[key] = numTrans && numTrans > 0 ? numTrans.toFixed(2) : ''
    }

    /**
     * @description: 表单提交
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = async (formEl: FormInstance | undefined, flag?: string | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                if (moduleTyping.value.isEdit) {
                    fetchRes(updateConfig(getParams()), {
                        successCB: (res: any) => {
                            const { returnCode, description } = res || {}
                            verifyMethods({
                                flag: returnCode,
                                description,
                                defaultFileDes: '修改失败请重试!',
                                defaultSuccessDes: '修改成功',
                                formEl
                            })
                        },
                        successTxt: '',
                        failTxt: '',
                        fetchKey: ''
                    })
                } else if (moduleTyping.value.isAdd) {
                    fetchRes(addSave(getParams()), {
                        successCB: (res: any) => {
                            const { returnCode, description } = res || {}
                            verifyMethods({
                                flag: returnCode,
                                description,
                                defaultFileDes: '修改失败请重试!',
                                defaultSuccessDes: '修改成功',
                                formEl
                            })
                        },
                        successTxt: '',
                        failTxt: '',
                        fetchKey: ''
                    })
                } else if (moduleTyping.value.isCheck) {
                    fetchRes(audtiList(getParams(flag)), {
                        successCB: (res: any) => {
                            const { returnCode, description } = res || {}
                            verifyMethods({
                                flag: returnCode,
                                description,
                                defaultFileDes: '审核失败请重试!',
                                defaultSuccessDes: '审核成功',
                                formEl
                            })
                        },
                        successTxt: '',
                        failTxt: '',
                        fetchKey: ''
                    })
                }
            }
        })
    }

    onBeforeMount(() => {
        if (moduleTyping.value.isAdd) {
            // 初始化新管理层数据
            formList.value.constObj.consCode = props.consCustNo
            formList.value.constObj.orgCode = props.orgCode
        } else if (moduleTyping.value.isEdit) {
            fetchList()
        } else if (moduleTyping.value.isCheck) {
            audtList()
        }
    })
</script>

<style lang="less" scoped>
    .is-no-data {
        padding: 15vh 0;
        text-align: center;
    }

    .txt-red {
        margin-left: 15px;
        font-size: 12px;
        color: red;
    }

    .cust-list-box {
        display: flex;
        align-items: center;
    }

    :deep(.el-select__tags) {
        .el-tag__content {
            max-width: 28px;
            overflow: hidden;
        }
    }

    .search-box {
        width: 40px;
        height: 24px;
        padding-left: 15px;
        font-size: 18px;
        color: @font_link_more;
        cursor: pointer;
    }

    .crm_dialog_form {
        width: 100%;
        height: 100%;

        .el-form-item {
            margin-bottom: 10px;

            :deep(.el-input-number) {
                text-align: left;
            }
        }

        :deep(.el-input) {
            .el-input__wrapper {
                padding-left: 10px;

                .el-input__inner {
                    text-align: left;
                }
            }
        }
    }
</style>
