{
    "compilerOptions": {
        "outDir": "./",
        "baseUrl": "./",
        "paths": {
            "@common/*": ["common/*"]
        },
        "target": "ES5",
        "module": "ESNext",
        "sourceMap": true, // 生成相应的 .map文件。
        "useDefineForClassFields": true,
        "moduleResolution": "Node",
        "strict": true,
        "jsx": "preserve",
        "resolveJsonModule": true,
        "esModuleInterop": true,
        "lib": ["ESNext", "DOM"],
        "allowJs": true,
        "isolatedModules": true,
        "skipLibCheck": true,
        "noEmit": true
    },
    "include": [
        "common/*.ts",
        "common/**/*.ts",
        "common/**/**/*.ts",
        "common/**/**/**/*.ts",
        "common/**/**/**/**/*.ts",
        "common/*.vue",
        "common/**/*.vue",
        "common/**/**/*.vue",
        "common/**/**/**/*.vue",
        "common/**/**/**/**/*.vue",
        "*.ts",
        "*.vue"
    ],
    "exclude": ["node_modules", "dist", "*.test.ts"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
