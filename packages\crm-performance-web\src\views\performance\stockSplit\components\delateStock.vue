<!--
 * @Description: 删除弹框
 * @Author: chaohui.wu
 * @Date: 2023-09-12 11:22:38
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 18:32:58
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/delateStock.vue
 *  
-->

<template>
    <crm-dialog
        v-model="dialogVisible"
        width="770px"
        title="删除"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <p class="txt-center">确认删除数据?</p>
        <el-form
            ref="dialogForm"
            class="crm_dialog_form"
            label-width="100px"
            :model="formList"
            :rules="formRules"
            :disabled="false"
            :scroll-to-error="true"
            validate-on-rule-change
            @submit.prevent
        >
            <el-form-item label="删除原因" prop="advice" :inline-message="false">
                <crm-input v-model="formList.advice"></crm-input>
            </el-form-item>
        </el-form>
        <!-- 删除确认 -->
        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="dialogHandleClose(dialogForm)"
                    >取 消</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn(dialogForm)"
                    >确 认</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import type { FormInstance, FormRules } from 'element-plus'
    import { deepClone, fetchRes, message, excludeArr } from '@common/utils/index'
    import { audtiList, deleteConfig } from '@/api/project/stockSplit/stockSplitList'
    import { useDiaVisible } from '../scripts/hooks/useDiaVisible'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            transData?: any
        }>(),
        {
            visibleCus: true,
            transData: () => {
                return {
                    id: '',
                    title: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    const rulesList = ref({})
    const formRules = computed<FormRules>(() => {
        const rulesMap: any = {
            advice: [{ required: true, message: '请输入删除原因', trigger: ['blur', 'change'] }]
        }
        // 基于条件联动校验规则
        return rulesMap
    })
    class FormList {
        id = ''
        advice = ''
    }
    const formList = ref<any>(new FormList())

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    // const dialogVisible = computed({
    //     get() {
    //         return props.visibleCus
    //     },
    //     set(newVal) {
    //         return emit('update:modelValue', newVal)
    //     }
    // })

    const dialogForm = ref<FormInstance>()

    /**
     * @description: hooks
     * @return {*}
     */
    const { dialogVisible, moduleTyping, getParams, handleClose, verifyMethods } = useDiaVisible({
        emit,
        props,
        formList
    })

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const dialogHandleClose = (formEl: FormInstance | undefined) => {
        handleClose({
            msgInfo: '取消',
            callBack: () => {
                if (!formEl) {
                    return formEl
                }
                dialogVisible.value = false
                formEl.resetFields()
            }
        })
    }

    /**
     * @description: 表单提交
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = async (formEl: FormInstance | undefined, flag?: string | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                fetchRes(deleteConfig({ ...formList.value, id: props.transData.id }), {
                    successCB: (res: any) => {
                        const { returnCode, description } = res
                        // if (returnCode === '0000') {
                        emit('callBack')
                        dialogVisible.value = false
                        // 重置
                        formEl.resetFields()
                        // } else {
                        //     message({
                        //         type: 'error',
                        //         message: description || '保存失败请重试!'
                        //     })
                        // }
                    },
                    successTxt: '删除成功',
                    failTxt: '删除失败请重试',
                    fetchKey: ''
                })
            }
        })
    }
</script>

<style lang="less" scoped>
    .txt-center {
        margin-bottom: 10px;
        margin-left: 30px;
        font-weight: bold;
        text-align: left;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
