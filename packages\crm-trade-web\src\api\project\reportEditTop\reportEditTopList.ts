/*
 * @Description: 报告编辑顶部
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-25 22:54:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:12:59
 * @FilePath: /crm-rpo-template/packages/crm-template/src/api/project/reportEditTop/reportEditTopList.ts
 *
 */

import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import { MaxInvestParams, ReportNameParams, TopInfoParams } from './type/apiType'

/**
 * @description: 保存最大可投资金额
 * @param {MaxInvestParams} params
 * @return {*}
 */
export const saveMaxInvest = (params: MaxInvestParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/savemaxinvest',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 保存报告标题
 * @param {ReportNameParams} params
 * @return {*}
 */
export const saveReportName = (params: ReportNameParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/savereportname',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询顶部报告名及最大可投资金额
 * @param {TopInfoParams} params
 * @return {*}
 */
export const topInfo = (params: TopInfoParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/topinfo',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 编辑页顶部目录
 * @param {TopInfoParams} params
 * @return {*}
 */
export const listMenu = (params: {
    showOptimalConfig?: string
    externalBalanceWay?: string
    assetId: string
    conscustNo: string
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/listassetdirectory',
            method: 'post',
            data: params
        })
    )
}
