import { axiosRequest } from '@common/utils/index'

// export function coverAuthTypesToDataRole(data: any) {
//     return axiosRequest({
//         url: '/dataRole/coverAuthTypesToDataRole',
//         method: 'post',
//         data
//     })
// }

// export function queryDataTypeListByRoleId(data: any) {
//     return axiosRequest({
//         url: '/dataRole/queryDataTypeListByRoleId',
//         method: 'get',
//         params: data
//     })
// }

// 保存双录模版
export function addtemplate(data: any) {
    return axiosRequest({
        url: '/doubletrade/template/addtemplate',
        method: 'post',
        data
    })
}

// 基金搜索筛选接口
export function searchfund(data: any) {
    return axiosRequest({
        url: '/doubletrade/template/searchfund',
        method: 'get',
        params: data
    })
}
