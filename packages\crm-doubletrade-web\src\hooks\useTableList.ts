/**
 * @description: 列表搜索hooks
 */
import { ref, unref, onBeforeMount } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { responseCode } from '@/constant/index'
import { useRoute } from 'vue-router'

interface ListItem {
    [key: string]: any
}

/**
 * @description: 页面table列表公共hooks
 */
export function useTableList({ searchData, fetchListRequest, removeRequest }: any) {
    const total = ref(0)
    const pageInfo = ref({
        page: 1,
        perPage: 10
    })
    const listLoading = ref(false)
    const tableData = ref([])

    // 请求接口列表
    const fetchTableList = async () => {
        listLoading.value = true
        const params = {
            ...unref(searchData),
            ...pageInfo.value
        }

        try {
            const res: any = await fetchListRequest(params)
            tableData.value = res?.body?.records || []
            total.value = res.body.total
        } catch (e) {
            tableData.value = []
        }

        listLoading.value = false
    }

    // 搜索
    const handleSearch = async () => {
        pageInfo.value.page = 1
        await fetchTableList()
    }

    // 分页变更
    const handlePageChange = async (current: number, perPage: number) => {
        pageInfo.value.page = current
        pageInfo.value.perPage = perPage
        await fetchTableList()
    }

    // 输入搜索事件---防抖
    const timeOut = ref()
    const handleInput = (): void => {
        if (timeOut.value) {
            clearTimeout(timeOut.value)
        }
        timeOut.value = setTimeout(() => {
            pageInfo.value.page = 1
            fetchTableList()
            clearTimeout(timeOut.value)
            timeOut.value = null
        }, 500)
    }

    // 删除
    const handleRemove = (row: ListItem) => {
        ElMessageBox.confirm('是否确认删除该条数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(async () => {
            try {
                const res = await removeRequest({ id: row.id, jgdm: row.jgdm, jjdm: row.jjdm })
                if (res?.code === responseCode.SUCCESS || res?.code === responseCode.CRM_SUCCESS) {
                    ElMessage({
                        message: '删除成功',
                        showClose: true,
                        type: 'success'
                    })
                    fetchTableList()
                }
            } catch (e) {
                ElMessage({
                    message: '删除失败',
                    showClose: true,
                    type: 'error'
                })
            }
        })
    }

    onBeforeMount(async () => {
        await fetchTableList()
    })

    return {
        tableData,
        pageInfo,
        total,
        listLoading,
        handleInput,
        handleSearch,
        handlePageChange,
        handleRemove
    }
}
