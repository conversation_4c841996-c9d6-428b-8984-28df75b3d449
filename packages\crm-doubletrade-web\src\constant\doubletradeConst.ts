/*
 * @Description: 存量分成枚举
 * @Author: chaohui.wu
 * @Date: 2023-08-03 15:44:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 14:08:11
 * @FilePath: /crm-web/packages/crm-performance-web/src/constant/stockConst.ts
 *
 */
import { transMapToArray } from '@common/utils'
/**
 * @description: 产品和机构类型
 * @return {*}
 */
enum IS_FLAG {
    YES = '1',
    NO = '0',
    ALL = ''
}

/**
 * @description: 分成列表枚举集合
 * @return {*}
 */
export const TEMPLATE_LIST_MAP = {
    // 客户类型枚举
    CUSTTYPE_MAP: new Map([
        ['', '全部'],
        ['1', '个人'],
        // ['9', '基金'],
        ['0', '机构']
        // ['2', '产品']
    ]),
    // 成单方式枚举
    PRETYPE_MAP: new Map([
        ['', '全部'],
        ['1', '纸质成单'],
        ['2', '电子成单']
    ]),
    // 模版适用类型枚举
    TEMPTYPE_MAP: new Map([
        ['', '全部'],
        ['1', '指定产品'],
        ['2', '指定条件']
    ]),
    // 审核状态枚举
    CHECKFLAG_MAP: new Map([
        ['', '全部'],
        ['1', '待审核'],
        ['2', '审核通过'],
        ['3', '审核不通过']
    ])
}

/**
 * @description: 客户类型
 * @param {*} Array
 * @return {*}
 */
export const CUSTTYPE_OPTION = transMapToArray(TEMPLATE_LIST_MAP.CUSTTYPE_MAP)

/**
 * @description: 成单方式
 * @return {*}
 */
export const PRETYPE_OPTION = transMapToArray(TEMPLATE_LIST_MAP.PRETYPE_MAP)

/**
 * @description: 模版适用类型
 * @return {*}
 */
export const TEMPTYPE_OPTION = transMapToArray(TEMPLATE_LIST_MAP.TEMPTYPE_MAP)
/**
 * @description: 审核状态
 * @return {*}
 */
export const CHECKFLAG_OPTION = transMapToArray(TEMPLATE_LIST_MAP.CHECKFLAG_MAP)
