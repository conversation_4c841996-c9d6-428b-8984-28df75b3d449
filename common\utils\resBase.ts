/*
 * @Description: 返回结果通用处理逻辑
 * @Author: chaohui.wu
 * @Date: 2023-03-28 16:47:02
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-20 18:52:35
 * @FilePath: /crm-web/common/utils/resBase.ts
 * fetchRes(
        saveMaxInvest({
            conscustno,
            amount: topInfoListChange.value.maxInvest
        }),
        {
            successCB: () => getTopInfo(),
            errorCB: null,
            successTxt: '',
            failTxt: '最大可投资金额保存失败',
            fetchKey: ''
        }
    )
 */

import { returnCodeRes, responseCode } from '../constant/index'
import { message, messageBox } from '../utils/index'

/**
 * @description: 接口返回通用处理逻辑
 * @param {any} saveRes
 * @param {MsgFetchRes} fetchResObj
 * @return {*}
 */
export const fetchRes = async (
    saveRes: any,
    fetchResObj: {
        isDialog?: boolean | undefined
        errorCB?: Function | undefined
        successCB?: Function | undefined
        catchCB?: Function | undefined
        successTxt?: string | boolean | undefined
        failTxt?: string | boolean | undefined
        fetchKey?: string | undefined
        useHtml?: boolean | undefined
    }
) => {
    try {
        const resObj = await saveRes
        const { code, data, body, description } = resObj || {}
        const { returnCode: returnCodeTpl, description: businessDes } = data ?? body ?? {}
        const returnCode = code || returnCodeTpl
        if (code === 'ERR_BAD_RESPONSE') {
            message({
                type: 'error',
                message: '服务请求失败'
            })
            fetchResObj.errorCB && fetchResObj.errorCB()
        }

        switch (returnCode) {
            case returnCodeRes.SUCCESS:
            case returnCodeRes.CRM_SUCCESS:
            case responseCode.CRM_SUCCESS:
            case returnCodeRes.DS_SUCCESS:
                if (fetchResObj?.successTxt) {
                    message({
                        type: 'success',
                        message: fetchResObj.successTxt || businessDes || '请求成功'
                    })
                }
                fetchResObj.successCB && fetchResObj.successCB(data ?? body)
                break
            case returnCodeRes.PARAM_ERROR:
            case returnCodeRes.CRM_PARAM_ERROR:
            case responseCode.CRM_PARAM_ERROR:
                if (fetchResObj.isDialog) {
                    messageBox(
                        {
                            content: businessDes,
                            dangerouslyUseHTMLString: fetchResObj?.useHtml ?? false
                        },
                        async () => true,
                        () => true
                    )
                } else {
                    message({
                        type: 'error',
                        dangerouslyUseHTMLString: fetchResObj?.useHtml ?? false,
                        message: businessDes ?? description ?? '参数错误'
                    })
                }
                fetchResObj.errorCB && fetchResObj.errorCB()
                break
            case returnCodeRes.SYS_FILED:
            case returnCodeRes.CRM_SYS_FILED:
            case responseCode.CRM_SYS_FILED:
                fetchResObj.errorCB && fetchResObj.errorCB()
                if (fetchResObj?.failTxt) {
                    message({
                        type: 'error',
                        message: fetchResObj?.failTxt ?? businessDes ?? '请求失败'
                    })
                } else {
                    message({
                        type: 'error',
                        message: businessDes ?? fetchResObj?.failTxt ?? '请求失败'
                    })
                }
                break
            default:
                message({
                    type: 'error',
                    message: businessDes || description || '接口请求失败'
                })
                fetchResObj.errorCB && fetchResObj.errorCB()
                break
        }
    } catch (e: any) {
        if (e?.code === 'ERR_NETWORK') {
            message({
                type: 'error',
                message: '请检查网络连接'
            })
        } else {
            message({
                type: 'error',
                message: e?.message ?? '请求失败,请重试!'
            })
        }
        fetchResObj.catchCB && fetchResObj.catchCB()
        // throw new Error(`请求失败${fetchResObj?.fetchKey || ''}`)
    }
}

/**
 * @description: 接口返回利用promise容器
 * @param {any} saveRes
 * @param {any} fetchResObj
 * @return {*}
 */
export const fetchSync = (saveRes: any, fetchResObj: any) => {
    return new Promise((resolve, reject) => {
        saveRes
            .then((resObj: any) => {
                const { code, data, body, description } = resObj || {}
                const { returnCode, description: businessDes } = data || body || {}
                const codeStr = returnCode || code
                if (code === 'ERR_BAD_RESPONSE') {
                    message({
                        type: 'error',
                        message: '接口请求失败'
                    })
                    fetchResObj.errorCB && fetchResObj.errorCB()
                    reject()
                }
                switch (codeStr) {
                    case returnCodeRes.SUCCESS:
                    case returnCodeRes.CRM_SUCCESS:
                    case responseCode.CRM_SUCCESS:
                        if (fetchResObj.successTxt) {
                            message({
                                type: 'success',
                                message: fetchResObj.successTxt ?? businessDes ?? '请求成功'
                            })
                        }
                        fetchResObj.successCB && fetchResObj.successCB(data ?? body)
                        resolve(data)
                        break
                    case returnCodeRes.SYS_FILED:
                    case returnCodeRes.CRM_SYS_FILED:
                    case responseCode.CRM_SYS_FILED:
                        if (fetchResObj.failTxt) {
                            message({
                                type: 'error',
                                message: fetchResObj.failTxt ?? businessDes ?? '请求失败'
                            })
                        } else {
                            message({
                                type: 'error',
                                message: businessDes ?? fetchResObj.failTxt ?? '请求失败'
                            })
                        }
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                    case returnCodeRes.PARAM_ERROR:
                    case returnCodeRes.CRM_PARAM_ERROR:
                    case responseCode.CRM_PARAM_ERROR:
                        message({
                            type: 'error',
                            message: businessDes ?? '参数错误'
                        })
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                    default:
                        message({
                            type: 'error',
                            message: businessDes || description || '接口请求失败'
                        })
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                }
            })
            .catch((e: any) => {
                message({
                    type: 'error',
                    message: e?.message ?? '接口请求失败'
                })
                fetchResObj.catchCB && fetchResObj.catchCB()
                reject()
            })
    })
}

/**
 * @description: 接口promise.all返回通用处理逻辑
 * @param {any} saveRes
 * @param {any} fetchResObj
 * @return {*}
 */
export const fetchAll = (saveRes: any, fetchResObj: any) => {
    if (saveRes?.length > 6) {
        throw new Error(`并发接口不建议大于6个,会导致浏览器性能问题`)
    }
    return Promise.all(saveRes)
        .then((resObj: any) => {
            fetchResObj.successCB && fetchResObj.successCB()
        })
        .catch((e: any) => {
            message({
                type: 'error',
                message: e?.message ?? '请求失败'
            })
            fetchResObj.catchCB && fetchResObj.catchCB()
            throw new Error(`请求失败${fetchResObj?.fetchKey || ''}`)
        })
}
