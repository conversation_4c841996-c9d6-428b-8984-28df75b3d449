import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import type {
    VisitMinutesListReq,
    ExportVisitMinutesReq,
    ISaveFeedbackReq,
    IVisitFeedbackDetailReq,
    ISearchUserReq,
    IUpdateManagerReq
} from './type/apiType'

/**
 * 查询客户拜访纪要列表
 * @param data 请求参数
 * @returns
 */
export function visitMinutesList(data: VisitMinutesListReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/visitMinutes/visitMinutesList',
            method: 'post',
            data,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * 导出客户拜访纪要列表
 * @param data 请求参数
 * @returns
 */
export function exportVisitMinutes(data: ExportVisitMinutesReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/visitMinutes/exportVisitMinutes',
            method: 'post',
            data,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询所有的部门机构列表
 * @param {object} params
 * @return {*}
 */
export const queryAllOrgTree = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryallorgtree',
            method: 'post',
            data: params,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询当前登录用户，根据权限可见的部门机构列表
 * @param {*} params
 * @return {*}
 */
export const queryAuthOrgTree = (params: { module: string; notAuthor: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryauthorgtree3',
            method: 'post',
            data: params,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * 查询客户拜访纪要反馈明细
 * @param params 请求参数
 * @returns
 */
export function getVisitFeedbackDetail(params: IVisitFeedbackDetailReq) {
    return axiosRequest({
        url: '/customer/visitMinutes/visitFeedbackDetail',
        method: 'post',
        data: params
    })
}

/**
 * 保存陪访人/主管反馈
 * @param params 请求参数
 * @returns
 */
export function saveFeedback(params: ISaveFeedbackReq) {
    return axiosRequest({
        url: '/customer/visitMinutes/saveFeedback',
        method: 'post',
        data: params
    })
}
export function searchUser(params: ISearchUserReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/visitMinutes/searchUser',
            method: 'post',
            data: params
        })
    )
}

export function updateManager(params: IUpdateManagerReq) {
    return axiosRequest(
        paramsMerge({
            url: '/customer/visitMinutes/updateManage',
            method: 'post',
            data: params
        })
    )
}
