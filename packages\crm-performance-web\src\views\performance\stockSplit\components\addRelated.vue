<!--
 * @Description: 添加相关投顾
 * @Author: chaohui.wu
 * @Date: 2023-09-12 15:22:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-11-20 17:26:04
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addRelated.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="970px"
        title="添加相关投顾"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        class-name="dialog-table"
        @keyup.enter.stop="handleClose"
    >
        <div class="content-box">
            <aside class="box-left">
                <header class="header-box">
                    <div class="top-head">
                        <RelatedSearch
                            ref="relatedSearchRef"
                            :prefix="true"
                            :search-func="queryConsultant"
                            type="select"
                            @callBack="setSelectList"
                        ></RelatedSearch>
                    </div>
                    <base-button
                        :radius="true"
                        :bold="false"
                        custom-class="small-btn"
                        @click="addRelated(serchSelectList, 'select')"
                        >添加</base-button
                    >
                </header>
                <div class="bottom-content">
                    <aside class="aside-left">
                        <el-tree
                            v-model="curOrgCode"
                            placeholder="请选择"
                            :check-strictly="true"
                            label-format="orgName"
                            value-format="orgCode"
                            node-key="value"
                            :data="organizationList"
                            :default-expanded-keys="treeExpandedKeys"
                            :style="{ width: '200px' }"
                            @nodeClick="getDefaultOption"
                        />
                    </aside>
                    <aside class="aside-right">
                        <base-table
                            ref="multipleTableRef"
                            :columns="columnList"
                            :data="listData"
                            style="width: 100%"
                            :no-select="true"
                            :stripe="true"
                            height="100%"
                            :border="true"
                            :no-index="false"
                            :show-operation="false"
                            operation-width="200"
                            :is-loading="tableLoading"
                            @select="changeSelectTable"
                            @selectionChange="changeSelectTable"
                        >
                        </base-table>
                    </aside>
                </div>
            </aside>
            <aside class="box-right">
                <p class="tips-title">已选择 {{ relatedOptions?.length ?? 0 }} 位</p>
                <ul class="select-list-box">
                    <li
                        v-for="item in relatedOptions"
                        :key="`selscted-list${item?.consCode}`"
                        class="select-item"
                    >
                        <span class="item-label">{{
                            selcetedStr(item?.consName, item?.consCode)
                        }}</span>
                        <span class="item-delate" @click="handleDel(item?.consCode)">
                            <el-icon><Close /></el-icon>
                        </span>
                    </li>
                    <li v-if="relatedOptions.length === 0" class="no-data">
                        点击左侧组织机构选中相关投顾，或者搜索添加。
                    </li>
                </ul>
            </aside>
        </div>
        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="handleClose"
                    >关 闭</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn">确 认</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { Close } from '@element-plus/icons-vue'
    import { ElTable } from 'element-plus'
    import {
        deepClone,
        fetchRes,
        message,
        excludeArr,
        addUnit,
        formatTableValue,
        dateTrans
    } from '@common/utils/index'
    import { useVisible } from '../scripts/hooks/useVisible'
    import { queryConsultant } from '@/api/project/stockSplit/stockSplitList'
    import { CUST_REASON } from '@/constant/index'

    /**©
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            consultList?: any[]
            orgCode?: string
            consCode?: string
            formerOrgCode?: string
            formerConsCode?: string
            organizationList?: any[]
            formerOrganizationList?: any[]
            formerConsultList?: any[]
            transData?: {
                selectedCons: any[]
            }
        }>(),
        {
            visibleCus: true,
            orgCode: '0',
            consCode: '',
            formerOrgCode: '0',
            formerConsCode: '',
            consultList: () => [],
            organizationList: () => [],
            formerOrganizationList: () => [],
            formerConsultList: () => [],
            transData: () => {
                return {
                    selectedCons: []
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack', params: { val: string[]; optionList: object[] }): void
    }>()

    // hooks-useVisible
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    /**
     * @description: 添加搜索框到选中列表
     * @param {*}
     * @return {*}
     */
    const relatedSearchRef = ref<any>([])
    const relatedOptions = ref<any>([]) // 当前选中的列表
    const addRelated = (curList: any[], type = 'search') => {
        const curListTpl = curList.map((item: any) => {
            if (type === 'search') {
                return {
                    consName: item.label,
                    consCode: item.value,
                    ...item
                }
            }
            return item
        })
        const listTpl = [...curListTpl, ...relatedOptions.value]
        // 合并去重
        const mapObj = new Map()
        relatedOptions.value = listTpl.filter((item: any) => {
            if (!mapObj.has(item?.consCode)) {
                mapObj.set(item?.consCode, item)
                return true
            }
        })
        console.log(relatedOptions.value)
        // 如果是搜索点添加则重置搜索选中数据
        if (type === 'select') {
            relatedSearchRef.value?.handleReset()
        }
    }

    /**
     * @description: 数据兼容展示处理
     * @param {*} val1
     * @param {*} val2
     * @return {*}
     */
    const selcetedStr = (val1: string, val2: string) => {
        return val1 ? (val2 ? `${val1}（${val2}）` : val1) : ''
    }

    /**
     * @description: 树形组件默认展开
     * @param {*} computed
     * @return {*}
     */
    const treeExpandedKeys = computed(() => {
        // 获取children的数据
        const { organizationList } = props || {}
        const orgList: any = organizationList[0]?.children?.map((item: any) => item.value) || []
        return orgList
    })

    /**
     * @description: 设置搜索选中数据
     * @param {*} val
     * @return {*}
     */
    const serchSelectList = ref([])
    const setSelectList = (val: any) => {
        const { selectList } = val || {}
        // 搜索框当前选中
        selectList.push(...serchSelectList.value)
        serchSelectList.value = selectList
    }

    /**
     * @description: 当前投顾列表数据
     * @return {*}
     */
    const listData = ref<any>([])

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const columnList = computed(() => {
        return [
            {
                key: 'consCode',
                label: '员工编号',
                minWidth: 100,
                formatter: formatTableValue
            },
            {
                key: 'consName',
                label: '姓名',
                minWidth: 100,
                formatter: formatTableValue
            }
        ]
    })

    /**
     * @description: 通过组织架构获取投顾数据
     * @return {*}
     */
    const showAll = ref(false)
    const curOrgCode = ref('')
    const tableLoading = ref<boolean>(false)
    const getDefaultOption = (data: any) => {
        console.log(data)
        curOrgCode.value = data.value
        showAll.value = false
        tableLoading.value = true
        fetchRes(queryConsultant({ recursive: '1', orgCode: curOrgCode.value, status: '1' }), {
            successCB: (res: any) => {
                const { rows } = res || { rows: [] }
                listData.value = rows
                tableLoading.value = false
            },
            errorCB: () => {
                listData.value = []
                tableLoading.value = false
            },
            catchCB: () => {
                listData.value = []
                tableLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: table表格选中数据
     * @param {*} sel
     * @param {*} row
     * @return {*}
     */
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: any[], row: object): void => {
        tableSelectList.value = unref(sel)
        // 赋值给右侧选中
        addRelated(tableSelectList.value, 'table')
    }

    /**
     * @description: 右侧删除左侧table同步变动
     * @param {*} rows
     * @return {*}
     */
    const multipleTableRef = ref<any>()
    const toggleSelection = (rows?: any[]) => {
        multipleTableRef?.value?.toggleSelection(rows)
    }

    /**
     * @description: 删除对应的投顾
     * @param {*} id
     * @return {*}
     */
    const handleDel = (id: string) => {
        if (id) {
            const delatedList = relatedOptions.value.filter((item: any) => item.consCode !== id)
            // 赋值选中
            relatedOptions.value = delatedList
            // 赋值table选中
            tableSelectList.value = delatedList
            const curList = listData.value.filter((item: any) => item.consCode === id)
            toggleSelection([...curList])
        }
    }

    /**
     * @description: 确定回填
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = () => {
        // 确定后回填数据
        const listTpl = relatedOptions?.value?.map((item: any) => {
            return {
                ...item,
                value: item.consCode,
                label: item.consName
            }
        })
        emit('callBack', { val: listTpl, optionList: relatedOptions.value })
        message({
            type: 'success',
            message: '添加相关投顾成功'
        })
        dialogVisible.value = false
    }

    onMounted(() => {
        curOrgCode.value = props?.orgCode
        // 页面初始默认值展示
        const { selectedCons } = props?.transData || []
        console.log(selectedCons)
        relatedOptions.value = selectedCons.map((item: any) => {
            return {
                ...item,
                consCode: item.value,
                consName: item.label
            }
        })
    })
</script>

<style lang="less" scoped>
    .content-box {
        box-sizing: border-box;
        display: flex;
        height: 60vh;
        padding: 20px;
        overflow: hidden;

        .box-left {
            flex: 2;
            padding-right: 20px;

            .header-box {
                display: flex;

                .top-head {
                    box-sizing: border-box;
                    // display: flex;
                    width: 100%;
                    padding: 0 10px 20px 0;
                }
            }

            .bottom-content {
                display: flex;
                height: calc(60vh - 86px);

                .aside-left {
                    flex: 1;
                    overflow-y: scroll;
                }

                .aside-right {
                    flex: 2;
                }
            }
        }

        .box-right {
            flex: 1;
            padding: 10px 0 0 10px;
            overflow: hidden;
            background-color: @bg_main;
            border-radius: 5px;

            .tips-title {
                font-size: 15px;
                font-weight: bold;
                color: #333333;
            }

            .select-list-box {
                height: calc(60vh - 50px);
                padding: 10px 0 20px;
                overflow-y: scroll;

                &::-webkit-scrollbar {
                    width: 0;
                }

                .select-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 40px;

                    .item-label {
                        font-size: 14px;
                        color: @font_color;
                    }

                    .item-delate {
                        width: 30px;
                        padding-right: 5px;
                        font-size: 15px;
                        cursor: pointer;
                    }
                }

                .no-data {
                    display: flex;
                    align-items: center;
                    height: 100%;
                    font-size: 14px;
                    color: #666666;
                    text-align: center;
                }
            }
        }
    }

    :deep(.el-select__tags) {
        .el-tag__content {
            max-width: 28px;
            overflow: hidden;
        }
    }

    .search-box {
        width: 40px;
        height: 24px;
        padding-left: 15px;
        font-size: 18px;
        color: @font_link_more;
        cursor: pointer;
    }

    .crm_dialog_form {
        width: 100%;
        height: 100%;

        .el-form-item {
            margin-bottom: 10px;

            :deep(.el-input-number) {
                text-align: left;
            }
        }

        :deep(.el-input) {
            .el-input__wrapper {
                padding-left: 10px;

                .el-input__inner {
                    text-align: left;
                }
            }
        }
    }
</style>
