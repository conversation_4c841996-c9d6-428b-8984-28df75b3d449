import { start } from 'repl'

/*
 * @Description: 定义搜索的label列表
 * @Author: chaohui.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 13:07:40
 * @FilePath: /ds-report-web/src/views/modBusiness/pageModule/script/labelData.ts
 *
 */
export const dataList = {
    // 架构名称(北森)
    beisenOrgName: {
        label: '架构名称(北森)',
        placeholder: '请输入'
    },
    beisenOrgId: {
        label: '架构ID(北森)',
        placeholder: '请输入'
    },
    centerOrg: {
        label: '业务中心',
        placeholder: '请选择',
        selectList: [
            {
                value: '1',
                label: 'IC1'
            },
            {
                value: '2',
                label: 'IC2'
            },
            {
                value: '3',
                label: 'HBC'
            },
            {
                value: '5',
                label: 'HKPWM'
            },
            {
                value: '4',
                label: '其他'
            }
        ]
    },
    //起止日期
    matchInterval: {
        label: '起止日期',
        placeholder: ['开始日期', '结束日期']
    },
    startDate: {
        label: '起始日期',
        placeholder: '请选择'
    },
    endDate: {
        label: '结束日期',
        placeholder: '请选择'
    },
    orgCode: {
        label: '所属部门',
        placeholder: '请选择'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
