<!--
 * @Description: 北森crm架构映射
 * @Author: jianji<PERSON>.yang
 * @Date: 2024-10-21 13:31:47
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-10-21 13:31:47
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/stockList.vue
-->
<template>
    <div class="report-list-module">
        <TableWrapCust class-name="crm_wraper" @searchFn="queryList">
            <template #searchArea>
                <table class="table-cust-select">
                    <tr>
                        <!-- 架构名称(北森) -->
                        <td>
                            <LabelItemCust :label="beisenOrgName.label">
                                <crm-input
                                    v-model="queryForm.beisenOrgName"
                                    :placeholder="beisenOrgName.placeholder"
                                    :clearable="true"
                                    :style="{ width: '160px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <!-- 所属部门 -->
                        <td>
                            <LabelItemCust label="所属部门" minwidth="340px">
                                <ReleatedSelect
                                    ref="newlySelect"
                                    v-model="queryForm.orgCons"
                                    :organization-list="organizationList"
                                    :default-org-code="orgCodeDefault"
                                    :have-cons="false"
                                    code-width="130px"
                                    :module="module"
                                    :add-all="true"
                                ></ReleatedSelect>
                            </LabelItemCust>
                        </td>
                        <!-- 起止日期 -->
                        <td>
                            <LabelItemCust :label="matchInterval.label">
                                <date-range
                                    v-model="queryForm.matchInterval"
                                    show-format="YYYY-MM-DD"
                                    :placeholder="matchInterval.placeholder"
                                    style-type="fund"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                </table>
            </template>
            <template #operationBtns>
                <ButtonCust v-show="addShow" size="small" plain :radius="true" @click="handleAdd"
                    >新增</ButtonCust
                >
                <ButtonCust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</ButtonCust
                >
                <ButtonCust size="small" :radius="true" plain @click="resetQueryForm"
                    >清空</ButtonCust
                >
                <ButtonCust
                    v-show="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click.stop="handleExport"
                    >导出</ButtonCust
                >
            </template>
            <template #tableContentMiddle>
                <BaseTableCust
                    :columns="orgConfigListTableColumn"
                    :data="tableList"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :no-index="false"
                    :border="true"
                    operation-width="200"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="modifyShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleModify(scope.row)"
                            >修改</el-button
                        >
                        <el-button size="small" :text="true" link @click="handleDelete(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </BaseTableCust>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[100, 200, 500, 1000, 2000]"
                    @change="handleCurrentChange"
                />
            </template>
        </TableWrapCust>
        <AddOrModConfig
            v-if="addOrModVisible"
            v-model="addOrModVisible"
            :trans-data="addOrModObj"
            :outer-organization-list="organizationList"
            :outer-org-code-default="orgCodeDefault"
            @callBack="queryList"
        ></AddOrModConfig>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download } from '@element-plus/icons-vue'
    import { ElMessageBox } from 'element-plus'
    import AddOrModConfig from './components/addOrModConfig.vue'
    import ReleatedSelect from '@/views/components/common/releatedSelect.vue'
    import { MANAGE_BEISEN_ORG_CONFIG_OPER_PERMISSION } from '@/constant/performanceConst'
    import { fetchRes } from '@common/utils/index'
    import { dataList } from './scripts/labelData'
    import { orgConfigListTableColumn, showTableColumn } from './scripts/tableData'
    import { getMenuPermission } from '@/api/base/menuRoles'
    import {
        beisenOrgConfigQuery,
        beisenOrgConfigDelete,
        beisenOrgConfigExport
    } from '@/api/project/beisen/beisenOrgConfig'

    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, orgCodeDefault } = storeToRefs(useConsOrgListStore)

    const { beisenOrgName, matchInterval } = dataList

    const listLoading = ref<boolean>(false)

    const addShow = ref<boolean>(false)
    const exportShow = ref<boolean>(false)
    const modifyShow = ref<boolean>(false)
    const deleteShow = ref<boolean>(false)

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableList = ref<object[]>([])

    /**
     * @description: 新增/编辑弹框
     * @return {*}
     */
    const addOrModVisible = ref<boolean>(false)

    const addOrModObj = ref({
        title: '',
        id: '',
        type: ''
    })

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        beisenOrgName = ''
        orgCons = {
            orgCode: ''
        }
        matchInterval = {
            startDate: '',
            endDate: ''
        }
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    const queryForm = reactive(new QueryForm())

    /**
     * @description: 重置
     * @param {*} void
     * @return {*}
     */
    const resetQueryForm = (): void => {
        queryForm.beisenOrgName = ''
        queryForm.orgCons.orgCode = ''
        queryForm.matchInterval.startDate = ''
        queryForm.matchInterval.endDate = ''
        newlySelect.value.resetSelect()
    }

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }
    // 管理层投顾组件
    const newlySelect = ref()

    const handleAdd = (): void => {
        addOrModVisible.value = true
        addOrModObj.value = {
            title: '新增',
            id: '',
            type: 'add'
        }
    }

    const handleModify = (val: any): void => {
        const { id } = val || {}
        addOrModVisible.value = true
        addOrModObj.value = {
            title: '修改',
            id: id,
            type: 'add'
        }
    }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        console.log(orgCodeDefault.value)
        const params = {
            orgNameBeisen: queryForm.beisenOrgName,
            startDate: queryForm.matchInterval.startDate,
            endDate: queryForm.matchInterval.endDate,
            orgCode: queryForm.orgCons.orgCode ? queryForm.orgCons.orgCode : orgCodeDefault.value,
            page: pageObj.value.page,
            size: pageObj.value.size
        }
        fetchRes(beisenOrgConfigQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableList.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 基于条件导出
     * @return {*}
     */
    const handleExport = async () => {
        const params = {
            orgNameBeisen: queryForm.beisenOrgName,
            startDate: queryForm.matchInterval.startDate,
            endDate: queryForm.matchInterval.endDate,
            orgCode: queryForm.orgCons.orgCode ? queryForm.orgCons.orgCode : orgCodeDefault.value
        }
        const res: any = await beisenOrgConfigExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleDelete = (val: any): void => {
        const { id } = val || {}
        const params = {
            id: id
        }
        ElMessageBox.confirm('确定删除？', '删除操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            beisenOrgConfigDelete(params).then(() => {
                queryList()
            })
        })
    }

    const module = ref<string>('090303')

    const getMenuAuth = async () => {
        const params = {
            menuCode: module.value
        }
        const res: any = await getMenuPermission(params)
        const { rows } = res.data
        if (rows.length > 0) {
            rows.forEach((item: any) => {
                if (
                    item.operateCode === MANAGE_BEISEN_ORG_CONFIG_OPER_PERMISSION.EXPORT &&
                    item.display === '1'
                ) {
                    exportShow.value = true
                }
                if (
                    item.operateCode === MANAGE_BEISEN_ORG_CONFIG_OPER_PERMISSION.ADD &&
                    item.display === '1'
                ) {
                    addShow.value = true
                }
                if (
                    item.operateCode === MANAGE_BEISEN_ORG_CONFIG_OPER_PERMISSION.MODIFY_SHOW &&
                    item.display === '1'
                ) {
                    modifyShow.value = true
                }
                if (
                    item.operateCode === MANAGE_BEISEN_ORG_CONFIG_OPER_PERMISSION.DELETE_SHOW &&
                    item.display === '1'
                ) {
                    deleteShow.value = true
                }
            })
        }
    }
    /**
     * @description 提示
     */
    onMounted(() => {
        // 获取当前页面权限
        getMenuAuth()
        fetchConsOrgList('', module.value)
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
