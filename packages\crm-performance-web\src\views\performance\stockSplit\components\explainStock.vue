<!--
 * @Description: 风险测评问卷弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 13:39:50
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/explainStock.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        title="说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <b>仅生效、且审核通过、且在计算时间段内 的数据参与数据核算</b><br />
            <div class="middle-content">
                <b> 分成类型：</b><br />
                1、接管-人力：新管理层（无原管理层）按接管规则超额累进制；激活：原架构_人力<br />
                2、接管-单客户：新管理层（无原管理层）按接管规则超额累进制；激活：跨架构_单客户<br />
                3、育成：初始团队成员 裂变时点存量 按一定比例回算给原管理层<br />
                4、异动-协商：新老管理层按一定比例拆分<br />
                5、异动-规则：新管理层按接管超额累进制计算（激活：单客户）；
                原管理层按接管存量存D*（1-新管理层超额累进制比例）<br />
                6、重复划转-协商：新老投顾按一定比例拆分<br />
                7、重复划转-规则：新理财师按超额累进制计算（激活：单客户）；
                原理财师按划转存量存D*（1-新理财师超额累进制比例）<br />
                8、重复划转-新老划断：划转前存量归老投顾；划转后新成交的存量归新投顾 <br />
            </div>
            <b>说明：</b><br />
            1、存量客户拆分，原投顾新老计入量取孰低，公式=min（原计算量，拆分计入量）<br />
            2、司龄月≤12M人员原则上不参与存续D的协商拆分（12M内与12M以上人员存续D算法不同）<br />
            3、协商规则 > 接管规则 <br />
            4、核算规则参照相关文件
        </template>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '../scripts/hooks/useVisible'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    .middle-content {
        padding: 10px 0;
    }
</style>
