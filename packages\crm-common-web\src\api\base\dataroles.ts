/*
 * @Author: xing.zhou
 * @Date: 2021-12-13 14:48:00
 * @LastEditTime: 2023-07-24 10:11:24
 * @LastEditors: chaohui.wu
 * @Description: 数据权限组管理--api
 */

import { axiosRequest } from '@common/utils/index'

export function getList() {
    return axiosRequest({
        url: '/dataRole/getList',
        method: 'get'
    })
}
// 查询数据权限组
export function getTree() {
    return axiosRequest({
        url: '/dataRole/getTree',
        method: 'get'
    })
}
// 新增数据权限组
export function add(data: any) {
    return axiosRequest({
        url: '/dataRole/add',
        method: 'put',
        data
    })
}

// 编辑数据权限组
export function update(data: any) {
    return axiosRequest({
        url: '/dataRole/update',
        method: 'post',
        data
    })
}

// 删除数据权限组
export function remove(data: any) {
    return axiosRequest({
        url: '/dataRole/remove',
        method: 'delete',
        params: data
    })
}

// 删除用户
export function removeUser(data: any) {
    return axiosRequest({
        url: '/dataRole/dataRoleRemovedUser',
        method: 'delete',
        params: data
    })
}

export function getUserList(data: any) {
    return axiosRequest({
        url: '/dataRoleUser/getUserList',
        method: 'get',
        params: data
    })
}

// 数据权限管理 - 选中数据权限组 添加用户
export function dataRoleAddUser(data: any) {
    return axiosRequest({
        url: '/dataRole/dataRoleAddUser',
        method: 'put',
        data
    })
}

export function getAllDataType() {
    return axiosRequest({
        url: '/dataRole/queryAllDataType',
        method: 'get'
    })
}

export function coverAuthTypesToDataRole(data: any) {
    return axiosRequest({
        url: '/dataRole/coverAuthTypesToDataRole',
        method: 'post',
        data
    })
}

export function queryDataTypeListByRoleId(data: any) {
    return axiosRequest({
        url: '/dataRole/queryDataTypeListByRoleId',
        method: 'get',
        params: data
    })
}

export function queryDataRoleUserByRoleId(data: any) {
    return axiosRequest({
        url: '/dataRole/queryDataRoleUser',
        method: 'get',
        params: data
    })
}

export function getDataDimensionList(params: any) {
    return axiosRequest({
        url: '/dataRoleToDataDimension/getDataDimensionList',
        method: 'get',
        params
    })
}

// 保存设置
export function save(data: any) {
    return axiosRequest({
        url: '/dataRoleToDataDimension/save',
        method: 'post',
        data
    })
}

// 获取用户token
export function getUserToken(data: any) {
    return axiosRequest({
        url: '/bs/sys/access/token/get',
        method: 'post',
        data
    })
}
