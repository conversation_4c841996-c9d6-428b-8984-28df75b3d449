<!--
 * @Description: 
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-25 14:22:15
 * @FilePath: /crm-rpo-template/common/components/charts/BarChart.vue
 *  
-->
<template>
    <chart-wrapper :width="width" :height="height" :options="chartOptions" v-bind="$attrs" />
</template>
<script setup lang="ts">
    import { watch, ref, computed } from 'vue'
    import { deepMergeObj } from './scripts/methods'
    import { barChartConfig } from './scripts/chartOptions.js'
    import { dateTrans, flatten } from '../../utils/index'

    interface Props {
        width?: string
        height?: string
        chartData?: any
    }
    const props = withDefaults(defineProps<Props>(), {
        width: '100%',
        height: '300px',
        chartData: () => {
            return {
                seriesData: [
                    {
                        name: '推荐配置',
                        data: [-15, 20, 30, 10, 65],
                        type: 'bar',
                        barCategoryGap: 25,
                        barGap: 0,
                        symbol: 'none',
                        showArea: false,
                        showRizeAndDown: true
                    },
                    {
                        name: '沪深300',
                        data: [10, -12, 22, -55, 54],
                        barCategoryGap: 25,
                        barGap: 0,
                        type: 'bar',
                        symbol: 'none'
                    }
                ],
                xData: ['20140102', '20150102', '20191231', '20200305', '20230403']
            }
        }
    })

    const xData = computed(() => {
        return props.chartData.xData
    })

    // 获取数据最大最小值
    const seriesDataMaxMin = computed(() => {
        const dataList = props.chartData.seriesData.map((item: any, index: number) => {
            const { data } = item
            return [Math.max(...data), Math.min(...data)]
        })

        return {
            max: Math.max(...flatten(dataList)),
            min: Math.min(...flatten(dataList))
        }
    })

    const seriesData = computed(() => {
        return props.chartData.seriesData.map((item: any, index: number) => {
            if (item.showArea) {
                item = {
                    ...item,
                    areaStyle: {
                        origin: 'end',
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(208, 2, 27, 0.2)' // 100% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(208, 2, 27, 0.2)' // 0% 处的颜色
                                }
                            ],
                            global: false // 缺省为 false
                        }
                    }
                }
            }
            if (item.showRizeAndDown) {
                const { data } = item
                return {
                    ...item,
                    data: data.map((store: number, index: number) => {
                        if (Number(store) < 0) {
                            return {
                                value: store,
                                itemStyle: {
                                    color: '#74AA35'
                                }
                            }
                        }
                        return store
                    })
                }
            }
            return item
        })
    })

    const xLen = computed(() => {
        return xData.value.length
    })
    const yLen = computed(() => {
        return seriesData.value[0].data.length
    })

    const chartOptions = ref()
    const getChartOptions = () => {
        chartOptions.value = deepMergeObj(barChartConfig, {
            grid: {
                left: 40,
                right: 10,
                top: 30,
                bottom: 20
            },
            legend: {
                orient: 'horizontal',
                icon: 'roundRect',
                itemWidth: 12,
                itemHeight: 2,
                top: 0,
                left: 0,
                itemGap: 22,
                textStyle: {
                    color: '#9497A7',
                    fontSize: 12,
                    fontFamily: 'Microsoft YaHei'
                }
            },
            xAxis: {
                type: 'category',
                data: xData,
                axisLabel: {
                    formatter: (value: any, index: number) => {
                        // if (index === 0) {
                        //     return `{a|${dateTrans(value, 'yyyy-MM-DD')}}`
                        // }
                        // if (index === xLen.value - 1) {
                        //     return `{b|${dateTrans(value, 'yyyy-MM-DD')}}`
                        // }
                        return `{c|${dateTrans(value, 'yyyy')}}`
                    },
                    rich: {
                        a: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 0, 0, 65]
                        },
                        b: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 70, 0, 0]
                        },
                        c: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999'
                        }
                    }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#e0e0e3',
                        width: 1
                    }
                },
                axisLabel: {
                    formatter: (value: any, index: number) => {
                        if (index === 0) {
                            return `{a|${value}%}`
                        }
                        return `{c|${value}%}`
                    },
                    rich: {
                        a: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 0, 8, 0]
                        },
                        b: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [10, 0, 0, 0]
                        },
                        c: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999'
                        }
                    }
                }
            },
            series: seriesData
        })
        return chartOptions
    }
    watch(
        [() => props.chartData],
        (newVal, oldVal) => {
            if (newVal) {
                getChartOptions()
            } else {
                chartOptions.value = null
            }
        },
        {
            immediate: true
            // deep: true
        }
    )
</script>
<style lang="less" scoped></style>
