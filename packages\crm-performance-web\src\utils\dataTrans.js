/*
 * @Description: 数据格式转换公用方法
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 15:57:06
 * @FilePath: /crm-template/src/utils/dataTrans.js
 * eg:
 * deepMergeObj | 对象深度合并
 * dateFormat | 时间戳转东8区北京时间
 * dateTrans | 北京时间转输出样式
 * sortObjDefault | 前端对象排序
 * | formatNumForTableValue
 * transArrayToParams | 传参数转换
 * treeToArray | 将树转为数组
 * makeElementTree | 转树
 * getString | 获取字符串形式的值
 * digitAdd | 加法，解决浮点计算精度问题
 * digitMinus | 减法
 */
import moment from 'moment'

const isString = val => Object.prototype.toString.call(val) === '[object String]'
const isBoolean = val => Object.prototype.toString.call(val) === '[object Boolean]'
const isNumber = val => Object.prototype.toString.call(val) === '[object Number]'
const isObject = val => Object.prototype.toString.call(val) === '[object Object]'
const isArray = val => Object.prototype.toString.call(val) === '[object Array]'
const isFunction = val => Object.prototype.toString.call(val) === '[object Function]'
const isDate = val => Object.prototype.toString.call(val) === '[object Date]'

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result

    const later = () => {
        // 据上一次触发时间间隔
        const last = +new Date() - timestamp

        // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last)
        } else {
            timeout = null
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
                result = func.apply(context, args)
                if (!timeout) {
                    context = args = null
                }
            }
        }
    }

    return () => {
        context = this
        timestamp = +new Date()
        const callNow = immediate && !timeout
        // 如果延时不存在，重新设定延时
        if (!timeout) {
            timeout = setTimeout(later, wait)
        }
        if (callNow) {
            result = func.apply(context, args)
            context = args = null
        }

        return result
    }
}

/**
 * 获取字符串形式的值
 */
export const getString = val => (val === 0 ? '0' : val ? '' + val : '')

/**
 * 加法，解决浮点计算精度问题
 * @param arg1
 * @param arg2
 */
export const digitAdd = (num1, num2) => {
    let baseNum = 0
    let baseNum1
    let baseNum2
    try {
        baseNum1 = num1.toString().split('.')[1].length
    } catch (e) {
        baseNum1 = 0
    }
    try {
        baseNum2 = num2.toString().split('.')[1].length
    } catch (e) {
        baseNum2 = 0
    }
    baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))
    return (digitMul(num1, baseNum) + digitMul(num2, baseNum)) / baseNum
}

/**
 * 减法
 * @param arg1
 * @param arg2
 */
export const digitMinus = (arg1, arg2) => {
    let r1
    let r2
    let m = 0
    let n = 0
    try {
        r1 = arg1.toString().split('.')[1].length
    } catch (e) {
        r1 = 0
    }
    try {
        r2 = arg2.toString().split('.')[1].length
    } catch (e) {
        r2 = 0
    }
    m = Math.pow(10, Math.max(r1, r2))
    n = r1 >= r2 ? r1 : r2
    return ((arg1 * m - arg2 * m) / m).toFixed(n)
}

/**
 * 除法
 * @param arg1
 * @param arg2
 */
export const digitDiv = (arg1, arg2) => {
    let t1
    let t2
    let r1 = 0
    let r2 = 0
    try {
        t1 = arg1.toString().split('.')[1].length
    } catch (e) {
        t1 = 0
    }
    try {
        t2 = arg2.toString().split('.')[1].length
    } catch (e) {
        t2 = 0
    }

    r1 = Number(arg1.toString().replace('.', ''))
    r2 = Number(arg2.toString().replace('.', ''))
    return digitMul(r1 / r2, Math.pow(10, t2 - t1))
}

/**
 * 乘法
 * @param arg1
 * @param arg2
 */
export const digitMul = (arg1 = '', arg2 = '') => {
    let m = 0
    const s1 = arg1.toString()
    const s2 = arg2.toString()
    try {
        m += s1.split('.')[1].length
    } catch (e) {
        m = 0
    }
    try {
        m += s2.split('.')[1].length
    } catch (e) {
        m = 0
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
}

/**
 * 对象深度合并
 * 如果target(也就是firstObj[key])存在，
 * 且是对象的话再去调用deepMergeObj，
 * 否则就是firstObj[key]里面没这个对象，需要与secondObj[key]合并
 */
export const deepMergeObj = (firstObj, secondObj) => {
    const firstObjCopy = { ...firstObj }
    const secondObjCopy = { ...secondObj }
    for (const key in secondObjCopy) {
        firstObjCopy[key] =
            firstObjCopy[key] && firstObjCopy[key].toString() === '[object Object]'
                ? deepMergeObj(firstObjCopy[key], secondObjCopy[key])
                : (firstObjCopy[key] = secondObjCopy[key])
    }
    return firstObjCopy
}

/**
 * @description: 时间戳转东8区北京时间
 * @param {*} date
 * @param {*} transStr
 * @return {*}
 */
export const dateFormat = (date, transStr = 'yyyy/MM/DD') => {
    return date ? moment.utc(date).local().format(transStr) : ''
}

/**
 * @description: 北京时间转输出样式
 * @param {*} date
 * @param {*} transStr
 * @return {*}
 */
export const dateTrans = (date, transStr = 'yyyy-MM-dd') => {
    return date ? moment(date).format(transStr) : ''
}

/**
 * @description: 当前时间及反推时间
 * @param {*} date
 * @param {*} transStr
 * @param {*} dateType 1｜一年前  2｜3年前  3｜今年以来  4｜自定义
 * @return {*}
 */
export const dateBaseTrans = ({ date, transStr = 'yyyyMMDD', dateType = '4' }) => {
    moment.suppressDeprecationWarnings = true
    switch (dateType) {
        case '1':
            return date ? moment(date).subtract(1, 'year').format(transStr) : ''
        case '2':
            return date ? moment(date).subtract(3, 'year').format(transStr) : ''
        case '3':
            return date ? moment(date).startOf('year').format(transStr) : ''
        case '4':
        default:
            return '2014-01-01'
    }
}

/**
 * @description: 前端对象排序
 * @param {*} sortKey
 * @param {*} sortType
 * @return {*}
 */
export const sortObjDefault = (sortKey, sortType = 1) => {
    return (a, b) => {
        const aValue = Number(a[sortKey])
        const bValue = Number(b[sortKey])
        if (aValue < bValue) {
            return -sortType
        }
        if (aValue > bValue) {
            return sortType
        }
        return 0
    }
}

/**
 * 添加红涨绿跌类名
 */
export const addRedGreenClass = val => {
    if (!val || val === '0' || val === '0.00') {
        return ''
    }
    return val > 0 ? 'red2' : val < 0 ? 'green2' : ''
}

/**
 * @description: 传参数转换
 * @param {*} list `11,11,11`
 * @return {*}
 */
export const transArrayToParams = list => {
    for (const item in list) {
        if (list[item] && Array.isArray(list[item])) {
            list[item] = list[item].join(',')
        }
    }
    return list
}

/**
 * @description: 将树转为数组
 * @param {*} treeData
 * @param {*} field
 * @return {*}
 */
export const treeToArray = (treeData, field = 'children') => {
    let result = []
    for (const key in treeData) {
        const treeObj = treeData[key]
        const clone = JSON.parse(JSON.stringify(treeObj))
        delete clone[field]
        result.push(clone)
        if (treeObj[field]) {
            const tmp = treeToArray(treeObj[field], field)
            result = result.concat(tmp)
        }
    }
    return result
}

/**
 * @description: 转树
 * @param {*} params
 * @return {*}
 */
export const makeElementTree = (
    params = {
        pid: '0',
        list: [],
        pidFiled: 'pValue',
        labelFiled: 'label',
        valueFiled: 'value'
    }
) => {
    const { pid, list, pidFiled, labelFiled, valueFiled } = params
    const makeTree = (pid, arr) => {
        const res = []
        arr.forEach(i => {
            if (i[pidFiled] === pid) {
                const children = makeTree(i[valueFiled], list)
                const child = true
                const obj = {
                    label: i[labelFiled],
                    value: i[valueFiled],
                    pid: i[pidFiled]
                }
                if (children.length) {
                    obj.children = children
                }
                res.push(obj)
            }
        })
        return res
    }
    return makeTree(pid, list)
}

export function arrayToTree(data, valueFormat = 'orgCode', labelFormat = 'orgName') {
    const _data = data.map(item => {
        return {
            value: item[valueFormat],
            label: item[labelFormat],
            pid: item.parentOrgCode,
            status: item.status
        }
    })

    const result = []
    const map = {}
    _data.forEach(item => {
        map[item.value] = item
    })
    _data.forEach(item => {
        const parent = map[item.pid]
        if (parent) {
            ;(parent.children || (parent.children = [])).push(item)
        } else {
            result.push(item)
        }
    })
    return result
}
