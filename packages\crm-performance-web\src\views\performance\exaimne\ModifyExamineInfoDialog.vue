<template>
    <div class="modify-examine-info-page">
        <div class="page-body">
            <el-form ref="ruleFormRef" :model="formList" :rules="rules" status-icon>
                <el-row>
                    <el-col>
                        <el-form-item prop="sourceType" style="margin-bottom: 10px">
                            <el-checkbox
                                v-model="sourceTypeVisible"
                                label="来源类型"
                                style="width: 140px; margin-right: 10px"
                            />
                            <crm-select
                                v-if="sourceTypeVisible"
                                v-model="formList.sourceType"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                placeholder="请选择来源类型"
                                :option-list="sourceTypeOptions"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item prop="custConversionCoeff" style="margin-bottom: 10px">
                            <el-checkbox
                                v-model="custConversionCoeffVisible"
                                label="客户折算系数"
                                style="width: 140px; margin-right: 10px"
                            />
                            <el-input-number
                                v-if="custConversionCoeffVisible"
                                v-model="formList.custConversionCoeff"
                                :precision="4"
                                :min="0"
                                :controls="false"
                                size="small"
                                style="width: 150px"
                                placeholder="请输入客户折算系数"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item prop="manageCoeffSubtotal" style="margin-bottom: 10px">
                            <el-checkbox
                                v-model="manageCoeffSubtotalVisible"
                                label="管理系数-分总"
                                style="width: 140px; margin-right: 10px"
                            />
                            <el-input-number
                                v-if="manageCoeffSubtotalVisible"
                                v-model="formList.manageCoeffSubtotal"
                                :precision="4"
                                :controls="false"
                                :min="0"
                                size="small"
                                style="width: 150px"
                                placeholder="请输入管理系数-分总"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="manageCoeffRegionalsubtotal"
                            style="margin-bottom: 10px"
                        >
                            <el-checkbox
                                v-model="manageCoeffRegionalsubtotalVisible"
                                label="管理系数-区副"
                                style="width: 140px; margin-right: 10px"
                            />
                            <el-input-number
                                v-if="manageCoeffRegionalsubtotalVisible"
                                v-model="formList.manageCoeffRegionalsubtotal"
                                :precision="4"
                                :controls="false"
                                :min="0"
                                size="small"
                                style="width: 150px"
                                placeholder="请输入管理系数-区副"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item prop="manageCoeffRegionaltotal" style="margin-bottom: 20px">
                            <el-checkbox
                                v-model="manageCoeffRegionaltotalVisible"
                                label="管理系数-区总"
                                style="width: 140px; margin-right: 10px"
                            />
                            <el-input-number
                                v-if="manageCoeffRegionaltotalVisible"
                                v-model="formList.manageCoeffRegionaltotal"
                                :precision="4"
                                :controls="false"
                                :min="0"
                                size="small"
                                style="width: 150px"
                                placeholder="请输入管理系数-区总"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 45%">
                            <el-button type="primary" @click="handleConfirm">确认</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button @click="handleCancel">取消</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref, watch, computed, onMounted } from 'vue'
    import { ElMessage } from 'element-plus'
    import { mergeConsPerformanceCoeff } from '@/api/project/exanine/consPerformanceCoeff'
    import { queryHbConstant } from '@/api/project/common/common'
    import { CUST_SOURCE_TYPE } from '@/constant/typeCodeConstant'

    interface BaseInfo {
        custType: string
        assignTime: string
        consCustNo: string
        hboneNo: string
        consCode: string
        modifier: string
        [key: string]: any
    }

    const emit = defineEmits(['update:visible', 'saved', 'close'])

    // 通知父页面关闭 iframe
    const notifyParentClose = () => {
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ type: 'closeDialog' }, '*')
        }
    }

    // 通知父页面关闭 iframe
    const notifyParentSaveSuccess = () => {
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ type: 'saveSuccess' }, '*')
        }
    }

    // 来源类型选项
    const sourceTypeOptions = ref<{ label: string; key: string }[]>([])

    // 获取来源类型选项
    const getSourceTypeOptions = async () => {
        try {
            const res: any = await queryHbConstant({ typeCode: CUST_SOURCE_TYPE })
            if (res.data?.constantList) {
                sourceTypeOptions.value = res.data.constantList.map((item: any) => ({
                    label: item.constName,
                    key: item.constCode
                }))
            }
        } catch (error) {
            console.error('获取来源类型选项失败:', error)
        }
    }

    // 组件挂载时获取来源类型选项
    onMounted(() => {
        getSourceTypeOptions()
    })

    // 表单数据
    const formList = ref<{
        sourceType: string
        custConversionCoeff: number | undefined
        manageCoeffSubtotal: number | undefined
        manageCoeffRegionalsubtotal: number | undefined
        manageCoeffRegionaltotal: number | undefined
        [key: string]: any
    }>({
        sourceType: '',
        custConversionCoeff: undefined,
        manageCoeffSubtotal: undefined,
        manageCoeffRegionalsubtotal: undefined,
        manageCoeffRegionaltotal: undefined
    })

    // 动态校验规则
    const rules = computed(() => {
        const dynamicRules: any = {}

        if (sourceTypeVisible.value) {
            dynamicRules.sourceType = [
                { required: true, message: '请选择来源类型', trigger: 'change' }
            ]
        }
        if (custConversionCoeffVisible.value) {
            dynamicRules.custConversionCoeff = [
                { required: true, message: '请输入客户折算系数', trigger: 'blur' }
            ]
        }
        if (manageCoeffSubtotalVisible.value) {
            dynamicRules.manageCoeffSubtotal = [
                { required: true, message: '请输入管理系数-分总', trigger: 'blur' }
            ]
        }
        if (manageCoeffRegionalsubtotalVisible.value) {
            dynamicRules.manageCoeffRegionalsubtotal = [
                { required: true, message: '请输入管理系数-区副', trigger: 'blur' }
            ]
        }
        if (manageCoeffRegionaltotalVisible.value) {
            dynamicRules.manageCoeffRegionaltotal = [
                { required: true, message: '请输入管理系数-区总', trigger: 'blur' }
            ]
        }

        return dynamicRules
    })

    // 控制各个字段的显示状态
    const sourceTypeVisible = ref(false)
    const custConversionCoeffVisible = ref(false)
    const manageCoeffSubtotalVisible = ref(false)
    const manageCoeffRegionalsubtotalVisible = ref(false)
    const manageCoeffRegionaltotalVisible = ref(false)

    const ruleFormRef = ref() // 用于表单校验和清除提示

    const props = withDefaults(
        defineProps<{
            visible?: boolean
        }>(),
        {
            visible: true // 默认展示
        }
    )

    // 从 URL 参数获取客户号 - 多种方式尝试
    const getCustNoFromUrl = () => {
        // 方式1: 从当前 URL 获取
        const urlParams = new URLSearchParams(window.location.search)
        let custNo = urlParams.get('consCustNo')

        // 方式2: 从 hash 路由获取
        if (!custNo && window.location.hash) {
            const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '')
            custNo = hashParams.get('consCustNo')
        }

        // 方式3: 从 URL 路径中解析
        if (!custNo) {
            const pathMatch = window.location.pathname.match(/consCustNo=([^&]+)/)
            if (pathMatch) {
                custNo = pathMatch[1]
            }
        }

        console.log('获取到的客户号:', custNo)
        return custNo || ''
    }

    const custNo = ref(getCustNoFromUrl())

    // 判断是否为独立页面模式
    const isStandalone = computed(() => {
        return window.location.pathname.includes('/examine/ModifyExamineInfoDialog')
    })

    // 使用 computed 来处理 v-model，避免直接修改 prop
    const dialogVisible = computed({
        get: () => props.visible ?? true, // 默认展示
        set: (value: boolean) => {
            emit('update:visible', value)
        }
    })

    watch(
        () => props.visible,
        (val: boolean) => {
            if (!val) {
                // 重置表单和显示状态
                formList.value = {
                    sourceType: '',
                    custConversionCoeff: undefined,
                    manageCoeffSubtotal: undefined,
                    manageCoeffRegionalsubtotal: undefined,
                    manageCoeffRegionaltotal: undefined
                }
                sourceTypeVisible.value = false
                custConversionCoeffVisible.value = false
                manageCoeffSubtotalVisible.value = false
                manageCoeffRegionalsubtotalVisible.value = false
                manageCoeffRegionaltotalVisible.value = false
            }
        }
    )

    function handleCancel() {
        emit('update:visible', false)
        notifyParentClose()
    }

    async function handleConfirm() {
        // 检查是否有选中的字段
        const selectedFields = []
        if (sourceTypeVisible.value) {
            selectedFields.push('sourceType')
        }
        if (custConversionCoeffVisible.value) {
            selectedFields.push('custConversionCoeff')
        }
        if (manageCoeffSubtotalVisible.value) {
            selectedFields.push('manageCoeffSubtotal')
        }
        if (manageCoeffRegionalsubtotalVisible.value) {
            selectedFields.push('manageCoeffRegionalsubtotal')
        }
        if (manageCoeffRegionaltotalVisible.value) {
            selectedFields.push('manageCoeffRegionaltotal')
        }

        if (selectedFields.length === 0) {
            ElMessage.warning('请选择要修改的字段')
            return
        }

        // 表单验证
        try {
            await ruleFormRef.value?.validate()
        } catch (error) {
            ElMessage.error('请完善表单信息')
            return
        }

        const data: any = {
            consCustNo: custNo.value // 添加客户号
        }
        selectedFields.forEach((field: string) => {
            data[field] = formList.value[field]
        })

        try {
            const res: any = await mergeConsPerformanceCoeff(data)
            if (res.code === 'C010000') {
                ElMessage.success('保存成功')
                debugger
                emit('saved')
                emit('update:visible', false)
                notifyParentSaveSuccess()
            } else {
                ElMessage.error(res.description || '保存失败')
            }
        } catch (e) {
            ElMessage.error('网络异常')
        }
    }

    /**
     * @desc 勾选取消时清除对应字段的校验提示
     * <AUTHOR>
     * @date 2025-07-25 15:33:19
     */
    watch(sourceTypeVisible, val => {
        if (!val) {
            ruleFormRef.value?.clearValidate('sourceType')
        }
    })
    watch(custConversionCoeffVisible, val => {
        if (!val) {
            ruleFormRef.value?.clearValidate('custConversionCoeff')
        }
    })
    watch(manageCoeffSubtotalVisible, val => {
        if (!val) {
            ruleFormRef.value?.clearValidate('manageCoeffSubtotal')
        }
    })
    watch(manageCoeffRegionalsubtotalVisible, val => {
        if (!val) {
            ruleFormRef.value?.clearValidate('manageCoeffRegionalsubtotal')
        }
    })
    watch(manageCoeffRegionaltotalVisible, val => {
        if (!val) {
            ruleFormRef.value?.clearValidate('manageCoeffRegionaltotal')
        }
    })
</script>

<style scoped>
    .modify-examine-info-page {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 24px 32px 32px;
        background: none;
    }

    .page-header {
        margin-bottom: 16px;
    }

    .page-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: bold;
        color: #303133;
    }

    .page-body {
        padding: 0;
        background: none;
    }

    .cust-info {
        padding: 10px 0;
        margin-bottom: 15px;
        background: none;
        border-left: 3px solid #409eff;
        border-radius: 4px;
    }

    .cust-label {
        font-weight: bold;
        color: #606266;
    }

    .cust-value {
        margin-left: 5px;
        font-weight: bold;
        color: #409eff;
    }

    .el-form-item {
        width: auto;
        min-width: 0;
    }

    .el-button {
        min-width: 80px;
    }
</style>
<!--
<AUTHOR>
@date 2025-07-21 19:44:56
@desc 页面改为普通页面风格，移除所有弹窗、居中、白色卡片等包裹和样式，内容直接铺满页面，和普通业务页面一致
-->
