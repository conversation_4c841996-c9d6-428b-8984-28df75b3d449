/*
 * @Author: ch<PERSON><PERSON>.<EMAIL>
 * @Date: 2023-02-22 19:50:51
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-02-02 15:29:42
 * @FilePath: /crm-web/packages/crm-performance-web/public/msEnv.js
 * @Description: 宙斯配置项
 */
var _msEnvFlag = '2' // 1 线上环境  2 测试环境  3 mock环境  4 测试 doc 预览
var _msApiPrefix = window.origin
var _msApiPrefix_oldAsset = window.origin + '/oldAsset'

if (_msEnvFlag === '2') {
    //_msApiPrefix = window.origin
    _msApiPrefix = 'http://localhost:18019/crm-cgi/inner'
} else if (_msEnvFlag === '3') {
    // _msApiPrefix = 'https://mock.apifox.cn/m1/2175602-0-default/'
    // _msApiPrefix = 'http://127.0.0.1:4523/m1/2810603-0-default'
    _msApiPrefix = 'https://mock.apifox.cn/m1/2810603-0-default'
    _msApiPrefix_oldAsset = 'https://mock.apifox.cn/m1/2175602-0-default/'
}
