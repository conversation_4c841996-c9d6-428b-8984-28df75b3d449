/*
 * @Description: 绩效管理data
 * @Author: chaohui.wu
 * @Date: 2023-09-07 13:57:57
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 11:14:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/stockListData.ts
 *
 */

import { arrayToTree } from '@/utils/dataTrans'
import { fetchSync } from '@common/utils/resBase'
import { queryAllOrgTree, queryAuthOrgTree } from '@/api/project/common/common'

export const useConsOrgListData = defineStore('stockListData', {
    state: () => {
        return {
            operateList: [],
            organizationList: [],
            consultList: [],
            orgCodeDefault: '',
            consCodeDefault: ''
        }
    },
    getters: {},
    actions: {
        setCommonData({ organizationList, consultList }: any) {
            if (organizationList) {
                this.organizationList = organizationList
            }
            if (consultList) {
                this.consultList = consultList
            }
        },
        fetchConsOrgList(type = '', menuCode = '') {
            if (type === 'all') {
                fetchSync(queryAllOrgTree({}), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        const consListTpl =
                            consultList?.length > 0
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        const b = arrayToTree(organizationList)
                        console.log('%c Line:93 🍌 b', 'color:#ed9ec7', b)
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            } else {
                const params = {
                    module: menuCode
                }
                fetchSync(queryAuthOrgTree(params), {
                    successCB: (res: any) => {
                        const { organizationList, consultList } = res || {}
                        const b = arrayToTree(organizationList)
                        console.log('%c Line:58 🍩 b', 'color:#6ec1c2', b)
                        // 获取组织架构数据
                        const consListTpl =
                            consultList?.length > 1
                                ? [{ consCode: '', consName: '全部', status: '0' }, ...consultList]
                                : consultList
                        const pid = organizationList[0]?.parentOrgCode || ''
                        // 将数据存储到pinia
                        this.setCommonData({
                            // organizationList: makeElementTree({
                            //     pid,
                            //     list: organizationList,
                            //     pidFiled: 'parentOrgCode',
                            //     labelFiled: 'orgName',
                            //     valueFiled: 'orgCode'
                            // }),
                            organizationList: b,
                            consultList: consListTpl
                        })
                        this.orgCodeDefault = organizationList[0].orgCode ?? ''
                        this.consCodeDefault = consListTpl[0].consCode ?? ''
                    },
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            }
        }
    }
})
