/*
 * @Description: reportEditTop 返回参数类型
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-25 23:23:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:39:29
 * @FilePath: /crm-template/src/api/project/reportList/type/apiResType.d.ts
 *
 */

export {}
import { CGIRes, TableListData } from '@/types/index'
declare module './apiResType' {
    /**
     * @description: 投顾名下客户信息
     * @return {*} 接口返回数据
     */
    type ListResponse = CGIRes<TableListData<object[]>>
    // interface ListData {
    //     /**
    //      * 当前第几页
    //      */
    //     page: number;
    //     rows: object[]
    //     /**
    //      * 返回数据量
    //      */
    //     size: number;
    //     /**
    //      * 总数
    //      */
    //     total: number;
    // }
    interface ConscustInfoVo {
        /**
         * 客户号
         */
        conscustno: string
        /**
         * 客户姓名
         */
        custName: string
        /**
         * 一账通号码
         */
        hboneNo: string
        /**
         * 手机掩码
         */
        mobileMask: string
    }
    /**
     * 资产配置报告，客户资产配置报告VO
     */
    type CmAssectFixedPositionVo = {
        /**
         * 报告ID
         */
        assetId: string
        /**
         * 投顾客户号
         */
        conscustno: string
        /**
         * 是否可下载
         */
        coun: string
        /**
         * 创建人
         */
        creator: string
        /**
         * 创建时间yyyy-MM-dd HH:mm:ss
         */
        creDt: string
        creDtTime?: number
        /**
         * 文件名称
         */
        fileName: string
    }
    export { ConscustInfoVo, ListResponse, CmAssectFixedPositionVo }
}
