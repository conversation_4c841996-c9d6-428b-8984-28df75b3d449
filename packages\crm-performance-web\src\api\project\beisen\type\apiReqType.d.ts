export {}
declare module './apiReqType' {
    interface QueryBeisenOrgConfigReq {
        /**
         * 架构名称(北森)
         */
        orgNameBeisen?: string
        /**
         * 机构编码
         */
        orgCode?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
        /**
         * 页号
         */
        pageNo?: number
        /**
         * 每页大小
         */
        pageSize?: number
    }
    interface InsertBeisenOrgConfigReq {
        /**
         * 架构名称(北森)
         */
        orgNameBeisen?: string
        /**
         * 架构Id(北森)
         */
        orgIdBeisen?: string
        /**
         * 机构编码
         */
        orgCode?: string
        /**
         * 业务中心
         */
        centerOrg?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
    }
    interface UpdateBeisenOrgConfigReq {
        /**
         * 主键
         */
        id?: string
        /**
         * 架构名称(北森)
         */
        orgNameBeisen?: string
        /**
         * 架构Id(北森)
         */
        orgIdBeisen?: string
        /**
         * 机构编码
         */
        orgCode?: string
        /**
         * 业务中心
         */
        centerOrg?: string
        /**
         * 开始日期 yyyyMMdd
         */
        startDate?: string
        /**
         * 结束日期 yyyyMMdd
         */
        endDate?: string
    }
    interface BeisenOrgConfigIdReq {
        /**
         * 主键
         */
        id?: string
    }

    export {
        QueryBeisenOrgConfigReq,
        InsertBeisenOrgConfigReq,
        UpdateBeisenOrgConfigReq,
        BeisenOrgConfigIdReq
    }
}
