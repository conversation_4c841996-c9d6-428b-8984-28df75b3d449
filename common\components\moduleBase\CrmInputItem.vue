<!--
* 带label的输入框
* @eg:
    <input-item
        v-model="data.cpmc"
        label="产品名称"
        placeholder="输入代码或名称模糊搜索"
    />
-->
<template>
    <div class="crm-input-item">
        <div class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-input v-bind="$attrs" />
        </div>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmInputItem',
        props: {
            label: {
                type: String,
                default: ''
            }
        }
    })
</script>

<style lang="less">
    .crm-input-item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        .label {
            min-width: 72px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
