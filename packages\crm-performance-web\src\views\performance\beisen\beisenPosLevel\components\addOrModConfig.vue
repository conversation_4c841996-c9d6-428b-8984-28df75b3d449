<!--
 * @Description: 添加用户信息
 * @Author: chaohui.wu
 * @Date: 2023-09-12 15:22:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-02-18 16:29:35
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addCustom.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="600px"
        height="600px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 编辑的弹框 -->
            <el-form ref="ruleFormRef" :model="formList" :rules="rules" status-icon>
                <!-- 职级编码(北森) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="positionsLevelBeisen"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="positionsLevelBeisen.label"
                                labelwidth="200px"
                                :required="true"
                            >
                                <crm-input
                                    v-model="formList.positionsLevelBeisen"
                                    :placeholder="positionsLevelBeisen.placeholder"
                                    :clearable="true"
                                    :disabled="props.transData.id !== ''"
                                    :style="{ width: '200px' }"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 职级名称(北森) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="positionsLevelNameBeisen"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="positionsLevelNameBeisen.label"
                                labelwidth="200px"
                                :required="true"
                            >
                                <crm-input
                                    v-model="formList.positionsLevelNameBeisen"
                                    :placeholder="positionsLevelNameBeisen.placeholder"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 层级(CRM) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="userLevelCrm"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="userLevelCrm.label"
                                labelwidth="200px"
                                :required="true"
                            >
                                <crm-select
                                    v-model="formList.userLevelCrm"
                                    placeholder="请选择层级"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="userLevelCrmList"
                                    :style="{ width: '180px' }"
                                    @change="handleSelectPositionsLevelCrm"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 职级(CRM) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="positionsLevelCrm"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="positionsLevelCrm.label"
                                labelwidth="200px"
                                :required="true"
                            >
                                <crm-select
                                    v-model="formList.positionsLevelCrm"
                                    placeholder="请选择职级"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="positionsLevelCrmList"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 副职(CRM) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="positionsLevelCrm"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust :label="subPositionsLevelCrm.label" labelwidth="200px">
                                <crm-select
                                    v-model="formList.subPositionsLevelCrm"
                                    placeholder="请选择副职"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="subPositionsLevelList"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 起始日期 -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="startDate"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="startDate.label"
                                labelwidth="200px"
                                :required="true"
                            >
                                <el-date-picker
                                    v-model="formList.startDate"
                                    :placeholder="startDate.placeholder"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    show-format="YYYY-MM-DD"
                                    value-format="YYYYMMDD"
                                    class-name="w180"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 结束日期 -->
                <el-row>
                    <el-col>
                        <el-form-item prop="endDate" style="margin-top: 15px; margin-bottom: 15px">
                            <LabelItemCust :label="endDate.label" labelwidth="200px">
                                <el-date-picker
                                    v-model="formList.endDate"
                                    :placeholder="endDate.placeholder"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    show-format="YYYY-MM-DD"
                                    value-format="YYYYMMDD"
                                    class-name="w180"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="saveFn">保 存</crm-button>
                <crm-button size="small" :radius="true" @click="handleClose">关 闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { FormInstance, FormRules, ElMessage } from 'element-plus'
    import { useVisible } from '@/views/common/scripts/useVisible'

    import { dataList } from './../scripts/labelData'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import {
        beisenPosLevelConfigInsert,
        beisenPosLevelConfigUpdate,
        beisenPosLevelConfigDetail
    } from '@/api/project/beisen/beisenPosLevel/beisenPosLevelConfig'
    import {
        downloadFile,
        makeElementTree,
        fetchRes,
        message,
        excludeArr
    } from '@common/utils/index'

    const {
        positionsLevelBeisen,
        positionsLevelNameBeisen,
        userLevelCrm,
        positionsLevelCrm,
        subPositionsLevelCrm,
        startDate,
        endDate
    } = dataList

    const module = ref<string>('090302')
    const listLoading = ref<boolean>(false)
    const userLevelCrmList = ref<any>([])
    const positionsLevelCrmList = ref<any>([])
    const subPositionsLevelList = ref<any>([])
    const positionsLevelCrmMap = ref<any>([])
    const positionsLevelCrmData = ref<any>([])

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            outerUserLevelCrmList?: any[]
            outerPositionsLevelCrmList?: any[]
            outerSubPositionsLevelList?: any[]
            outerPositionsLevelCrmMap?: any[]
            outPositionsLevelCrmData?: any[]
            transData?: {
                title: string
                type: string
                id: string
            }
        }>(),
        {
            visibleCus: false,
            outerUserLevelCrmList: () => [],
            outerPositionsLevelCrmList: () => [],
            outerSubPositionsLevelList: () => [],
            outerPositionsLevelCrmMap: () => [],
            outPositionsLevelCrmData: () => [],
            transData: () => {
                return {
                    title: '添加客户',
                    type: 'add',
                    id: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'custCallBack', params: { val: string[]; optionList: object[] }): void
    }>()

    // hooks-useVisible
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    class FormList {
        positionsLevelBeisen = ''
        positionsLevelNameBeisen = ''
        userLevelCrm = ''
        positionsLevelCrm = ''
        subPositionsLevelCrm = ''
        matchInterval = ''
        startDate = ''
        endDate = ''
    }

    const formList = reactive<any>(new FormList())
    const rules = reactive<FormRules<FormList>>({
        positionsLevelBeisen: [
            { required: true, message: '请输入职级编码(北森)！', trigger: 'blur' }
        ],
        positionsLevelNameBeisen: [
            { required: true, message: '请输入职级名称(北森)！', trigger: 'blur' }
        ],
        userLevelCrm: [{ required: true, message: '请选择层级(CRM)！', trigger: 'blur' }],
        positionsLevelCrm: [{ required: true, message: '请选择职级(CRM)！', trigger: 'blur' }],
        subPositionsLevelCrm: [{ required: true, message: '请选择副职(CRM)！', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择起始日期！', trigger: 'blur' }]
    })

    const saveFn = () => {
        console.log('saveFn')
        const params = {
            id: props.transData.id,
            positionsLevelBeisen: formList.positionsLevelBeisen,
            positionsLevelNameBeisen: formList.positionsLevelNameBeisen,
            userLevelCrm: formList.userLevelCrm,
            positionsLevelCrm: formList.positionsLevelCrm,
            subPositionsLevelCrm: formList.subPositionsLevelCrm,
            startDate: formList.startDate,
            endDate: formList.endDate
        }
        if (props.transData.id) {
            fetchRes(beisenPosLevelConfigUpdate(params), {
                successCB: (resObj: any) => {
                    listLoading.value = false
                    const { verifyMsg } = resObj
                    if (verifyMsg) {
                        message({
                            type: 'error',
                            message: verifyMsg
                        })
                    } else {
                        message({
                            type: 'success',
                            message: '成功'
                        })
                        handleClose()
                    }
                },
                errorCB: () => {
                    listLoading.value = false
                },
                catchCB: () => {
                    listLoading.value = false
                },
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            })
        } else {
            if (params.endDate.length > 0 && params.startDate - params.endDate > 0) {
                ElMessage({
                    message: '结束日期不能小于起始日期！',
                    type: 'warning',
                    duration: 2000
                })
                return
            }
            fetchRes(beisenPosLevelConfigInsert(params), {
                successCB: (resObj: any) => {
                    listLoading.value = false
                    const { verifyMsg } = resObj
                    if (verifyMsg) {
                        message({
                            type: 'error',
                            message: verifyMsg
                        })
                    } else {
                        message({
                            type: 'success',
                            message: '成功'
                        })
                        handleClose()
                    }
                },
                errorCB: () => {
                    listLoading.value = false
                },
                catchCB: () => {
                    listLoading.value = false
                },
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            })
        }
    }

    const initFormList = () => {
        debugger
        const params = {
            id: props.transData.id
        }
        listLoading.value = true
        fetchRes(beisenPosLevelConfigDetail(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const {
                    positionsLevelBeisen,
                    positionsLevelNameBeisen,
                    userLevelCrm,
                    positionsLevelCrm,
                    subPositionsLevelCrm,
                    startDate,
                    endDate
                } = resObj || {}
                formList.positionsLevelBeisen = positionsLevelBeisen
                formList.positionsLevelNameBeisen = positionsLevelNameBeisen
                formList.userLevelCrm = userLevelCrm
                formList.positionsLevelCrm = positionsLevelCrm
                formList.subPositionsLevelCrm = subPositionsLevelCrm
                formList.startDate = startDate
                formList.endDate = endDate
                const ulc = formList.userLevelCrm
                const map1 = positionsLevelCrmMap.value
                for (const key in map1) {
                    if (ulc === key) {
                        const value1 = map1[key]
                        positionsLevelCrmList.value = value1
                        return
                    }
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        userLevelCrmList.value = props?.outerUserLevelCrmList
        subPositionsLevelList.value = props?.outerSubPositionsLevelList
        positionsLevelCrmMap.value = props?.outerPositionsLevelCrmMap
        positionsLevelCrmData.value = props?.outPositionsLevelCrmData
        debugger
        if (props.transData.id) {
            initFormList()
        }
    })

    function handleSelectPositionsLevelCrm(value: any): void {
        debugger
        formList.positionsLevelCrm = ''
        const ulc = formList.userLevelCrm
        console.log('formList.userLevelCrm', ulc)
        const map1 = positionsLevelCrmMap.value
        for (const key in map1) {
            if (ulc === key) {
                const value1 = map1[key]
                positionsLevelCrmList.value = value1
                return
            }
        }
    }
</script>

<style lang="less" scoped></style>
