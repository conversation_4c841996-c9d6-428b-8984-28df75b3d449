<!--
 * @Description: 导航树形控件封装
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-28 20:05:20
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-19 15:49:17
 * @FilePath: /crm-asset-web/src/components/moduleBase/MenuDrop.vue
 *  
-->

<template>
    <el-dropdown
        :size="fontSize"
        popper-class="menu-btn-popper"
        :teleported="false"
        @command="handleCommand"
    >
        <el-button class="menu-list-btn" plain :size="fontSize" bg>
            <svg-icons name="menu-list" class="mr8"></svg-icons>
            <span class="menu-txt">
                {{ btnText }}
            </span>
            <el-icon>
                <CaretBottom />
            </el-icon>
        </el-button>

        <template #dropdown>
            <p class="drop-notice">*点击模块标题，快速定位到对应位置</p>
            <el-dropdown-menu>
                <slot />
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script>
    import { Download, ArrowDown, Fold, CaretBottom } from '@element-plus/icons-vue'
    export default defineComponent({
        name: 'MenuDrop',
        components: { CaretBottom },
        props: {
            dropData: {
                type: Array,
                default: () => {
                    return []
                }
            },
            btnText: {
                type: String,
                default: '报告列表'
            },
            fontSize: {
                type: [String],
                default: 'default'
            }
        },
        setup() {
            return { Download, Fold, CaretBottom }
        },
        computed: {
            dropDataTrans() {
                return this.dropData.filter(item => item.isShow)
            }
        },
        methods: {
            handleCommand(command) {
                const { callBackFunc } = this.dropDataTrans[command]
                if (callBackFunc) {
                    return this.$emit(callBackFunc)
                }
                throw new Error('dropData数组中请输入事件的回调函数callBackFunc')
            }
        }
    })
</script>
<style lang="less" scoped>
    .menu-list-btn {
        height: auto;
        font-weight: normal;
        color: @font_color_01;
        vertical-align: baseline;
        background-color: @theme_main;
        border-color: @theme_main;
        border-radius: 5px;

        &.el-button--small {
            font-size: 12px;

            .menu-txt {
                font-size: 12px;
            }
        }

        &.el-button--default {
            width: 138px;
            padding: 7px 10px 8px;
            font-size: 14px;

            .menu-txt {
                min-width: 75px;
                height: 19px;
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                line-height: 19px;
                color: @font_color_01;
                text-align: left;
            }
        }

        &:focus-visible {
            outline: 0;
        }

        .el-icon {
            margin: 0 0 0 4px;
        }
    }

    .el-dropdown-menu__item {
        &:not(.is-disabled):focus {
            color: @theme_main;
            background-color: var(--el-fill-color-light);
        }
    }

    :deep(.menu-btn-popper) {
        &.el-dropdown__popper.el-popper {
            top: 40px !important;
            left: 0 !important;
            // width: 269px;
            // height: 294px;
            height: 400px;
            padding: 12px 0;
            background: #ffffff;
            border-radius: 5px;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.12);

            .el-popper__arrow {
                display: none;
            }

            .el-dropdown-menu {
                padding: 0;
            }

            .drop-notice {
                height: 15px;
                margin: 0 0 5px 10px;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                line-height: 15px;
                color: @font_color_05;
            }

            .el-select-dropdown__item.selected {
                color: @theme_main;
            }

            .el-tree {
                .el-tree-node {
                    .el-tree-node__label {
                        font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                        font-size: 15px;
                        font-weight: bold;
                        color: @font_color;

                        &:hover {
                            color: @theme_main_hover;
                        }
                    }

                    .el-tree-node__children {
                        .el-tree-node__label {
                            font-family: 'Microsoft YaHei';
                            font-weight: normal;
                        }
                    }

                    > .el-tree-node__content {
                        height: 30px;
                    }

                    &.is-focusable.is-current {
                        > .el-tree-node__content {
                            > .el-tree-node__label {
                                font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                                font-weight: bold;
                                color: @theme_main;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
