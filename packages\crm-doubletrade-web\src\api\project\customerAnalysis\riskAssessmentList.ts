/*
 * @Description: 客户定位分析
 * @Author: chaohui.wu
 * @Date: 2023-03-23 15:42:38
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:12:51
 * @FilePath: /crm-rpo-template/packages/crm-template/src/api/project/customerAnalysis/riskAssessmentList.ts
 *
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import { AdjustKycParams, CustInfoParams } from '@/types/index'

/**
 * @description: 查询客户风险测评问卷
 * @param {object} params
 * @return {*}
 */
export const getCustKYC = (params: { conscustno: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/getcustkyc',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 保存调整问卷
 * @param {AdjustKycRes} params
 * @return {*}
 */
export const insertCustKYC = (params: AdjustKycParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/savecustkyc',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询客户基本信息
 * @param {CustInfoParams} params
 * @return {*}
 */
export const getCustInfo = (params: CustInfoParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/getcustinfo',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 问卷还原
 * @param {CustInfoParams} params
 * @return {*}
 */
export const resetCustKYC = (params: CustInfoParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/deletecustkyc',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询风险结果
 * @param {CustInfoParams} params
 * @return {*}
 */
export const getCustKYCrisk = (params: CustInfoParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/getcustkycrisk',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 保存客户定位信息
 * @return {*}
 */
export const saveLocate = (params: {
    conscustno: string
    assetId: string
    showCustInfo: string
    [x: string]: string
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/savelocate',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 客户定位分析主界面全量查询返回数据
 * @param {CustInfoParams} params
 * @return {*}
 */
export const getCustomerLocate = (params: {
    conscustno: string
    assetId: string
    [x: string]: string
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/asset/edit/custlocate/getcustomerlocate',
            method: 'post',
            data: params
        })
    )
}
