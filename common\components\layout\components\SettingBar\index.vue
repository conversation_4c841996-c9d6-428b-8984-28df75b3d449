<template>
    <div class="settingBarContainer">
        <el-dropdown size="small" @command="handleCommand">
            <span class="icon_user">
                <i class="iconfont icon_account" />
                <span class="userName">{{ userStore.userInfo ? userStore.userInfo.cn : '' }}</span>
            </span>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="2">退出</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <!-- <div class="dataRole">
            <change-local-data-role />
        </div> -->
    </div>
</template>

<script>
    import { useUserStore } from '../../stores/user'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'SettingBar',
        setup() {
            const userStore = useUserStore()
            return { userStore }
        },
        data() {
            return {}
        },
        methods: {
            handleCommand(command) {
                if (command === '2') {
                    this.userStore.logout()
                }
            }
        }
    })
</script>

<style lang="less">
    .settingBarContainer {
        display: flex;
        align-items: center;
        justify-content: right;
        margin-left: auto;
        color: @font_color_01;
        text-align: right;

        .dataRole {
            margin-left: 10px;
        }

        .icon_account {
            display: inline-block;
            margin-right: 6px;
            font-size: 19px;
            color: @font_color_01;
            vertical-align: top;
            cursor: pointer;
        }

        .userName {
            display: inline-block;
            font-size: 14px;
            color: @font_color_01;
        }

        .icon_user {
            display: inline-block;
            line-height: 40px;
            cursor: pointer;
        }
    }

    .dropdown_role_list {
        min-width: 60px;
        text-align: center;

        &.el-popper[x-placement^='bottom'] {
            margin-top: 6px;
        }

        .el-dropdown-menu__item {
            font-size: 13px;
        }
    }
</style>
