export {}
declare module './apiReqType' {
    // 保存反馈的请求参数接口
    interface ISaveFeedbackReq {
        visitMinutesId: string
        accompanyingId: string
        feedbackType: string
        summary: string
        suggestion: string
    }
    // 查询拜访反馈详情的请求参数接口
    interface IVisitFeedbackDetailReq {
        visitMinutesId: string
        feedbackType: string
    }

    export { ISaveFeedbackReq, IVisitFeedbackDetailReq }
}
