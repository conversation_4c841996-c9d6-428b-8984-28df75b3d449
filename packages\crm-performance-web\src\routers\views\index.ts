/*
 * @Description: 固定路由配置
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 13:52:35
 * @FilePath: /crm-web/packages/crm-performance-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { name: 'stockList' },
        children: [
            {
                path: '/performance/stockSplit',
                name: 'stockList',
                meta: {
                    title: '存量分成'
                },
                component: () => import('@/views/performance/stockSplit/stockList.vue')
            },
            {
                path: '/performance/beisen',
                name: 'beisenOrgConfig',
                meta: {
                    title: '存量分成'
                },
                component: () => import('@/views/performance/beisen/beisenOrgConfig.vue')
            },
            {
                path: '/performance/beisen/beisenPosLevel',
                name: 'beisenPosLevelConfig',
                meta: {
                    title: 'CRM北森职级映射'
                },
                component: () =>
                    import('@/views/performance/beisen/beisenPosLevel/beisenPosLevelConfig.vue')
            },
            {
                path: '/examine/ModifyExamineInfoDialog',
                name: 'ModifyExamineInfoDialog',
                meta: {
                    title: '修改考核信息'
                },
                component: () => import('@/views/performance/exaimne/ModifyExamineInfoDialog.vue')
            },
            {
                path: '/examine/consPerformanceCoeff',
                name: 'consPerformanceCoeff',
                meta: {
                    title: '修改考核信息'
                },
                component: () => import('@/views/performance/exaimne/consPerformanceCoeff.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
