<!--
 * @Description: 案例预览
 * @Author: chaohui.wu
 * @Date: 2023-10-07 18:50:07
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-12 10:18:05
 * @FilePath: /crm-web/packages/crm-common-web/src/views/viewer/viewerIdx.vue
 *  
-->
<template>
    <div class="demo-module">
        <PreviewWrapper :option="optionObj"></PreviewWrapper>
    </div>
</template>

<script setup lang="ts">
    import PreviewWrapper from './PreviewWrapper.vue'
    const optionObj = {
        title: `划转.xlsx`,
        fileType: `xlsx`,
        fileName: `2420`,
        userId: `12`
    }
    console.log('进入viewerIndex页面')
</script>

<style lang="less" scoped>
    .demo-module {
        touch-action: none;
    }
</style>
