/*
 * @Description:页面table列表公共hooks
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-09-06 18:45:44
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 15:32:00
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/hooks/useVisible.ts
 *
 */
export function useVisible({ props, emit }: any) {
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = () => {
        dialogVisible.value = false
    }
    return { dialogVisible, handleClose }
}
