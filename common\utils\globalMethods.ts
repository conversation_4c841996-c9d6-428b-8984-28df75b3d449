/*
 * @Description: 全局公共方法
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-12 11:09:08
 * @FilePath: /crm-web/common/utils/globalMethods.ts
 *
 */
import { removeToken, axiosRequest, deleteLocalItem, message } from './index'
import { responseCode, returnCodeRes } from '../constant/index'
import { paramsMerge } from '../api/mock'

// 支持预览的文件类型
const previewTypes = ['doc', 'docx', 'xls', 'xlsx', 'xlsm', 'ppt', 'pptx', 'pdf']

export const addScript = () => {
    if (window?.DocsAPI) {
        return
    }
    if (window?._msViewerApi) {
        const script = document.createElement('script')
        // script.src = `http://**************:8081/web-apps/apps/api/documents/api.js`
        script.src = `${window._msViewerApi}/web-apps/apps/api/documents/api.js`
        script.type = 'text/javascript'
        // script.async = false //可以明确将其设置为同步加载
        document.body.appendChild(script)
    }
}

/**
 * @description: 未登录逻辑
 * @return {*}
 */
export const logoutWithoutRequest = () => {
    const { origin, port, pathname } = window.location
    let redirect = location.hash.slice(1)
    redirect =
        redirect && !redirect.includes('error/') && redirect !== '/'
            ? decodeURIComponent(redirect)
            : ''

    // // 已在登录页情况下，不再重复跳转登录页
    // if (redirect.startsWith('/login')) {
    //     return
    // }

    removeToken()
    deleteLocalItem('USER_INFO')
    deleteLocalItem('USER_MENU')
    deleteLocalItem('hb_crm_token')

    window.location.href = `${origin}/crm-sys/login`
    // window.location.reload()
}

/**
 * 深拷贝
 */
export const deepClone = (data: any) => {
    if (!data || typeof data !== 'object') {
        return data
    }

    const newData: any = data instanceof Array ? [] : {}
    for (const key in data) {
        const tmpKey: any = typeof data[key] === 'object' ? deepClone(data[key]) : data[key]
        newData[key] = tmpKey
    }
    return newData
}

/**
 * 把嵌套tree数组扁平化
 * @param data <Array>
 * @param childName <String> 子集节点的名称
 * @return newData <Array>
 */
export const flatTree: any = (data: any, childName = 'children') => {
    if (!Array.isArray(data)) {
        console.warn('只支持传入数组')
        return data
    }
    return data.reduce((prev, curt) => {
        // 有子节点的话把子节点作为一级节点递归遍历
        const childList: any[] = curt[childName]?.length ? flatTree(curt[childName]) : []
        return [...prev, curt, ...childList]
    }, [])
}

/**
 * @description: 二维转一维
 * @param {any} arr
 * @return {*}
 */
export const flatten: any = (arr: any[]) => {
    return [].concat(...arr.map(x => (Array.isArray(x) ? flatten(x) : x)))
}
/**
 * @description: 导出导出单文件流
 * @param {*} data 数据对象
 * @return {*}
 */
export const singleFileUpload = (data: { fileByte: string; name: string; type: string }) => {
    const { fileByte, name, type } = data || {}
    const bstr = atob(fileByte)
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    const blob = new Blob([u8arr], {
        // 下载的文件类型
        type: type
    })
    const nav = window.navigator as any
    if (nav.msSaveOrOpenBlob) {
        nav.msSaveBlob(blob)
    } else {
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        window.URL.revokeObjectURL(link.href) // 释放内存
    }
}

/**
 * @description: 下载文件并导出
 * @param {*} params
 * @param {*} afterDownload
 * @param {*} successFunc
 * @return {*}
 */
export const downloadFile = async (
    params: any,
    afterDownload: any = null,
    successFunc: any = null
) => {
    // 错误回调
    const callbacks = {
        errorCB: (err: any) => {
            afterDownload && afterDownload()
            message({
                type: 'error',
                message: err?.desc || '文件下载失败',
                duration: 3000
            })
        }
    }
    // 请求参数
    const requestParams = {
        callbacks,
        timeout: 600 * 1000, // 大文件下载时间较长，超时时间设置为10分钟
        ...params
    }

    const res: any = await axiosRequest(paramsMerge(requestParams)).finally(afterDownload)
    const { code, data, description } = res || {}
    if (code === responseCode.SUCCESS || code === responseCode.CRM_SUCCESS) {
        if (Array.isArray(data)) {
            data.map(item => {
                return singleFileUpload(item)
            })
            return successFunc && successFunc()
        }
        singleFileUpload(data)
        return successFunc && successFunc(data.description)
    }
    return callbacks.errorCB({ desc: data.description })
}

/**
 * 获取操作按钮权限
 * @param elementName: 操作类型名称，如add, delete等
 * @param moduleName: 页面路由名称，非必传，不传默认取当前访问的路由名
 * @example 是否显示操作按钮：getElementPermission('delete') 返回true为显示，否则不显示
 */
export function getElementPermission(elementName: string, moduleName: string) {
    let localMenus = localStorage.getItem('USER_MENU')
    localMenus = localMenus ? JSON.parse(localMenus) : null
    // const menus = store?.getters?.user?.menus || localMenus || null
    const menus = localMenus || null

    if (!elementName || !menus) {
        return false
    }
    const currentPath = window.location.hash.split('#')[1]
    const menuList = flatTree(menus) || []
    const activeMenu = menuList.find((item: any) => {
        if (moduleName) {
            return moduleName === item.name
        }
        return item.path && item.path !== '/' && currentPath.includes(item.path)
    })

    /**
     * @modify xiang.zhou 尽调任务池是写死的路由，不在 menus 数据里，导致 exts 为空，前端抛异常
     */
    // console.log(elementName, moduleName, activeMenu)
    if (activeMenu) {
        const exts = JSON.parse(activeMenu.exts)
        if (exts && exts.action) {
            return exts.action.indexOf(elementName) > -1
        }
    }

    return false
}
