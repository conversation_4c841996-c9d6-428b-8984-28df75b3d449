/*
 * @Author: ch<PERSON><PERSON>.<EMAIL>
 * @Date: 2023-02-22 19:50:51
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-19 13:39:46
 * @FilePath: /crm-web/packages/crm-performance-web/public/msEnv.js
 * @Description: 宙斯配置项
 */
var _msEnvFlag = '1' // 1 线上环境  2 测试环境  3 mock环境  4 测试 doc 预览
var _msApiPrefix = window.origin + '/assetCurrent'
var _msApiPrefix_oldAsset = window.origin + '/oldAsset'

if (_msEnvFlag === '2') {
    _msApiPrefix = window.origin + '/hdCurrent'
    // _msApiPrefix = 'http://**************:8087'
} else if (_msEnvFlag === '3') {
    // _msApiPrefix = 'https://mock.apifox.cn/m1/2175602-0-default/'
    // _msApiPrefix = 'http://127.0.0.1:4523/m1/2810603-0-default'
    // api fox 的默认值
    // _msApiPrefix = 'https://mock.apifox.cn/m1/2810603-0-default'
    // apifox本地环境的值
    // _msApiPrefix = 'http://127.0.0.1:4523/m1/2810603-0-default'
    _msApiPrefix = 'http://localhost:8080'
    _msApiPrefix_oldAsset = 'https://mock.apifox.cn/m1/2175602-0-default/'
}
