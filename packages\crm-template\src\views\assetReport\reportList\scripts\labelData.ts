/*
 * @Description: 定义搜索的label列表
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    investmentDepartment: {
        label: '投顾所属部门',
        placeholder: '请选择投顾所属部门',
        selectList: [
            {
                key: '1',
                label: 'Howbuy'
            },
            {
                key: '2',
                label: '公募组合'
            }
        ]
    },
    ownerAccount: {
        label: '所属投顾',
        placeholder: '请选择所属投顾',
        selectList: []
    },
    clientName: {
        label: '客户姓名',
        placeholder: '输入客户姓名',
        selectList: []
    },
    phoneNumber: {
        label: '手机号',
        placeholder: '输入手机号搜索'
    },
    insertCustomerAccountNumber: {
        label: '投顾客户号',
        placeholder: '输入投顾客户号搜索'
    },
    aBillPassesThroughNumber: {
        label: '一账通号',
        placeholder: '输入一账通号搜索'
    },
    reportCreationTime: {
        label: '创建报告时间',
        // placeholder: ['起始日期', '结束日期']
        placeholder: '起始日期'
    },
    tabsPanelList: [
        { label: '我的报告', name: 'myReport' },
        { label: '全部报告', name: 'allReport' }
    ],
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    },
    chromeLink: {
        url: `https://dl.google.com/tag/s/appguid%3D%7B8A69D345-D564-463C-AFF1-A69D9E530F96%7D%26iid%3D%7B77CCCA8B-F59C-E570-E701-E2575690F29C%7D%26lang%3Dzh-CN%26browser%3D3%26usagestats%3D0%26appname%3DGoogle%2520Chrome%26needsadmin%3Dprefers%26ap%3Dx64-stable-statsdef_1%26installdataindex%3Dempty/chrome/install/ChromeStandaloneSetup64.exe`
    }
}
