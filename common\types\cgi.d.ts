/*
 * @Description: cgi ts格式
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-06-28 16:52:01
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:31:53
 * @FilePath: /crm-rpo-template/common/types/cgi.d.ts
 *
 */
export {}
declare module '@common/types/cgi' {
    interface CGIRes<T> {
        /**
         * 代码 成功-0000 失败-0001 参数错误-0002
         */
        code: string
        data: T
        /**
         * 描述
         */
        description: string
    }
    interface TableListData<T> {
        /**
         * 当前第几页
         */
        page: number
        rows: T
        /**
         * 返回数据量
         */
        size: number
        /**
         * 总数
         */
        total: number
    }
    export { CGIRes, TableListData }
}
