/**
 * @description: 配类型请求参数映射
 * @return {*}
 */
export const assetTypeReq = [
    {
        key: 'fof105',
        label: '大类进取FOF'
    },
    {
        key: 'fof106',
        label: '大类平衡FOF'
    },
    {
        key: 'fof104',
        label: '大类稳健FOF'
    },
    {
        key: '101',
        label: '股票型'
    },
    {
        key: '102',
        label: '股权型'
    },
    {
        key: '201',
        label: 'CTA策略'
    },
    {
        key: '202',
        label: '另类策略'
    },
    {
        key: '203',
        label: '宏观策略'
    },
    {
        key: '301',
        label: '固定收益'
    },
    {
        key: '302',
        label: '市场中性'
    },
    {
        key: '999',
        label: '其它'
    },
    {
        key: '303',
        label: '收益型保险'
    }
    // {
    //     key: 'combination',
    //     label: '公募组合'
    // }
]

/**
 * @description: 配类型请求参数映射
 * @return {*}
 */
export const TYPEAll = [
    {
        key: 'fof105',
        label: '大类进取FOF'
    },
    {
        key: 'fof106',
        label: '大类平衡FOF'
    },
    {
        key: 'fof104',
        label: '大类稳健FOF'
    },
    {
        key: '101',
        label: '股票型'
    },
    {
        key: '102',
        label: '股权型'
    },
    {
        key: '201',
        label: 'CTA策略'
    },
    {
        key: '202',
        label: '另类策略'
    },
    {
        key: '203',
        label: '宏观策略'
    },
    {
        key: '301',
        label: '固定收益'
    },
    {
        key: '302',
        label: '市场中性'
    },
    {
        key: '303',
        label: '收益型保险'
    },
    {
        key: '999',
        label: '其它'
    },
    {
        key: 'combination',
        label: '公募组合'
    }
]

/**
 * @description: 优选产品配类型参数映射
 * @return {*}
 */
export const ADJUSTTYPE = [
    {
        key: '105',
        label: '大类进取FOF'
    },
    {
        key: '106',
        label: '大类平衡FOF'
    },
    {
        key: '104',
        label: '大类稳健FOF'
    },
    {
        key: '101',
        label: '股票型'
    },
    {
        key: '102',
        label: '股权型'
    },
    {
        key: '201',
        label: 'CTA策略'
    },
    {
        key: '202',
        label: '另类策略'
    },
    {
        key: '203',
        label: '宏观策略'
    },
    {
        key: '301',
        label: '固定收益'
    },
    {
        key: '302',
        label: '市场中性'
    },
    {
        key: '999',
        label: '其它'
    },
    {
        key: 'DLPZFOF',
        label: '大类配置FOF'
    }
]

/**
 * @description: 资配类型返回参数映射
 * @return {*}
 */
export const assetTypeRes = [
    {
        key: 'DLPZFOF105',
        label: '大类进取FOF'
    },
    {
        key: 'DLPZFOF106',
        label: '大类平衡FOF'
    },
    {
        key: 'DLPZFOF104',
        label: '大类稳健FOF'
    },
    {
        key: '101',
        label: '股票型'
    },
    {
        key: '102',
        label: '股权型'
    },
    {
        key: '201',
        label: 'CTA策略'
    },
    {
        key: '202',
        label: '另类策略'
    },
    {
        key: '203',
        label: '宏观策略'
    },
    {
        key: '301',
        label: '固定收益'
    },
    {
        key: '302',
        label: '市场中性'
    },
    {
        key: '999',
        label: '其它'
    }
]

/**
 * @description: 投资类型
 * @return {*}
 */
export const INVESTTYPE = [
    {
        key: '1',
        label: '公募基金'
    },
    {
        key: '2',
        label: '私募基金'
    },
    {
        key: '3',
        label: '股票'
    },
    {
        key: '4',
        label: '银行理财'
    },
    {
        key: '5',
        label: '债券'
    },
    {
        key: '99',
        label: '其他'
    }
]

/**
 * @description: 标签展示 1 red | 2 gray
 * @return {*}
 */
export const TIPSLABEL = [
    {
        key: 'isConsultantPortfolio',
        label: '投顾组合',
        styleColor: '1'
    },
    {
        key: 'isPub30',
        label: '公募30',
        styleColor: '1'
    },
    {
        key: 'isQdii',
        label: 'QDII',
        styleColor: '2'
    },
    {
        key: 'isOptimizeFof',
        label: '精选FOF',
        styleColor: '1'
    },
    {
        key: 'isOverseasOptimize',
        label: '海外优选',
        styleColor: '1'
    }
]

/**
 * @description: 日期选项枚举
 * @return {*}
 */
export const DATELIST = [
    {
        label: '近一年',
        value: '1'
    },
    {
        label: '近三年',
        value: '2'
    },
    {
        label: '今年以来',
        value: '3'
    },
    {
        label: '自定义',
        value: '4'
    }
]

/**
 * @description: 比较基准
 * @return {*}
 * eg: 沪深300、万得全A、中证500、创业板指、上证指数、深证成指、上证50、信用债3-5AA+、恒生指数、南华商品、纳斯达克1000、标普500
 */
export const BASECOMPARE = [
    {
        label: '沪深300',
        value: '000300'
    },
    {
        label: '万得全A',
        value: '881001'
    },
    {
        label: '中证500',
        value: '000905'
    },
    {
        label: '创业板指',
        value: '399006'
    },
    {
        label: '上证指数',
        value: '000001'
    },
    {
        label: '深证成指',
        value: '399001'
    },
    {
        label: '上证50',
        value: '000016'
    },
    {
        label: '信用债3-5AA+',
        value: '931213'
    },
    {
        label: '恒生指数',
        value: 'HSI001'
    },
    {
        label: '南华商品',
        value: 'CI0077'
    },
    {
        label: '纳斯达克1000',
        value: 'ND100'
    },
    {
        label: '标普500',
        value: 'SP500'
    }
]

/**
 * @description: 数据补齐方式
 * @return {*}
 * eg: 使用好买指数替代（推荐）、旗舰及指数拼接、仅成分基金（不做拼接或替换）
 */
export const DATAPOLISH = [
    {
        label: '使用好买指数替代（推荐）',
        value: '3'
    },
    {
        label: '旗舰及指数拼接',
        value: '2'
    },
    {
        label: '仅成分基金（不做拼接或替换）',
        value: '0'
    }
]

/**
 * @description: 策略分类标签展示
 * @return {*} tacticType
 */
export const TACTICTYPE = [
    {
        label: '新方程FOF',
        subTitle: '长期均衡，边际逆向',
        key: 'fof'
    },
    {
        label: '进取型',
        subTitle: '立足长期，优选策略',
        key: '1'
    },
    {
        label: '平衡型',
        subTitle: '多元对冲，平衡风险',
        key: '2'
    },
    {
        label: '稳健型',
        subTitle: '降低波动，稳健回报',
        key: '3'
    }
]

/**
 * @description: 风险等级映射
 * @return {*}
 */
export const RISKLEVEL = [
    {
        key: '1',
        label: '低风险(R1)',
        label1: 'R1'
    },
    {
        key: '2',
        label: '中低风险(R2)',
        label1: 'R2'
    },
    {
        key: '3',
        label: '中风险(R3)',
        label1: 'R3'
    },
    {
        key: '4',
        label: '中高风险(R4)',
        label1: 'R4'
    },
    {
        key: '5',
        label: '高风险(R5)',
        label1: 'R5'
    }
]

/**
 * @description: 资产配置比例变化产品名称映射
 * @return {*}
 */
export const RARIOTYPENAME = [
    {
        key: 'fof105',
        label: '大类进取FOF'
    },
    {
        key: 'fof106',
        label: '大类平衡FOF'
    },
    {
        key: 'fof104',
        label: '大类稳健FOF'
    },
    {
        key: '101',
        label: '股票型'
    },
    {
        key: '102',
        label: '股权型'
    },
    {
        key: '201',
        label: 'CTA策略'
    },
    {
        key: '202',
        label: '另类策略'
    },
    {
        key: '203',
        label: '宏观策略'
    },
    {
        key: '301',
        label: '固定收益'
    },
    {
        key: '302',
        label: '市场中性'
    },
    {
        key: '303',
        label: '收益型保险'
    },
    {
        key: '999',
        label: '其它'
    },
    {
        key: '0',
        label: '国内'
    },
    {
        key: '1',
        label: '海外'
    }
]
