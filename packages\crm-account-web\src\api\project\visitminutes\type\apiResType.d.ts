export {}
declare module './apiResType' {
    /**
     * 拜访纪要信息
     */
    interface VisitMinutesInfo {
        /**
         * 创建日期 格式YYYY-MM-DD HH:MM:SS
         */
        createTime: string

        /**
         * 拜访日期 格式YYYY-MM-DD
         */
        visitDt: string

        /**
         * 拜访目的 多个用逗号分隔
         */
        visitPurpose: string

        /**
         * 投顾客户号
         */
        consCustNo: string

        /**
         * 客户姓名
         */
        custName: string

        /**
         * 纪要创建人姓名
         */
        creatorName: string

        /**
         * 所属中心
         */
        centerName: string

        /**
         * 所属区域
         */
        areaName: string

        /**
         * 所属分公司
         */
        branchName: string

        /**
         * 沟通方式
         */
        visitType: string

        /**
         *, 客户存量
         */
        marketVal: string

        /**
         * 客户综合健康度
         */
        healthAvgStar: string

        /**
         * 提供资料
         */
        giveInformation: string

        /**
         * 参与人员及角色
         */
        attendRole: string

        /**
         * 产品服务反馈
         */
        productServiceFeedback: string

        /**
         * IPS报告反馈
         */
        ipsFeedback: string

        /**
         * 币种+金额+万，多个逗号分隔
         */
        addAmount: string

        /**
         * 关注资产
         */
        focusAsset: string

        /**
         * 客户需求
         */
        estimateNeedBusiness: string

        /**
         * 工作计划
         */
        nextPlan: string

        /**
         * 陪访人类型
         */
        accompanyingType: string

        /**
         * 陪访人姓名，多个逗号分隔
         */
        accompanyingUser: string

        /**
         * 陪访人反馈-概要，格式：姓名:内容，多个分号分隔
         */
        accompanySummary: string

        /**
         * 陪访人反馈-建议，格式：姓名:内容，多个分号分隔
         */
        accompanySuggestion: string

        /**
         * 上级主管姓名
         */
        managerName: string

        /**
         * 主管反馈概要
         */
        managerSummary: string

        /**
         * 主管反馈建议
         */
        managerSuggestion: string

        /**
         * 是否可陪访人反馈
         */
        canAccompanyFeedback: boolean

        /**
         * 是否可主管反馈
         */
        canManagerFeedback: boolean
    }

    /**
     * 查询客户拜访纪要列表响应数据
     */
    interface VisitMinutesListRes {
        /**
         * 总记录数
         */
        total: number

        /**
         * 拜访纪要列表
         */
        list: VisitMinutesInfo[]
    }

    /**
     * 导出客户拜访纪要列表响应数据
     */
    interface ExportVisitMinutesRes {
        /**
         * 文件字节数组的base64编码串
         */
        fileByte: string

        /**
         * 文件名
         */
        name: string

        /**
         * 文件类型
         */
        type: string

        /**
         * 错误信息
         */
        errorMsg: string
    }
    // 查询拜访反馈详情的响应数据接口
    interface IVisitFeedbackDetailRes {
        custName: string
        consCustNo: string
        visitDt: string
        visitType: string
        marketVal: string
        healthAvgStar: string
        visitPurpose: string[]
        visitPurposeOther: string
        ipsReport: {
            reportId: string
            reportTitle: string
        }
        giveInformation: string
        accompanyingUser: string
        attendRole: string
        productServiceFeedback: string
        ipsFeedback: string
        addAmountRmb: string
        addAmountForeign: string
        focusAsset: string
        estimateNeedBusiness: string
        nextPlan: string
        userName: string
        accompanyingId: string
        managerName: string
        summary: string
        suggestion: string
        canNotEditSummary: boolean
    }

    export { VisitMinutesListRes, VisitMinutesInfo, ExportVisitMinutesRes, IVisitFeedbackDetailRes }
}
