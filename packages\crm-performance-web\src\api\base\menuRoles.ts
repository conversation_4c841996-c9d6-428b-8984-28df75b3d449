/*
 * @Description: 项目菜单角色权限
 * @Author: chao<PERSON>.wu
 * @Date: 2023-09-14 12:33:37
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-14 12:37:17
 * @FilePath: /crm-web/packages/crm-performance-web/src/api/base/menuRoles.ts
 *
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../mock'

/**
 * @description: 查菜单下用户拥有的操作权限列表
 * @return {*}
 */
export const getMenuPermission = (params: { menuCode: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/pageauth/fetchauth',
            method: 'post',
            data: params
        })
    )
}
