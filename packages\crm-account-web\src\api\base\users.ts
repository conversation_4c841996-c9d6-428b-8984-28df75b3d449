/*
 * @Author: xing.zhou
 * @Date: 2021-12-13 14:48:00
 * @LastEditTime: 2023-07-24 10:12:27
 * @LastEditors: chaohui.wu
 * @Description: 用户管理 -- api
 */
import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../mock'

// 只有qx_defined_user（用户自定义表），不走qx_user（域账号查询）
export function login(data: any) {
    return axiosRequest({
        url: '/auth/login',
        method: 'post',
        data
    })
}
export function logout() {
    return axiosRequest({
        url: '/auth/logout',
        method: 'get'
    })
}

// 2020-12-31 xiang.zhou : /user/info => /userCommon/info
export function getUserInfo() {
    return axiosRequest({
        url: '/user/common/info',
        method: 'get'
    })
}

// 2020-12-31 xiang.zhou : /user/getListByRoleId => /userCommon/getListByRoleId
export function getListByRoleId(data: any) {
    return axiosRequest({
        url: '/user/common/get/list',
        method: 'get',
        params: data
    })
}

// 2020-12-31 xiang.zhou : /user/getPermission => /userCommon/getPermission
export function getSelfPermission() {
    return axiosRequest({
        url: '/user/common/get/permission',
        method: 'get'
    })
}

export function remove(data: any) {
    return axiosRequest({
        url: '/user/remove',
        method: 'delete',
        params: data
    })
}

// 获取表头显示字段，params: { moduleName：模块名称 }
export function getTableColumns(params: any) {
    return axiosRequest({
        url: '/userShowColumn/get',
        method: 'get',
        params
    })
}

// 设置表头显示字段，params: { moduleName：模块名称, columns：'字段1，字段2' }
export function setTableColumns(params: any) {
    return axiosRequest({
        url: '/userShowColumn/set',
        method: 'put',
        data: params
    })
}

// 查询（域账号）用户列表
export function getList(data: any) {
    return axiosRequest({
        url: '/user/getList',
        method: 'get',
        params: data
    })
}

// 查询（自定义账号）用户列表
export function getDefinedUserList(data: any) {
    return axiosRequest({
        url: '/defined/user/get/list',
        method: 'get',
        params: data
    })
}

// 新增用户/账号
export function add(data: any) {
    return axiosRequest({
        url: '/user/add',
        method: 'put',
        data
    })
}

// 自定义账户新增/创建
export function addDefinedUser(data: any) {
    return axiosRequest({
        url: '/defined/user/add',
        method: 'put',
        data
    })
}

// 自定义账户修改
export function updateDefinedUser(data: any) {
    return axiosRequest({
        url: '/defined/user/update',
        method: 'post',
        data
    })
}

export function changeLocalDataRole(data: any) {
    return axiosRequest({
        url: '/user/common/change/role',
        method: 'get',
        params: data
    })
}

// 根据keyword获取当前用户的下级用户
export function querySubUserInfo(param: any) {
    return axiosRequest({
        url: '/user/common/query/info',
        method: 'get',
        params: param
    })
}

// 获取用户拥有的菜单
export function getUserMenu() {
    return axiosRequest({
        url: '/user/common/menu',
        method: 'get'
    })
}

// 内网登录不需要验证码
export function getAuthExtranet() {
    return axiosRequest({
        url: '/auth/extranet/get',
        method: 'get'
    })
}

export function searchLdapUser(param: any) {
    return axiosRequest({
        url: '/ldap/search',
        method: 'get',
        params: param
    })
}

export const queryConsultant = (params: {
    orgCode: string
    status?: string
    recursive?: string //是否为递归查询 组织架构下的投顾 1-是 0-否。 默认1-是
    menuCode?: string
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/consultant/queryauthconsultant',
            method: 'post',
            data: params,
            canclePending: true
        })
    )
}
