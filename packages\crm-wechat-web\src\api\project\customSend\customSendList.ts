/*
 * @Description:自定义发送任务
 * @Author: chaohui.wu
 * @Date: 2023-08-02 13:12:31
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-23 10:55:01
 * @FilePath: /crm-web/packages/crm-wechat-web/src/api/project/customSend/customSendList.ts
 *
 */

import { axiosRequest } from '@common/utils/index'
import { paramsMerge } from '../../mock'
import { CustSendParams } from './type/apiType'

/**
 * @description: 自定义发送任务初始化
 * @return {*}
 */
export const getInitData = (params: { sequenceNo: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/wechat/customizesend/initdata',
            method: 'post',
            // data: { ...params, cookieTicket: 'ST-874b67dd2d934da0a92261444e474304' },
            data: params,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * @description: 文件校验
 * @return {*}
 */
export const checkFile = () => {
    return axiosRequest(
        paramsMerge({
            url: '/wechat/customizesend/checkfile',
            method: 'post',
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * @description: 发送预览
 * @return {*}
 */
export const previewSend = (params: { importType: string; sequenceNo: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/wechat/customizesend/previewsend',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.send-preview-list',
                isCust: false
            }
        })
    )
}

/**
 * @description: 提交
 * @return {*}
 */
export const submitSend = (params: CustSendParams) => {
    return axiosRequest(
        paramsMerge({
            url: '/wechat/customizesend/submitsend',
            method: 'post',
            data: params,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}

/**
 * @description: 去重确定
 * @param {CustSendParams} params
 * @return {*}
 */
export const confirmRepeat = (params: { sequenceNo: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/wechat/customizesend/confirmrepeat',
            method: 'post',
            data: params,
            loadingParams: {
                target: 'body',
                isCust: true
            }
        })
    )
}
