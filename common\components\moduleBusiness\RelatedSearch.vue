<!--
 * @Description: 搜索投顾姓名组件
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-11-20 15:24:27
 * @FilePath: /crm-web/common/components/moduleBusiness/relatedSearch.vue
-->
<template>
    <!-- 下拉搜索多选 -->
    <div v-if="type === 'select'">
        <el-select
            v-model="relatedList"
            class="cust-select"
            size="small"
            multiple
            filterable
            clearable
            remote
            :remote-show-suffix="false"
            reserve-keyword
            collapse-tags
            :max-collapse-tags="4"
            :remote-method="remoteMethod"
            placeholder="输入搜索"
            :loading="loading"
            @change="handleSelect"
        >
            <!-- 前置图标 -->
            <template v-if="prefix" #prefix>
                <el-icon class="el-input__icon" @click="handleIconClick">
                    <Search />
                </el-icon>
            </template>
            <template #default>
                <el-option v-if="showAll" label="全部" value="all" @click="selectAll" />
                <el-option
                    v-for="(item, index) in relatedOptions"
                    :key="`option-tpl-${index}`"
                    :label="item.label"
                    :value="item.value"
                    class="cust-option"
                >
                </el-option>
            </template>
        </el-select>
    </div>
    <div v-else-if="type === 'autocomplete'">
        <!-- 远程搜索单选自动填充 -->
        <el-autocomplete
            v-model="searchKey"
            class="crm_autocomplete"
            size="small"
            :popper-class="popperClss"
            :value-key="valueFormat"
            :fetch-suggestions="querySearchAsync"
            v-bind="$attrs"
            @handleSelect="handleSelect"
        >
            <!-- 前置图标 -->
            <template v-if="prefix" #prefix>
                <el-icon class="el-input__icon" @click="handleIconClick">
                    <Search />
                </el-icon>
            </template>
            <!-- 后置图标 -->
            <template v-if="suffix" #suffix>
                <i :class="suffix" @click="$emit('suffixIconClick')" />
            </template>
            <!-- 下拉选项 -->
            <template #default="{ item }">
                <div>
                    <div v-if="item.fullName" class="item-span item-span1">
                        <span v-dompurify-html="highlightSearchKey(item.fullName)"></span>
                    </div>
                    <div
                        v-dompurify-html="highlightSearchKey(item[valueFormat])"
                        class="item-span fund-name"
                    ></div>
                </div>
            </template>
        </el-autocomplete>
    </div>
</template>

<script setup lang="ts">
    import { Search } from '@element-plus/icons-vue'
    import { message, messageBox, fetchRes } from '../../utils/index'
    import { computed, ref, unref } from 'vue'
    defineOptions({
        name: 'RelatedSearch'
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            searchFunc: any
            handleIconClick?: any
            // eslint-disable-next-line vue/require-default-prop
            params?: {
                type?: string
            }
            labelFormat?: string
            valueFormat?: string
            inputFormat?: string
            prefix?: boolean | string
            suffix?: boolean | string
            placeholder?: string
            type?: 'search' | 'select' | 'autocomplete'
            showAll?: boolean //展示全部选项
        }>(),
        {
            searchFunc: () => undefined,
            handleIconClick: () => undefined,
            params: () => {
                return {
                    type: ''
                }
            },
            labelFormat: 'productName',
            valueFormat: 'label',
            inputFormat: '',
            prefix: false,
            suffix: false,
            placeholder: '搜索',
            type: 'search',
            showAll: false
        }
    )

    /**
     * @description: 外抛事件
     * @return {*}
     */
    const emit = defineEmits(['select', 'suffixIconClick', 'callBack'])

    /**
     * @description: 是否包含投顾
     * @param {*} computed
     * @return {*}
     */
    const searchKey = ref('')

    /**
     * @description: 弹框样式联动
     * @param {*} computed
     * @return {*}
     */
    const popperClss = computed(() => {
        return `crm_autocomplete_popper}`
    })

    /**
     * @description: 异步搜索
     * @param {*} queryString
     * @param {*} cb
     * @return {*}
     */
    // 上次搜索关键字
    const lastQueryString = ref<string>('')
    // 加载状态
    const loading = ref<boolean>(false)
    // 搜索结果
    const relatedOptions = ref<{ label: string; value: string }[]>([])
    const querySearchAsync = (queryString: string, cb: any) => {
        const { searchFunc, params } = props || {}
        // 如果搜索的关键字为空  则直接cb
        if (!queryString) {
            cb([])
        } else if (queryString === lastQueryString.value) {
            // 如果关键字与上次的关键字一致   则不进行搜索   直接cb上次的结果
            cb(relatedOptions.value)
        } else {
            getRelatedInfo(queryString, (val: { label: string; value: string }[]) => {
                cb(val)
            })
        }
    }

    /**
     * @description: 获取投顾数据
     * @param {*} query
     * @param {*} callBack
     * @return {*}
     */
    const getRelatedInfo = (query: string, callBack?: any) => {
        const { searchFunc, params } = props || {}
        loading.value = true
        // 如果关键字不一致  则搜索  成功后将result和lastQueryString更新
        fetchRes(searchFunc({ recursive: '1', fuzzyTerm: query }), {
            successCB: (res: any) => {
                lastQueryString.value = query
                const { rows } = res || { rows: [] }
                relatedOptions.value = rows.map((item: any) => {
                    if (item) {
                        const { centerOrgName, districtName, partOrgName } = item
                        item.label = `${item.consName} ${getLable(
                            centerOrgName,
                            districtName,
                            partOrgName
                        )}`
                        item.value = `${item.consCode}`
                    }
                    return item
                })
                callBack && callBack(relatedOptions.value)
                loading.value = false
            },
            catchCB: () => {
                loading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 远程方法
     * @param {*} query
     * @return {*}
     */
    const remoteMethod = (query: string) => {
        console.log(query)
        if (query) {
            getRelatedInfo(query)
        }
    }

    /**
     * @description: 替换搜索关键字
     * @param {*} text
     * @return {*}
     */
    const highlightSearchKey = (text: any) => {
        if (!text) {
            return text
        }
        searchKey.value = lastQueryString.value || ''
        if (!searchKey.value) {
            return text
        }
        const replaceReg = new RegExp(searchKey.value, 'g') // 匹配关键字正则
        const replaceString = '<span class="highlight_search_key">' + searchKey.value + '</span>' // 高亮替换v-html值
        return text.replace(replaceReg, replaceString) // 开始替换
    }

    /**
     * @description: 选中字段
     * @param {*} item
     * @return {*}
     */
    const selectList = ref<any>([])
    const handleSelect = (optionItem: any) => {
        if (optionItem) {
            selectList.value = relatedOptions.value.filter((store: any) => {
                return optionItem.includes(store.value)
            })
            emit('callBack', { selectList: selectList.value })
        }
    }

    /**
     * @description: 获取标签拼取规则
     * @param {*} centerOrgName
     * @param {*} districtName
     * @param {*} partOrgName
     * @return {*}
     */
    const getLable = (centerOrgName: string, districtName: string, partOrgName: string) => {
        let labelStrTpl = ''
        if (centerOrgName) {
            labelStrTpl += centerOrgName
        }
        if (districtName) {
            labelStrTpl += districtName
        }
        if (partOrgName) {
            labelStrTpl += partOrgName
        }
        return labelStrTpl
    }

    /**
     * @description: 多选列表
     * @return {*}
     */
    const relatedList = ref<any>([])

    /**
     * @description: 全选
     * @return {*}
     */
    const selectAll = () => {
        const { showAll } = props || {}
        if (showAll) {
            relatedList.value = relatedOptions.value.map((item: any) => item.value)
        }
    }

    /**
     * @description: 重置
     * @return {*}
     */
    const handleReset = () => {
        const { type } = props || {}
        switch (type) {
            case 'select':
                console.log(11111)
                relatedList.value = []
                break
            default:
                break
        }
    }
    defineExpose({ handleReset })
</script>
<style lang="less">
    .el-input {
        --el-input-focus-border-color: @border_focus;
    }

    .crm_autocomplete {
        width: 100%;
        font-size: 12px;
        line-height: 24px;

        .el-input--small {
            .el-input__inner {
                height: 24px;
                padding: 0 8px 0 0;

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        .is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        .el-input__inner {
            // border-color: #d5d6da;
            color: @font_color_02;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }
        }
    }
    // 自定义搜索样式
    .cust-select {
        width: 100%;
        font-size: 12px;
        line-height: 24px;

        .el-input--small {
            .el-input__inner {
                height: 24px;
                padding: 0 8px 0 0;

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        .is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        .el-input__prefix {
            .el-input__inner {
                padding-left: 50px;
            }
        }

        &.el-select--small {
            .el-select-tags-wrapper.has-prefix {
                margin-left: 38px;
            }
        }

        .el-input__inner {
            // border-color: #d5d6da;
            color: @font_color_02;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }
        }
    }

    .crm_autocomplete_popper {
        li {
            display: flex;
            justify-content: space-between;

            .highlight_search_key {
                color: @theme_main;
            }

            .el-icon.is-loading {
                margin: auto;
            }
        }

        &.crm-auto-org {
            li {
                line-height: 1.2;
                white-space: inherit;
            }

            .el-autocomplete-suggestion__list > li {
                line-height: 1.2;
            }
        }
    }

    .el-autocomplete-suggestion__list {
        li {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            line-height: 24px;

            .item-span {
                min-width: 80px;
                padding: 5px 0;

                &:last-child {
                    padding-right: 0;
                }
            }
        }
    }

    .crm-auto-org {
        .item-span1 {
            width: 150px;
        }
    }

    .cust-search-box {
        position: relative;

        .list-container {
            position: absolute;
            top: 30px;
            left: 0;
        }
    }
</style>
