<!--
 * @Description: 首页路由及layout
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:53:39
 * @FilePath: /crm-rpo-template/packages/crm-template/src/App.vue
 *  
-->
<script>
    import { defineComponent } from 'vue'
    import { ElConfigProvider } from 'element-plus'

    import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

    export default defineComponent({
        components: {
            ElConfigProvider
        },
        setup() {
            return {
                locale: zhCn
            }
        }
    })
</script>

<template>
    <el-config-provider :locale="locale">
        <router-view v-slot="{ Component }">
            <!-- <keep-alive> -->
            <component :is="Component" />
            <!-- </keep-alive> -->
        </router-view>
    </el-config-provider>
</template>

<style lang="less">
    @import '@common/assets/css/elVariable.less';
    @import '@common/assets/css/pageCommon.less';
    // @import './assets/css/custFont.less';
</style>
