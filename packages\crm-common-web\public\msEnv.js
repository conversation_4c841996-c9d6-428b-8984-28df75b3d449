/*
 * @Author: chao<PERSON>.<EMAIL>
 * @Date: 2023-02-22 19:50:51
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-12-27 16:40:17
 * @FilePath: /crm-web/packages/crm-common-web/public/msEnv.js
 * @Description: 宙斯配置项
 */
var _msEnvFlag = '1' // 1 线上环境  2 测试环境  3 mock环境  4 测试 doc 预览
var _msApiPrefix = window.origin + '/assetCurrent'
var _msApiPrefix_oldAsset = window.origin + '/oldAsset'
var _msViewer = window.origin + '/crm-cgi/inner/hbDoc/docManagement/docPreViewOnline?docId='
var _msViewerApi = "http://**************:8081"

if (_msEnvFlag === '2') {
    _msApiPrefix = window.origin + '/hdCurrent'
    // _msApiPrefix = 'http://**************:8087'
    _msViewer = window.origin + '/crm-cgi/inner/hbDoc/docManagement/docPreViewOnline?docId='
} else if (_msEnvFlag === '3') {
    _msApiPrefix = 'https://mock.apifox.cn/m1/2175602-0-default/'
    _msApiPrefix_oldAsset = 'https://mock.apifox.cn/m1/2175602-0-default/'
    _msViewer = 'http://crm.it31.k8s.howbuy.com/crm-cgi/inner/hbDoc/docManagement/docPreViewOnline?docId='
}
