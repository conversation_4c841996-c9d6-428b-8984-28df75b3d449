<!--
 * @Description: 操作明细/客户明细
 * @Author: chaohui.wu
 * @Date: 2023-04-07 10:56:38
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 18:17:55
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/custDetail.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="200px"
        title="详情"
        :slot-list="['default', 'footer']"
        :before-close="handleClose"
        :close-on-click-modal="false"
    >
        <template #default>
            <div>
                <base-table
                    :columns="columnList"
                    :data="dataList"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    :border="true"
                    height="100%"
                    operation-width="60"
                >
                </base-table>
            </div>
        </template>
        <!-- 删除确认 -->
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script setup lang="ts">
    import { formatTableValue } from '@common/utils/index'
    import { useVisible } from '../scripts/hooks/useVisible'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            transData?: {
                phone: string
            }
        }>(),
        {
            visibleCus: true,
            transData: () => {
                return {
                    phone: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const columnList = computed(() => {
        return [
            {
                key: 'phone',
                label: '身份证号',
                width: 250,
                formatter: formatTableValue
            }
        ]
    })

    const dataList = ref<any>([])

    onMounted(() => {
        dataList.value = [
            {
                phone: props.transData.phone
            }
        ]
    })
</script>

<style lang="less" scoped>
    .notice-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;

        .txt-notice {
            padding: 4px 11px;
            // font-size: 12px;
            // font-family: Microsoft YaHei;
            // color: @theme_main;
            // background-color: rgba(208, 2, 27, 0.05);
        }
    }

    .ext-invest-index-module {
        display: flex;
        flex-direction: column;
        height: calc(56vh - 76px);
    }
</style>
