/*
 * @Description: 自定义指令集合
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-01 19:54:36
 * @FilePath: /crm-asset-web/src/utils/directives.ts
 *
 */

const directives: any = {
    // auto focus when component was inserted
    focus: {
        inserted(el: any) {
            el.focus()
        }
    }
}

export default {
    install(Vue: any) {
        for (const key in directives) {
            Vue.directive(key, directives[key])
        }
    }
}
