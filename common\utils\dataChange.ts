/*
 * @Description: 数据转换ts
 * @Author: chaohui.wu
 * @Date: 2023-04-13 10:14:18
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-25 10:12:15
 * @FilePath: /crm-web/common/utils/dataChange.ts
 *
 */
import { assetTypeReq } from '../constant/index'
import { getString, dateFormat, datenewFormat } from './index'

/**
 * @description: 将枚举值转为下拉列表
 * @param {object} param1
 * @return {*}
 */
export const transMapToArray = (val: Map<string | number, string>) => {
    return Array.from(val).map(item => {
        return {
            key: item[0],
            label: item[1]
        }
    })
}

/**
 * @description: o值传参数处理
 * @return {*}
 */
export const zeroTrans = ({ val, fixed }: { val: string | number; fixed?: number }) => {
    return val === null ? null : val === '' ? '' : fixed ? Number(val).toFixed(fixed) : Number(val)
}

/**
 * @description: 排除下拉列表里面的某一项
 * @param {object} list
 * @param {string} excludeList
 * @return {*}
 */
export const excludeArr = (list: any[], excludeList: any[], key = 'key') => {
    return list.filter(item => !excludeList.includes(item[key]))
}

/**
 * @description: 将数字专为会计计数法
 * @param {*} num ｜ number
 * @param {*} fixed | number
 * @return {*} string
 */
export const formaAccountingNumber = ({ num = '0', fixed = 2 }: any) => {
    if (isNaN(Number(num))) {
        return num.toString()
    }

    // Round the number to 2 decimal places
    num = parseFloat(Number(num).toFixed(fixed))

    // Convert the number to a string
    const numStr = num.toString()

    // Split the number into integer and decimal parts
    const parts = numStr.split('.')
    const integerPart = parts[0]
    const decimalPart = parts.length > 1 ? '.' + parts[1] : '.' + '0'.repeat(fixed)

    // Add commas to the integer part
    const formattedIntegerPart = parseInt(integerPart).toLocaleString()

    // Combine the integer and decimal parts and return the result
    return formattedIntegerPart + decimalPart
}

/**
 * @description: 转会计计数法
 * @param {*} param1
 * @return {*}
 */
export const formatNumber = ({ num = 0 }): string => {
    const decimalRegex = /\.(\d{2})\d*$/
    const matches = decimalRegex.exec(num.toFixed(2))
    const decimalPart = matches ? matches[0] : ''
    const integerPart = num.toFixed(0).replace(decimalRegex, '')
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return formattedIntegerPart + decimalPart
}

/**
 * 判断字段是否有值及无值时的展示处理
 * @param val
 * @param blankPlaceholder: 无数据时的展示字符，默认'-'
 */
export const formatValue = (val: string | number, blankPlaceholder = '—') => {
    if (!getString(val)) {
        return blankPlaceholder
    }
    return val || blankPlaceholder
}

export const formatThousands = (val: string | number) => {
    if (!getString(val)) {
        return val
    }
    return val.toString().replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
}

/**
 * 把申请份额数据数据转成千分位显示，小数点后保留四位小数,小数点后的不显示千分位
 */
export const formatAMTThousands = (val: string | number) => {
    if (!getString(val)) {
        return val
    }
    if (typeof val === 'number') {
        val = val.toFixed(6)
    } else {
        val = parseFloat(val).toFixed(6)
    }
    const parts = val.toString().split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return parts.join('.')
}

export const formatVOLThousands = (val: string | number) => {
    if (!getString(val)) {
        return val
    }
    if (typeof val === 'number') {
        val = val.toFixed(2)
    } else {
        val = parseFloat(val).toFixed(2)
    }
    const parts = val.toString().split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return parts.join('.')
}

/**
 * @description: 类型处理
 * @param {any} param1
 * @return {*}
 */
export const formatType = ({ c, placeholder = '', typeList = assetTypeReq }: any) => {
    const curArr = typeList.filter((item: any) => item.key === c)[0]
    return curArr?.label ? curArr.label : placeholder
}

/**
 * @description: 类型二级标题枚举
 * @param {any} param1
 * @return {*}
 */
export const formatSub = ({ c, placeholder = '', typeList = assetTypeReq }: any) => {
    const curArr = typeList.filter((item: any) => item.key === c)[0]
    return curArr?.subTitle ? curArr.subTitle : placeholder
}

/**
 * @description: 国内海外枚举
 * @param {any} param1
 * @return {*}
 */
export const formatIsOverseas = ({ c }: any) => (c === '1' ? '是' : '否')

/**
 * @description: 百分比/展示处理
 * @return {*}
 */
export const formatRatioPersent = ({
    val,
    amount,
    showCust = false,
    fixed = 2,
    sign = false,
    unit = '%'
}: any) => {
    const valTpl = Number(val)
    const amountTpl = Number(amount)
    if (amount !== undefined) {
        if (valTpl === 0 && amountTpl === 0) {
            return `0%`
        } else if (valTpl < 0.5 && amountTpl > 0) {
            return `<0.5%`
        } else if (valTpl === 100) {
            return `100%`
        } else if (valTpl > 99.5) {
            return `>99.5%`
        }
    }
    if (showCust) {
        if (Math.abs(valTpl) === 0) {
            return `0%`
        } else if (Math.abs(valTpl) < 0.5) {
            return valTpl > 0 ? `<0.5${unit}` : `>-0.5${unit}`
        } else if (Math.abs(valTpl) === 100) {
            return valTpl > 0 ? `100${unit}` : `-100${unit}`
        } else if (Math.abs(valTpl) > 99.5) {
            return valTpl > 0 ? `>99.5${unit}` : `<-99.5${unit}`
        }
    }
    return valTpl || valTpl === 0
        ? `${
              sign && valTpl > 0 ? '+' + valTpl.toFixed(fixed) + unit : valTpl.toFixed(fixed) + unit
          }`
        : '-'
}

/**
 * 添加单位
 * @param val
 * @param blankPlaceholder: 无数据时的展示字符，默认'-'
 */
// eslint-disable-next-line max-params
export const addUnit = ({
    val,
    fixed = 2,
    sign = false,
    unit = '',
    blankPlaceholder = '-'
}: {
    val: string | number
    fixed?: number
    sign?: boolean
    unit?: string
    blankPlaceholder?: string
}) => {
    if (!getString(val) || !val) {
        return blankPlaceholder
    }
    return Number(val) || Number(val) === 0 ? Number(val).toFixed(fixed) + unit : val + unit
}

/**
 * @description: 添加正负号
 * @param {string} val
 * @return {*}
 */
export const addPlusOrMinusSign = (val: any) => {
    if (!val || val === '0' || val === '0.00') {
        return val
    }
    return val > 0 ? '+' + val : val
}

/**
 * @description: 数据转换国内海外组合为100%
 * @param {*} dataTpl
 * @return {*}
 */
export const transHundredStr = (dataTpl: { ratio: number; data: number; value: string }[]) => {
    // 获取数据中较小值
    const { ratio, data, value } = dataTpl[0]
    const { ratio: ratio1, data: data1, value: value1 } = dataTpl[1]
    if (ratio >= ratio1) {
        if (ratio1 === 0 && data1 === 0) {
            return [`${value} ${ratio}%`, `${value1} ${ratio1}%`]
        } else if (ratio1 < 0.5 && data1 !== 0) {
            return [`${value} >${99.5}%`, `${value1} <${0.5}%`]
        }
        return [`${value} ${Number(ratio).toFixed(0)}%`, `${value1} ${Number(ratio1).toFixed(0)}%`]
    } else if (ratio === 0 && data === 0) {
        return [`${value} ${ratio}%`, `${value1} ${ratio1}%`]
    } else if (ratio < 0.5 && data !== 0) {
        return [`${value} <${0.5}%`, `${value1} >${99.5}%`]
    }
    return [`${value} ${Number(ratio).toFixed(0)}%`, `${value1} ${Number(ratio1).toFixed(0)}%`]
}

/**
 * 判断表格列是否显示
 * @param {*} columns
 * @param {*} key
 * @return {*}
 */
const getTableColumnState = (columns: any, key: string) => {
    return columns.some((item: any) => item.key === key)
}

/**
 * @description: table表格 类型
 * @return {*}
 */

// 给表格数据加'%'，空值显示'-'，传递formatter方法的时候使用
export const addPercentUnitForTableValue = (a: any, b: any, c: any) => addUnit({ val: c })
// 给表格数据加'%'，空值显示''，传递formatter方法的时候使用
export const addNullUnitTableVal = (a: any, b: any, c: any) =>
    addUnit({ val: c, blankPlaceholder: '' })
// 给表格数据进行日期格式化
export const formateDateForTableValue = (a: any, b: any, c: any) => (c ? datenewFormat(c) : '--')
// 处理表格数值型数据
export const formatNumForTableValue = (a: any, b: any, c: any) => c ?? '-'
// 表格数据的空值显示'-'，传递formatter方法的时候使用
export const formatTableValue = (a: any, b: any, c: any) => formatValue(c)
// 表格数据的千分位显示
export const formatThousandsForTableValue = (a: any, b: any, c: any) => formatThousands(c)
export const formatThousandsVOLForTableValue = (a: any, b: any, c: any) => formatVOLThousands(c)
export const formatThousandsAMTForTableValue = (a: any, b: any, c: any) => formatAMTThousands(c)
// table表格类型处理
export const tableType = (a: any, b: any, c: any) => formatType({ c, placeholder: '合计' })
// table 国内海外展示
export const tableIsOverseas = (a: any, b: any, c: any) => formatIsOverseas({ c })
export const tableIsBol = (a: any, b: any, c: string) => formatIsOverseas({ c })
// table会计计数法
export const tableFormatNum = (a: any, b: any, c: any) => formaAccountingNumber({ num: c })
