<!--
 * @Description: 分页组件
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-07 13:04:33
 * @FilePath: /crm-web/common/components/module/Pagination.vue
 *  
-->

<template>
    <div :class="`table-pagination ${custmClassName}`">
        <el-pagination
            v-model:current-page="current"
            small
            background
            layout="total, prev, pager, next, sizes"
            :page-size="page.size"
            :page-sizes="pageSizeList"
            :total="total"
            :page="page"
            popper-class="pagination-select-popper"
            @sizeChange="sizeChangeHandle"
            @currentChange="currentChangeHandle"
        >
        </el-pagination>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'Pagination',
        props: {
            total: {
                type: Number,
                default: 0
            },
            change: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            page: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            pageSizeList: {
                type: Array,
                default: () => {
                    return [10, 20, 30, 50, 100, 200, 500]
                }
            },
            custmClassName: {
                type: [String],
                default: ''
            }
        },
        emits: ['change'],
        data() {
            return {
                current: 1
            }
        },
        watch: {
            page: {
                handler: function (newVal) {
                    this.current = newVal.page
                },
                deep: true
            }
        },
        methods: {
            sizeChangeHandle(val) {
                this.$emit('change', this.current, val)
            },
            currentChangeHandle(val) {
                this.$emit('change', val, this.page.size)
            }
        }
    })
</script>

<style lang="less" scoped>
    .table-pagination {
        display: flex;
        justify-content: flex-end;
        padding: 10px 0;
        background-color: @font_color_01;

        &.add-pagiation-module {
            border-top: 1px solid @border_color_01;
        }
    }

    :deep(.el-pagination) {
        height: 26px;
        color: @font_color_05;

        .btn-next,
        .btn-prev,
        .el-pager li {
            color: @font_color_05;
            border-radius: 0;
        }

        &.is-background {
            .el-pager {
                li:not(.disabled) {
                    &.is-active {
                        background-color: @theme_main;
                    }
                }
            }
        }

        .el-pagination__sizes {
            padding-right: 10px;
            margin-left: 20px;
            color: inherit;

            .el-input {
                width: 80px;

                &.is-focus .el-input__inner {
                    border-color: @theme_main_hover;
                }

                .el-input__inner {
                    height: 22px;
                    padding-right: 20px;
                    line-height: 22px;
                    border-radius: 0;

                    &:hover,
                    &:focus {
                        border-color: @theme_main_hover;
                    }
                }
            }
        }

        .el-pagination__total,
        .el-input__inner {
            font-size: 12px;
            color: inherit;
        }
    }
</style>
