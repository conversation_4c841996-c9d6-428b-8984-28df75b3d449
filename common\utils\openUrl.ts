/*
 * @Author: xing.zhou
 * @Date: 2021-11-04 17:55:34
 * @LastEditTime: 2023-07-05 15:29:32
 * @LastEditors: chaohui.wu
 * @Description: 跳转链接、跳转路由统一方法
 */
// import globalConstant from '@/constant' // 全局常量

const isDev = process.env.NODE_ENV === 'development'

/**
 * @description: 打开新窗口或标签页统一方法
 * @param {type} 打开类型 1、2、3
 * @param {path} 打开页面路径
 * @param {appName} 打开的子应用名称
 * @param {callback} 非子应用回调方法，比如在base页有路由配置就不用跳转子应用，使用callback
 * @params {vueVersion} '2' | '3' vue版本，默认3
 */
const openUrl = ({ type, path }: any) => {
    const { origin, port, pathname } = window.location
    if (type === 1) {
        const location = `${origin}${pathname}#${path}`
        window.open(location) // 投研系统新窗口打开location
    } else if (type === 2) {
        // 子应用间跳转(开发环境 & 生产环境)
        const location = isDev ? `http://localhost:${port}/#${path}` : `${origin}/#${path}`
        window.open(location)
    } else if (type === 3) {
        window.open(path) // 完整路径直接跳转
    }
}

export { openUrl }
