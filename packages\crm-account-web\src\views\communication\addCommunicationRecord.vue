<!--
 * @Description: 沟通记录页面
 * @Author: ai
 * @Date: 2024-03-21
 * @LastEditors: ai
 * @LastEditTime: 2024-03-21
-->
<template>
    <div class="communication-record">
        <!-- 添加沟通记录模块 -->
        <div class="section-block">
            <div class="section-title"><strong>添加沟通记录</strong></div>
            <div class="section-content">
                <el-form
                    ref="communicationFormRef"
                    :model="communicationForm"
                    :rules="rules"
                    label-width="120px"
                    label-position="left"
                >
                    <el-form-item
                        label="沟通方式："
                        prop="visittype"
                        required
                        class="el-form-item--with-radio"
                    >
                        <el-radio-group v-model="communicationForm.visittype">
                            <el-radio label="2">见面</el-radio>
                            <el-radio label="9">线上会议</el-radio>
                            <el-radio label="1">电话</el-radio>
                            <el-radio label="7">企业微信</el-radio>
                            <el-radio label="6">微信</el-radio>
                            <el-radio label="3">参会</el-radio>
                            <el-radio label="4">短信</el-radio>
                            <el-radio label="5">邮件</el-radio>
                            <el-radio label="8">快递</el-radio>
                            <el-radio label="10">其他</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="沟通摘要："
                        prop="commcontent"
                        required
                        class="el-form-item--with-textarea"
                    >
                        <el-input
                            v-model="communicationForm.commcontent"
                            type="textarea"
                            :rows="4"
                            :maxlength="300"
                            show-word-limit
                            placeholder="请输入沟通摘要"
                        />
                    </el-form-item>
                    <el-form-item label="拜访日期：" prop="visitDate" required>
                        <el-date-picker
                            v-model="communicationForm.visitDate"
                            type="date"
                            placeholder="选择日期"
                            :disabled-date="disableFutureDate"
                        />
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 拜访纪要模块（可折叠） -->
        <el-collapse v-model="activeCollapse">
            <el-collapse-item name="visitSummary">
                <template #title>
                    <div class="section-title"><strong>拜访纪要</strong></div>
                </template>
                <div class="section-content">
                    <el-form :model="visitForm" label-width="120px" label-position="left">
                        <el-row :gutter="20">
                            <el-col :span="10">
                                <el-form-item label="客户目前存量：">
                                    <span>{{ visitForm.currentAmount }}</span>
                                    <span style="color: #999999">（同IPS报告存量口径）</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="13">
                                <el-form-item
                                    label="客户目前综合评级（1-5星）："
                                    class="rating-item"
                                >
                                    <span>{{ visitForm.healthAvgStar }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item
                            label="拜访目的："
                            required
                            class="el-form-item--with-checkbox"
                        >
                            <div class="purpose-group">
                                <el-checkbox-group v-model="visitForm.purpose">
                                    <el-checkbox label="1">初次见面</el-checkbox>
                                    <el-checkbox label="2">产品服务</el-checkbox>
                                    <el-checkbox label="3">IPS陪访</el-checkbox>
                                    <el-checkbox label="4">创新陪访</el-checkbox>
                                    <el-checkbox label="5">Leads及继承客户拜访</el-checkbox>
                                    <el-checkbox label="6">其他</el-checkbox>
                                </el-checkbox-group>
                                <el-input
                                    v-if="visitForm.purpose.includes('6')"
                                    v-model="visitForm.otherPurpose"
                                    placeholder="请输入其他拜访目的"
                                    style="width: 200px; margin-left: 10px"
                                />
                            </div>
                        </el-form-item>
                        <el-form-item v-if="visitForm.purpose.includes('3')" class="ips-report">
                            <el-button type="primary" @click="handleSelectReport"
                                >选择报告</el-button
                            >
                            <span class="report-status">
                                <div v-if="selectedReport" class="selected-report">
                                    <el-link type="primary" @click="handleViewIpsReport">{{
                                        selectedReport.reportName
                                    }}</el-link>
                                    <el-icon class="cancel-icon" @click="handleCancelReport">
                                        <Close />
                                    </el-icon>
                                </div>
                                <template v-else>
                                    未选择报告
                                    <span class="report-tip">（请选择拜访时提供的IPS报告）</span>
                                </template>
                            </span>
                        </el-form-item>
                        <el-form-item label="提供资料：" required>
                            <el-input
                                v-model="visitForm.materials"
                                placeholder="请输入拜访时提供的资料"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item label="陪访人：" class="el-form-item--with-checkbox">
                            <div class="accompany-persons">
                                <div class="accompany-person">
                                    <el-checkbox
                                        v-model="visitForm.accompanyPersons.projectManager"
                                        @change="handleAccompanyPersonChange('projectManager')"
                                        >项目经理</el-checkbox
                                    >
                                    <el-select
                                        v-model="visitForm.accompanyPersons.projectManagerName"
                                        placeholder="请选择"
                                        :disabled="!visitForm.accompanyPersons.projectManager"
                                        style="width: 600px"
                                        multiple
                                        filterable
                                        collapse-tags
                                        :reserve-keyword="false"
                                        @remove-tag="handleRemoveTag('projectManager', $event)"
                                    >
                                        <el-option
                                            v-for="item in projectManagerUserList"
                                            :key="item.name"
                                            :label="`${item.name}（${item.code}）`"
                                            :value="item.code"
                                        />
                                    </el-select>
                                </div>
                                <div class="accompany-person">
                                    <el-checkbox
                                        v-model="visitForm.accompanyPersons.manager"
                                        @change="handleAccompanyPersonChange('manager')"
                                        >主管（分总/区副/区总）</el-checkbox
                                    >
                                    <el-select
                                        v-model="visitForm.accompanyPersons.managerName"
                                        placeholder="请选择"
                                        :disabled="!visitForm.accompanyPersons.manager"
                                        style="width: 600px"
                                        multiple
                                        filterable
                                        collapse-tags
                                        :reserve-keyword="false"
                                        @remove-tag="handleRemoveTag('manager', $event)"
                                    >
                                        <el-option
                                            v-for="item in manageUserList"
                                            :key="item.name"
                                            :label="`${item.name}（${item.code}）`"
                                            :value="item.code"
                                        />
                                    </el-select>
                                </div>
                                <div class="accompany-person">
                                    <el-checkbox
                                        v-model="visitForm.accompanyPersons.businessManager"
                                        @change="handleAccompanyPersonChange('businessManager')"
                                        >总部业资</el-checkbox
                                    >
                                    <el-select
                                        v-model="visitForm.accompanyPersons.businessManagerName"
                                        placeholder="请选择"
                                        :disabled="!visitForm.accompanyPersons.businessManager"
                                        style="width: 600px"
                                        multiple
                                        filterable
                                        collapse-tags
                                        :reserve-keyword="false"
                                        @remove-tag="handleRemoveTag('businessManager', $event)"
                                    >
                                        <el-option
                                            v-for="item in supervisorUserList"
                                            :key="item.name"
                                            :label="`${item.name}（${item.code}）`"
                                            :value="item.code"
                                        />
                                    </el-select>
                                </div>
                                <div class="accompany-person">
                                    <el-checkbox
                                        v-model="visitForm.accompanyPersons.other"
                                        @change="handleAccompanyPersonChange('other')"
                                        >其他</el-checkbox
                                    >
                                    <el-select
                                        v-model="visitForm.accompanyPersons.otherName"
                                        placeholder="请选择"
                                        :disabled="!visitForm.accompanyPersons.other"
                                        style="width: 600px"
                                        multiple
                                        filterable
                                        collapse-tags
                                        :reserve-keyword="false"
                                        @remove-tag="handleRemoveTag('other', $event)"
                                    >
                                        <el-option
                                            v-for="item in otherUserList"
                                            :key="item.name"
                                            :label="`${item.name}（${item.code}）`"
                                            :value="item.code"
                                        />
                                    </el-select>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="客户参与人员及其角色：" class="no-wrap">
                            <el-input v-model="visitForm.customerParticipants" />
                        </el-form-item>
                        <el-form-item class="el-form-item--feedback">
                            <template #label>
                                <div class="multi-line-label">
                                    <div>对产品或服务的回馈反馈：<br />（创新陪访必填）</div>
                                </div>
                            </template>
                            <el-input
                                v-model="visitForm.productFeedback"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item class="el-form-item--feedback">
                            <template #label>
                                <div class="multi-line-label">
                                    <div>对于IPS报告反馈：<br />（IPS陪访必填）</div>
                                </div>
                            </template>
                            <el-input
                                v-model="visitForm.ipsFeedback"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item class="el-form-item--multi-line el-form-item--with-checkbox">
                            <template #label>
                                <div class="multi-line-label">
                                    <div>近期可用于加仓的金额：<br />（IPS陪访必填）</div>
                                </div>
                            </template>
                            <div class="amount-inputs">
                                <el-checkbox
                                    v-model="visitForm.amount.rmb"
                                    @change="handleAmountTypeChange('rmb')"
                                    >人民币</el-checkbox
                                >
                                <el-input
                                    v-model="visitForm.amount.rmbValue"
                                    style="width: 200px"
                                    :disabled="!visitForm.amount.rmb"
                                    @input="handleAmountInput('rmbValue', $event)"
                                >
                                    <template #append>万</template>
                                </el-input>
                                <el-checkbox
                                    v-model="visitForm.amount.foreign"
                                    @change="handleAmountTypeChange('foreign')"
                                    >外币</el-checkbox
                                >
                                <el-input
                                    v-model="visitForm.amount.foreignValue"
                                    style="width: 200px"
                                    :disabled="!visitForm.amount.foreign"
                                    @input="handleAmountInput('foreignValue', $event)"
                                >
                                    <template #append>万</template>
                                </el-input>
                            </div>
                        </el-form-item>
                        <el-form-item class="el-form-item--multi-line">
                            <template #label>
                                <div class="multi-line-label">
                                    <div>近期关注的资产类别或具体产品：<br />（IPS陪访必填）</div>
                                </div>
                            </template>
                            <el-input
                                v-model="visitForm.interestedProducts"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item class="el-form-item--multi-line">
                            <template #label>
                                <div class="multi-line-label">
                                    <div>
                                        评估客户对创新业务、家族信托、<br />身份、法税的需求：
                                    </div>
                                </div>
                            </template>
                            <el-input
                                v-model="visitForm.customerNeeds"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item class="el-form-item--multi-line" required>
                            <template #label>
                                <div class="multi-line-label">
                                    <div>下一步工作计划：</div>
                                </div>
                            </template>
                            <el-input
                                v-model="visitForm.nextPlan"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </el-collapse-item>

            <!-- 新增提醒模块（可折叠） -->
            <el-collapse-item name="newReminder">
                <template #title>
                    <div class="section-title"><strong>新增提醒</strong></div>
                </template>
                <div class="section-content">
                    <el-form :model="reminderForm" label-width="120px" label-position="left">
                        <el-form-item label="沟通方式：" class="el-form-item--with-radio">
                            <div class="radio-group-with-clear">
                                <el-radio-group v-model="reminderForm.nextvisittype">
                                    <el-radio label="2">见面</el-radio>
                                    <el-radio label="9">线上会议</el-radio>
                                    <el-radio label="1">电话</el-radio>
                                    <el-radio label="7">企业微信</el-radio>
                                    <el-radio label="6">微信</el-radio>
                                    <el-radio label="3">参会</el-radio>
                                    <el-radio label="4">短信</el-radio>
                                    <el-radio label="5">邮件</el-radio>
                                    <el-radio label="8">快递</el-radio>
                                    <el-radio label="10">其他</el-radio>
                                </el-radio-group>
                                <el-button
                                    v-if="reminderForm.nextvisittype"
                                    type="text"
                                    class="clear-button"
                                    @click="clearCommunicationType"
                                >
                                    <el-icon><Close /></el-icon>
                                </el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="预约日期：">
                            <div class="date-time-picker">
                                <el-date-picker
                                    v-model="reminderForm.nextdt"
                                    type="date"
                                    placeholder="选择日期"
                                />
                                <el-time-picker
                                    v-model="reminderForm.timeRange[0]"
                                    placeholder="开始时间"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    @change="validateTimeRange"
                                />
                                <span class="separator">至</span>
                                <el-time-picker
                                    v-model="reminderForm.timeRange[1]"
                                    placeholder="结束时间"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    @change="validateTimeRange"
                                />
                            </div>
                        </el-form-item>
                        <el-form-item label="预约事项：">
                            <el-input
                                v-model="reminderForm.nextvisitcontent"
                                type="textarea"
                                :rows="4"
                                :maxlength="300"
                                show-word-limit
                                placeholder="请输入预约事项"
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </el-collapse-item>
        </el-collapse>

        <div class="form-actions">
            <crm-button type="primary" :radius="true" @click="handleSubmit">保存</crm-button>
            <crm-button :radius="true" plain @click="handleCancel">取消</crm-button>
        </div>
    </div>

    <!-- 添加IPS报告选择弹窗 -->
    <IpsReportDialog ref="ipsDialogRef" @select="handleReportSelect" />
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue'
    import type { Ref } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { Close } from '@element-plus/icons-vue'
    import type { FormInstance, FormRules } from 'element-plus'
    import { useRoute } from 'vue-router'
    import { getVisitInit, getHealth, searchUser, addCommunicate } from '@/api/project/communicate'
    import type { QueryHealthReq, AddCommunicateReq } from '@/api/project/communicate/type/apiType'
    import { fetchRes, message, dateTrans } from '@common/utils'
    import IpsReportDialog from '@/components/IpsReportDialog.vue'

    const route = useRoute()
    const consCustNo = route.query.consCustNo as string

    // 禁用未来日期的函数
    const disableFutureDate = (time: Date) => {
        return time > new Date()
    }

    // 添加用户列表接口
    interface User {
        code: string
        name: string
    }

    // 用户列表数据
    const manageUserList = ref<User[]>([])
    const otherUserList = ref<User[]>([])
    const supervisorUserList = ref<User[]>([])
    const projectManagerUserList = ref<User[]>([]) // 项目经理用户列表

    // 初始化数据
    const initData = async () => {
        console.log('consCustNo', consCustNo)

        fetchRes(getVisitInit(), {
            successCB: (res: any) => {
                // 成功初始化
                if (res) {
                    console.log('res', res)
                    manageUserList.value = res.manageUserList || []
                    otherUserList.value = res.otherUserList || []
                    supervisorUserList.value = res.supervisorUserList || []
                    projectManagerUserList.value = res.projectManagerUserList || []
                }
            },
            successTxt: '',
            failTxt: '获取初始化信息失败！',
            fetchKey: ''
        })
    }

    // 页面加载时调用初始化方法
    initData()

    const activeCollapse: Ref<string[]> = ref(['newReminder'])
    const communicationFormRef = ref<FormInstance>()

    // 表单验证规则
    const rules = ref<FormRules>({
        visittype: [{ required: true, message: '请选择沟通方式', trigger: 'change' }],
        commcontent: [{ required: true, message: '请输入沟通摘要', trigger: 'blur' }],
        visitDate: [{ required: true, message: '请选择拜访日期', trigger: 'change' }]
    })

    // 沟通记录表单数据
    const communicationForm = ref({
        visittype: '',
        commcontent: '',
        visitDate: ''
    })

    // 修改表单数据类型
    interface AccompanyPersons {
        projectManager: boolean
        projectManagerName: string[]
        manager: boolean
        managerName: string[]
        businessManager: boolean
        businessManagerName: string[]
        other: boolean
        otherName: string[]
    }

    // 陪访人信息接口定义
    interface AccompanyingReq {
        /**
         * 陪访人类型(1-项目经理 2-主管 3-总部业资 4-其他)
         */
        accompanyingType: string
        /**
         * 陪访人用户ID
         */
        accompanyingUserId: string
    }

    interface VisitForm {
        currentAmount: string
        healthAvgStar: string
        purpose: string[]
        otherPurpose: string
        materials: string
        accompanyPersons: AccompanyPersons
        customerParticipants: string
        productFeedback: string
        ipsFeedback: string
        amount: {
            rmb: boolean
            rmbValue: string
            foreign: boolean
            foreignValue: string
        }
        interestedProducts: string
        customerNeeds: string
        nextPlan: string
    }

    // 拜访纪要表单数据
    const visitForm = ref<VisitForm>({
        currentAmount: '',
        healthAvgStar: '',
        purpose: [],
        otherPurpose: '',
        materials: '',
        accompanyPersons: {
            projectManager: false,
            projectManagerName: [],
            manager: false,
            managerName: [],
            businessManager: false,
            businessManagerName: [],
            other: false,
            otherName: []
        },
        customerParticipants: '',
        productFeedback: '',
        ipsFeedback: '',
        amount: {
            rmb: false,
            rmbValue: '',
            foreign: false,
            foreignValue: ''
        },
        interestedProducts: '',
        customerNeeds: '',
        nextPlan: ''
    })

    // 监听拜访目的变化，当取消IPS陪访时清空已选报告
    watch(
        () => visitForm.value.purpose,
        newPurpose => {
            if (!newPurpose.includes('3')) {
                selectedReport.value = null
            }
        }
    )

    // 监听拜访日期变化
    const handleVisitDateChange = async (date: Date) => {
        if (!date || !consCustNo) {
            return
        }

        const params: QueryHealthReq = {
            consCustNo,
            visitDt: dateTrans(date, 'YYYYMMDD') // 使用dateTrans将日期格式化为YYYYMMDD
        }

        fetchRes(getHealth(params), {
            successCB: (res: any) => {
                if (res) {
                    visitForm.value.currentAmount = res.marketVal ? res.marketVal : '--'
                    visitForm.value.healthAvgStar = res.healthAvgStar
                        ? res.healthAvgStar + '星'
                        : '--'
                }
            },
            successTxt: '',
            failTxt: '获取客户健康情况失败！',
            fetchKey: ''
        })
    }

    // 监听visitDate变化
    watch(
        () => communicationForm.value.visitDate,
        newDate => {
            if (newDate) {
                handleVisitDateChange(new Date(newDate))
            }
        }
    )

    // 新增提醒表单数据
    interface ReminderForm {
        nextvisittype: string
        nextdt: string | Date
        timeRange: [string, string]
        nextvisitcontent: string
    }

    const reminderForm = ref<ReminderForm>({
        nextvisittype: '',
        nextdt: '',
        timeRange: ['', ''],
        nextvisitcontent: ''
    })

    // 查看IPS报告
    const handleViewIpsReport = () => {
        window.open(
            window._msCrmAssetWebPrefix +
                `/index.html#/assetPreview?conscustno=${consCustNo}&assetId=${selectedReport.value?.reportId}`,
            '_blank'
        )
    }

    // 处理陪访人选择变化
    const handleAccompanyPersonChange = (
        type: keyof Pick<
            AccompanyPersons,
            'projectManager' | 'manager' | 'businessManager' | 'other'
        >
    ) => {
        if (!visitForm.value.accompanyPersons[type]) {
            switch (type) {
                case 'projectManager':
                    visitForm.value.accompanyPersons.projectManagerName = []
                    break
                case 'manager':
                    visitForm.value.accompanyPersons.managerName = []
                    break
                case 'businessManager':
                    visitForm.value.accompanyPersons.businessManagerName = []
                    break
                case 'other':
                    visitForm.value.accompanyPersons.otherName = []
                    break
            }
        }
    }

    // 处理移除标签
    const handleRemoveTag = (
        type: 'projectManager' | 'manager' | 'businessManager' | 'other',
        tag: string
    ) => {
        switch (type) {
            case 'projectManager':
                visitForm.value.accompanyPersons.projectManagerName =
                    visitForm.value.accompanyPersons.projectManagerName.filter(name => name !== tag)
                break
            case 'manager':
                visitForm.value.accompanyPersons.managerName =
                    visitForm.value.accompanyPersons.managerName.filter(name => name !== tag)
                break
            case 'businessManager':
                visitForm.value.accompanyPersons.businessManagerName =
                    visitForm.value.accompanyPersons.businessManagerName.filter(
                        name => name !== tag
                    )
                break
            case 'other':
                visitForm.value.accompanyPersons.otherName =
                    visitForm.value.accompanyPersons.otherName.filter(name => name !== tag)
                break
        }
    }

    // 处理金额类型变化
    const handleAmountTypeChange = (type: 'rmb' | 'foreign') => {
        if (!visitForm.value.amount[type]) {
            // 如果取消选中，清空对应的输入框
            visitForm.value.amount[`${type}Value`] = ''
        }
    }

    // 处理金额输入，只允许输入数字
    const handleAmountInput = (field: 'rmbValue' | 'foreignValue', value: string) => {
        // 只保留数字和小数点
        const newValue = value.replace(/[^\d.]/g, '')
        // 确保只有一个小数点
        const parts = newValue.split('.')
        if (parts.length > 2) {
            visitForm.value.amount[field] = parts[0] + '.' + parts.slice(1).join('')
        } else {
            visitForm.value.amount[field] = newValue
        }
        // 如果是以小数点结尾，允许继续输入
        if (value.endsWith('.')) {
            return
        }
        // 如果有小数点，限制小数位数为2位
        if (parts.length === 2 && parts[1].length > 2) {
            visitForm.value.amount[field] = Number(newValue).toFixed(2)
        }
    }

    // 校验时间范围
    const validateTimeRange = () => {
        const [startTime, endTime] = reminderForm.value.timeRange

        // 如果开始时间和结束时间都已选择
        if (startTime && endTime) {
            // 将时间转换为分钟进行比较
            const [startHour, startMinute] = startTime.split(':').map(Number)
            const [endHour, endMinute] = endTime.split(':').map(Number)

            const startMinutes = startHour * 60 + startMinute
            const endMinutes = endHour * 60 + endMinute

            if (startMinutes >= endMinutes) {
                ElMessage.warning('开始时间必须小于结束时间')
                // 清空结束时间
                reminderForm.value.timeRange[1] = ''
            }
        }
    }

    // 提交表单
    const handleSubmit = async () => {
        if (!communicationFormRef.value) {
            return
        }

        // 【添加沟通记录】校验
        // 1. 验证沟通方式
        if (!communicationForm.value.visittype) {
            ElMessage.error('沟通方式不能为空！')
            return
        }

        // 2. 验证沟通摘要
        if (!communicationForm.value.commcontent) {
            ElMessage.error('沟通摘要不能为空！')
            return
        }

        // 3. 验证拜访日期
        if (!communicationForm.value.visitDate) {
            ElMessage.error('拜访日期不能为空！')
            return
        }

        // 检查拜访日期是否小于等于当前日期
        const visitDate = new Date(communicationForm.value.visitDate)
        const currentDate = new Date()
        // 重置时间部分以便只比较日期
        currentDate.setHours(0, 0, 0, 0)
        visitDate.setHours(0, 0, 0, 0)

        if (visitDate > currentDate) {
            ElMessage.error('拜访日期不能超过今天')
            return
        }

        // 【新增提醒】校验
        // 判断是否有预约信息
        const hasAppointmentInfo =
            reminderForm.value.nextvisittype ||
            reminderForm.value.nextdt ||
            reminderForm.value.timeRange[0] ||
            reminderForm.value.timeRange[1] ||
            reminderForm.value.nextvisitcontent

        if (hasAppointmentInfo) {
            // 预约日期校验
            if (!reminderForm.value.nextdt) {
                ElMessage.error('预约日期字段不能为空！')
                return
            }

            // 预约方式校验
            if (!reminderForm.value.nextvisittype) {
                ElMessage.error('沟通方式字段不能为空！')
                return
            }

            // 预约事项校验
            if (!reminderForm.value.nextvisitcontent) {
                ElMessage.error('预约事项字段不能为空！')
                return
            }

            // 面见预约的时间校验
            if (reminderForm.value.nextvisittype === '2') {
                // 见面
                if (!reminderForm.value.timeRange[0] || !reminderForm.value.timeRange[1]) {
                    ElMessage.error('沟通方式为见面时，预约日期和时间为必选！')
                    return
                }

                // 判断开始时间是否大于结束时间
                if (reminderForm.value.timeRange[0] && reminderForm.value.timeRange[1]) {
                    const startTime = reminderForm.value.timeRange[0]
                    const endTime = reminderForm.value.timeRange[1]

                    // 将时间转换为分钟进行比较
                    const [startHour, startMinute] = startTime.split(':').map(Number)
                    const [endHour, endMinute] = endTime.split(':').map(Number)

                    const startMinutes = startHour * 60 + startMinute
                    const endMinutes = endHour * 60 + endMinute

                    if (startMinutes >= endMinutes) {
                        ElMessage.error('开始时间不能大于结束时间！')
                        return
                    }
                }
            }
        }

        // 判断【拜访纪要】模块是否有填写内容
        const hasVisitSummaryInfo =
            visitForm.value.purpose.length > 0 ||
            visitForm.value.otherPurpose ||
            visitForm.value.materials ||
            visitForm.value.customerParticipants ||
            visitForm.value.productFeedback ||
            visitForm.value.ipsFeedback ||
            visitForm.value.interestedProducts ||
            visitForm.value.customerNeeds ||
            visitForm.value.nextPlan ||
            visitForm.value.accompanyPersons.projectManager ||
            visitForm.value.accompanyPersons.manager ||
            visitForm.value.accompanyPersons.businessManager ||
            visitForm.value.accompanyPersons.other ||
            visitForm.value.amount.rmb ||
            visitForm.value.amount.foreign

        if (hasVisitSummaryInfo) {
            // 【拜访纪要】模块校验
            // 校验1：检查必填项是否已填写
            const missingFields = []

            // 检查拜访目的（必填项）
            if (visitForm.value.purpose.length === 0) {
                missingFields.push('拜访目的')
            }

            // 检查提供资料（必填项）
            if (!visitForm.value.materials) {
                missingFields.push('提供资料')
            }

            // 检查下一步工作计划（必填项）
            if (!visitForm.value.nextPlan) {
                missingFields.push('下一步工作计划')
            }

            // 检查IPS陪访特有必填项
            if (visitForm.value.purpose.includes('3')) {
                if (!visitForm.value.ipsFeedback) {
                    missingFields.push('对于IPS报告反馈（IPS陪访必填）')
                }

                // 检查金额：要求至少填写一种货币类型的金额
                if (
                    (!visitForm.value.amount.rmb || !visitForm.value.amount.rmbValue) &&
                    (!visitForm.value.amount.foreign || !visitForm.value.amount.foreignValue)
                ) {
                    missingFields.push('近期可用于加仓的金额（IPS陪访必填）')
                }

                if (!visitForm.value.interestedProducts) {
                    missingFields.push('近期关注的资产类别或具体产品（IPS陪访必填）')
                }
            }

            // 单独检查：如果勾选了金额复选框，对应的金额值必填
            if (
                (visitForm.value.amount.rmb && !visitForm.value.amount.rmbValue) ||
                (visitForm.value.amount.foreign && !visitForm.value.amount.foreignValue)
            ) {
                if (missingFields.indexOf('近期可用于加仓的金额（IPS陪访必填）') === -1) {
                    missingFields.push('近期可用于加仓的金额（已勾选复选框但未填写金额）')
                }
            }

            // 检查创新陪访特有必填项
            if (visitForm.value.purpose.includes('4')) {
                if (!visitForm.value.productFeedback) {
                    missingFields.push('对产品或服务的回馈反馈（创新陪访必填）')
                }
            }

            // 拜访目的包含"其他"时，需校验其他原因
            if (visitForm.value.purpose.includes('6')) {
                if (!visitForm.value.otherPurpose) {
                    missingFields.push('其他拜访目的')
                }
            }

            if (missingFields.length > 0) {
                // 存在必填项未填写，弹窗提示
                const missingFieldsText = missingFields.join('、')

                // 使用 element-plus 的 MessageBox 实现确认弹窗
                ElMessageBox.alert(
                    `<strong>请将拜访纪要内容填写完整，再提交保存。</strong><br/><br/>拜访纪要还需要填下以下内容：${missingFieldsText}`,
                    {
                        title: '提示',
                        confirmButtonText: '继续提交，不保存拜访纪要',
                        cancelButtonText: '好的',
                        dangerouslyUseHTMLString: true,
                        showCancelButton: true,
                        callback: (action: string) => {
                            if (action === 'confirm') {
                                // 用户选择了"继续提交，不保存拜访纪要"
                                // 调用保存逻辑，但只保存【添加沟通记录】和【新增提醒】部分
                                saveWithoutVisitSummary()
                            }
                        }
                    }
                )
                return
            }

            // 校验3：如果拜访目的包含IPS陪访，检查是否选择了IPS报告
            if (visitForm.value.purpose.includes('3') && !selectedReport.value) {
                ElMessage.error('请选择IPS报告')
                return
            }

            // 必填项都已填写，IPS报告也已选择（如果需要），显示最终确认弹窗
            ElMessageBox.confirm(
                '拜访纪要提交后，已填写内容不可修改<br/>未填写内容3个自然日内，陪访人/上级主管未填写反馈可修改<br/><strong style="color: red;">是否继续提交？</strong>',
                '提示',
                {
                    confirmButtonText: '继续提交',
                    cancelButtonText: '取消',
                    dangerouslyUseHTMLString: true,
                    type: 'warning'
                }
            )
                .then(() => {
                    // 用户确认继续提交
                    saveAllData()
                })
                .catch(() => {
                    // 用户取消
                })
            return
        }

        // 如果没有填写拜访纪要，或者用户选择了只保存沟通记录和提醒，则直接保存
        saveAllData()
    }

    // 只保存沟通记录和提醒（不保存拜访纪要）
    const saveWithoutVisitSummary = async () => {
        try {
            await communicationFormRef.value?.validate()

            // 构建请求对象
            const params: Partial<AddCommunicateReq> = {
                // 沟通记录信息
                communicateReq: {
                    consCustNo: consCustNo,
                    visittype: communicationForm.value.visittype,
                    commcontent: communicationForm.value.commcontent,
                    visitDate: dateTrans(communicationForm.value.visitDate, 'YYYYMMDD') // 转换为YYYYMMDD格式
                }
            }

            // 添加预约信息（如果有）
            if (
                reminderForm.value.nextvisittype ||
                reminderForm.value.nextdt ||
                reminderForm.value.timeRange[0] ||
                reminderForm.value.timeRange[1] ||
                reminderForm.value.nextvisitcontent
            ) {
                params.bookingReq = {
                    nextvisittype: reminderForm.value.nextvisittype || '',
                    nextdt: reminderForm.value.nextdt
                        ? dateTrans(reminderForm.value.nextdt, 'YYYYMMDD')
                        : '',
                    nextvisitcontent: reminderForm.value.nextvisitcontent || '',
                    nextstarttime: reminderForm.value.timeRange[0] || '',
                    nextendtime: reminderForm.value.timeRange[1] || ''
                }
            }

            // 调用接口保存数据
            fetchRes(addCommunicate(params as AddCommunicateReq), {
                successCB: (res: any) => {
                    ElMessage.success('保存成功')
                    iframeTrans({ eventName: 'saveVisit' })
                },
                successTxt: '',
                failTxt: '保存失败',
                fetchKey: 'addCommunicate'
            })
        } catch (error) {
            ElMessage.error('请填写必填项')
            return false
        }
    }

    // 保存所有数据
    const saveAllData = async () => {
        try {
            await communicationFormRef.value?.validate()

            // 判断是否有拜访纪要信息
            const hasVisitSummaryInfo =
                visitForm.value.purpose.length > 0 ||
                visitForm.value.otherPurpose ||
                visitForm.value.materials ||
                visitForm.value.customerParticipants ||
                visitForm.value.productFeedback ||
                visitForm.value.ipsFeedback ||
                visitForm.value.interestedProducts ||
                visitForm.value.customerNeeds ||
                visitForm.value.nextPlan ||
                visitForm.value.accompanyPersons.projectManager ||
                visitForm.value.accompanyPersons.manager ||
                visitForm.value.accompanyPersons.businessManager ||
                visitForm.value.accompanyPersons.other ||
                visitForm.value.amount.rmb ||
                visitForm.value.amount.foreign

            // 构建请求对象
            const params: AddCommunicateReq = {
                // 沟通记录基本信息
                communicateReq: {
                    consCustNo: consCustNo,
                    visittype: communicationForm.value.visittype,
                    commcontent: communicationForm.value.commcontent,
                    visitDate: dateTrans(communicationForm.value.visitDate, 'YYYYMMDD') // 转换为YYYYMMDD格式
                }
            }

            // 添加预约信息（如果有）
            if (
                reminderForm.value.nextvisittype ||
                reminderForm.value.nextdt ||
                reminderForm.value.timeRange[0] ||
                reminderForm.value.timeRange[1] ||
                reminderForm.value.nextvisitcontent
            ) {
                params.bookingReq = {
                    nextvisittype: reminderForm.value.nextvisittype || '',
                    nextdt: reminderForm.value.nextdt
                        ? dateTrans(reminderForm.value.nextdt, 'YYYYMMDD')
                        : '',
                    nextvisitcontent: reminderForm.value.nextvisitcontent || '',
                    nextstarttime: reminderForm.value.timeRange[0] || '',
                    nextendtime: reminderForm.value.timeRange[1] || ''
                }
            }

            // 添加拜访纪要信息（如果有）
            if (hasVisitSummaryInfo) {
                // 构建陪访人列表
                const accompanyingList: AccompanyingReq[] = []

                // 添加项目经理陪访人
                if (
                    visitForm.value.accompanyPersons.projectManager &&
                    visitForm.value.accompanyPersons.projectManagerName.length > 0
                ) {
                    visitForm.value.accompanyPersons.projectManagerName.forEach(pmCode => {
                        accompanyingList.push({
                            accompanyingType: '1', // 项目经理类型
                            accompanyingUserId: pmCode
                        })
                    })
                }

                // 添加主管陪访人
                if (
                    visitForm.value.accompanyPersons.manager &&
                    visitForm.value.accompanyPersons.managerName.length > 0
                ) {
                    visitForm.value.accompanyPersons.managerName.forEach(mCode => {
                        accompanyingList.push({
                            accompanyingType: '2', // 主管类型
                            accompanyingUserId: mCode
                        })
                    })
                }

                // 添加总部业资陪访人
                if (
                    visitForm.value.accompanyPersons.businessManager &&
                    visitForm.value.accompanyPersons.businessManagerName.length > 0
                ) {
                    visitForm.value.accompanyPersons.businessManagerName.forEach(bmCode => {
                        accompanyingList.push({
                            accompanyingType: '3', // 总部业资类型
                            accompanyingUserId: bmCode
                        })
                    })
                }

                // 添加其他陪访人
                if (
                    visitForm.value.accompanyPersons.other &&
                    visitForm.value.accompanyPersons.otherName.length > 0
                ) {
                    visitForm.value.accompanyPersons.otherName.forEach(oCode => {
                        accompanyingList.push({
                            accompanyingType: '4', // 其他类型
                            accompanyingUserId: oCode
                        })
                    })
                }

                // 构建拜访纪要对象
                params.visitMinutesReq = {
                    marketVal:
                        visitForm.value.currentAmount === '--' ? '' : visitForm.value.currentAmount,
                    healthAvgStar:
                        visitForm.value.healthAvgStar === '--' ? '' : visitForm.value.healthAvgStar,
                    visitPurpose: visitForm.value.purpose.join(','),
                    visitPurposeOther: visitForm.value.otherPurpose || undefined,
                    assetReportId: selectedReport.value?.reportId,
                    giveInformation: visitForm.value.materials,
                    attendRole: visitForm.value.customerParticipants || undefined,
                    productServiceFeedback: visitForm.value.productFeedback || undefined,
                    ipsFeedback: visitForm.value.ipsFeedback || undefined,
                    addAmountRmb: visitForm.value.amount.rmb
                        ? visitForm.value.amount.rmbValue
                        : undefined,
                    addAmountForeign: visitForm.value.amount.foreign
                        ? visitForm.value.amount.foreignValue
                        : undefined,
                    focusAsset: visitForm.value.interestedProducts || undefined,
                    estimateNeedBusiness: visitForm.value.customerNeeds || undefined,
                    nextPlan: visitForm.value.nextPlan,
                    accompanyingList: accompanyingList.length > 0 ? accompanyingList : undefined
                }
            }

            // 调用接口保存数据
            fetchRes(addCommunicate(params), {
                successCB: (res: any) => {
                    ElMessage.success('保存成功')
                    iframeTrans({ eventName: 'saveVisit' })
                },
                successTxt: '',
                failTxt: '保存失败',
                fetchKey: 'addCommunicate'
            })
        } catch (error) {
            ElMessage.error('请填写必填项')
            return false
        }
    }

    const iframeTrans = ({ eventName, params }: any) => {
        console.log(
            JSON.stringify({
                eventName,
                params: params
            })
        )
        if (window.parent) {
            window.parent.postMessage(
                JSON.stringify({
                    eventName,
                    params: params
                }),
                '*'
            )
        }
    }

    // 取消操作
    const handleCancel = () => {
        ElMessage.info('已取消')
        iframeTrans({ eventName: 'closeVisit' })
    }

    // IPS报告选择相关
    interface IpsReport {
        reportId: string
        reportName: string
        creDt: string
    }

    const selectedReport = ref<IpsReport | null>(null)
    const ipsDialogRef = ref<InstanceType<typeof IpsReportDialog> | null>(null)

    // 处理选择报告
    const handleSelectReport = () => {
        if (!consCustNo) {
            ElMessage.warning('缺少必要参数')
            return
        }
        if (ipsDialogRef.value) {
            ipsDialogRef.value.dialogVisible = true
        }
    }

    // 处理报告选择
    const handleReportSelect = (report: IpsReport | null) => {
        selectedReport.value = report
    }

    // 取消选择报告
    const handleCancelReport = () => {
        selectedReport.value = null
    }

    // 项目经理搜索相关
    const projectManagerLoading = ref(false)

    // 新增的 clearCommunicationType 方法
    const clearCommunicationType = () => {
        reminderForm.value.nextvisittype = ''
    }
</script>

<style lang="less" scoped>
    .communication-record {
        padding: 16px;

        .section-block {
            margin-bottom: 15px;
            background-color: #ffffff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .section-title {
            padding: 10px 16px;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #ebeef5;
        }

        .section-content {
            padding: 12px 16px;
        }

        :deep(.el-collapse) {
            border: none;

            .el-collapse-item {
                margin-bottom: 15px;
                background-color: #ffffff;
                border-radius: 4px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .el-collapse-item__header {
                padding: 0;
                border: none;
            }

            .el-collapse-item__wrap {
                border: none;
            }

            .el-collapse-item__content {
                padding: 0;
            }
        }

        .accompany-persons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .accompany-person {
            display: flex;
            gap: 8px;
            align-items: center;

            .el-checkbox {
                width: 180px;
                margin-right: 8px;
            }

            .el-input {
                width: 600px;

                &.is-disabled {
                    .el-input__inner {
                        color: #c0c4cc;
                        cursor: not-allowed;
                        background-color: #f5f7fa;
                        border-color: #e4e7ed;
                    }
                }
            }
        }

        .amount-inputs {
            display: flex;
            gap: 8px;
            align-items: center;

            .el-checkbox {
                margin-right: 3px;
            }
        }

        .form-actions {
            margin-top: 15px;
            text-align: center;
        }

        :deep(.el-form-item.is-required .el-form-item__label::before) {
            margin-right: 4px;
            color: #f56c6c;
            content: '*';
        }

        // 移除默认的红色星号
        :deep(.el-form-item .el-form-item__label::before) {
            margin-right: 0;
            content: '';
        }

        :deep(.no-wrap) {
            .el-form-item__label {
                white-space: nowrap;
            }
        }

        .purpose-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
        }

        .ips-report {
            margin-top: -10px;

            .report-status {
                margin-left: 8px;
                vertical-align: middle;

                .cancel-icon {
                    margin-left: 5px;
                    color: #909399;
                    cursor: pointer;
                    transition: color 0.3s;

                    &:hover {
                        color: #f56c6c;
                    }
                }
            }

            .report-tip {
                margin-left: 5px;
                color: #f56c6c;
            }
        }

        :deep(.el-form) {
            .el-form-item {
                display: flex;
                margin-bottom: 12px;

                .el-form-item__label {
                    display: flex;
                    flex: 0 0 120px;
                    align-items: flex-start;
                    justify-content: flex-end;
                    height: 100%;
                    padding-top: 7px;
                    padding-right: 10px;
                    font-weight: bold;
                    line-height: 1.4;
                    text-align: right;
                }

                &.rating-item {
                    .el-form-item__label {
                        flex: 0 0 200px;
                        overflow: visible;
                        white-space: nowrap;
                    }
                }

                .el-form-item__content {
                    display: flex;
                    flex: 1;
                    align-items: flex-start;
                    min-width: 0;
                    padding-top: 7px;
                    margin-left: 0 !important;
                    line-height: 1.4;
                }

                // 单选按钮和复选框特殊处理
                &:has(.el-radio-group),
                &:has(.el-checkbox-group) {
                    .el-form-item__content {
                        padding-top: 4px; // 调整单选和复选框的顶部内边距
                    }
                }
            }

            // 更通用的方式处理单选按钮和复选框
            :deep(.el-radio-group),
            :deep(.el-checkbox-group) {
                & + .el-form-item__content,
                & ~ .el-form-item__content {
                    padding-top: 4px !important; // 调整单选和复选框的顶部内边距
                }
            }

            // 为单选按钮和复选框的父元素添加特殊类
            .el-form-item--with-radio .el-form-item__content,
            .el-form-item--with-checkbox .el-form-item__content {
                padding-top: 4px !important;
            }

            // 处理包含文本域的表单项
            .el-form-item--with-textarea .el-form-item__label,
            .el-form-item:has(.el-textarea) .el-form-item__label {
                align-items: flex-start !important;
                padding-top: 7px !important;
            }

            .el-form-item--with-textarea .el-form-item__content,
            .el-form-item:has(.el-textarea) .el-form-item__content {
                align-items: flex-start !important;
            }

            // 直接处理文本域
            .el-textarea {
                & ~ .el-form-item__label {
                    align-items: flex-start !important;
                    padding-top: 7px !important;
                }

                & ~ .el-form-item__content {
                    align-items: flex-start !important;
                }
            }

            // 通用处理所有包含文本域的表单项
            .el-form-item .el-textarea {
                & + .el-form-item__label,
                & ~ .el-form-item__label,
                & + .el-form-item__content,
                & ~ .el-form-item__content {
                    align-items: flex-start !important;
                }
            }

            // 为文本域的父级表单项设置样式
            .el-form-item:has(.el-textarea) {
                .el-form-item__label {
                    align-items: flex-start !important;
                    padding-top: 7px !important;
                }

                .el-form-item__content {
                    align-items: flex-start !important;
                }
            }

            // 为多行标签的表单项单独设置样式
            .el-form-item--feedback .el-form-item__label,
            .el-form-item--multi-line .el-form-item__label {
                flex: 0 0 220px;
                align-items: flex-start;
                padding-top: 7px;
                text-align: right;
            }

            // 为客户参与人员及其角色标签单独设置宽度
            .no-wrap .el-form-item__label {
                flex: 0 0 180px;
                text-align: right;
            }

            :deep(.el-input__inner),
            :deep(.el-textarea__inner) {
                padding: 5px 8px;
            }

            :deep(.el-form-item__label-wrap),
            :deep(.el-form-item__label) {
                margin-bottom: 0;
            }

            // 更直接地处理包含文本域的表单项
            :deep(.el-textarea) {
                & + .el-form-item__label,
                & ~ .el-form-item__label {
                    align-items: flex-start !important;
                    padding-top: 7px !important;
                }

                & + .el-form-item__content,
                & ~ .el-form-item__content {
                    align-items: flex-start !important;
                }
            }
        }

        .multi-line-label {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            height: 100%;

            > div {
                font-weight: bold;
                line-height: 1.2;
                text-align: left;
                white-space: nowrap;
            }
        }

        .required-tip {
            font-size: 0.8em;
            color: #f56c6c;
        }

        .date-time-picker {
            display: flex;
            gap: 8px;
            align-items: center;

            .separator {
                color: #606266;
            }

            :deep(.el-date-editor) {
                width: 180px;
            }

            :deep(.el-time-picker) {
                width: 140px;
            }
        }

        :deep(.el-radio),
        :deep(.el-checkbox) {
            padding: 3px 0;
            margin-right: 10px;
            line-height: 1.4;

            .el-radio__label,
            .el-checkbox__label {
                padding-left: 6px;
            }
        }

        :deep(.el-textarea__inner) {
            min-height: 60px !important;
            line-height: 1.4;
        }

        :deep(.el-select-dropdown__item) {
            height: 28px;
            padding: 0 15px;
            line-height: 28px;
        }

        :deep(.el-row) {
            margin-bottom: 8px;
        }
    }

    :deep(.el-picker-panel) {
        margin-top: 5px;
    }

    :deep(.el-textarea__inner) {
        line-height: 1.4;
    }

    // 全局处理文本域对齐问题
    :deep(.el-form-item:has(.el-textarea)) {
        .el-form-item__label {
            align-items: flex-start !important;
            padding-top: 7px !important;
        }

        .el-form-item__content {
            align-items: flex-start !important;
        }
    }

    // 全局处理所有表单项的对齐问题
    :deep(.el-form-item) {
        .el-form-item__label {
            display: flex;
            align-items: flex-start !important;
        }

        .el-form-item__content {
            display: flex;
            align-items: flex-start !important;
        }
    }

    // 单选框组与清空按钮样式
    .radio-group-with-clear {
        display: flex;
        align-items: center;

        .clear-button {
            padding: 2px;
            margin-left: 8px;
            font-size: 16px;
            color: #909399;

            &:hover {
                color: #f56c6c;
            }

            .el-icon {
                font-size: 16px;
                font-weight: bold;
            }
        }
    }
</style>
