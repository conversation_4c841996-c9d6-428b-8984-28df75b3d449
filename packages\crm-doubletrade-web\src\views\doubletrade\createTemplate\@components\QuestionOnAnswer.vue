<!--
 * @Author: wencai.deng <EMAIL>
 * @Date: 2024-06-06 13:15:11
 * @LastEditors: wencai.deng <EMAIL>
 * @LastEditTime: 2024-06-07 11:23:44
 * @FilePath: /crm-web/packages/crm-doubletrade-web/src/views/doubletrade/createTemplate/@components/QuestionOnAnswer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>问题及答案</span>
                </div>
            </template>

            <div class="hint-word">
                提示：问题如涉及参数，请使用以下占位符；答案如需配置多个关键词，请使用英文格式的分号(;)分割
            </div>
            <el-table :data="tableData" border class="table">
                <el-table-column prop="customerName" label="客户姓名" width="180" />
                <el-table-column prop="creditCard" label="证件号码" width="180" />
                <el-table-column prop="customerRiskLevel" label="客户风险等级" />
                <el-table-column prop="distribution" label="分销渠道" />
                <el-table-column prop="fundcode" label="基金代码" />
                <el-table-column prop="fundName" label="基金名称" />
                <el-table-column prop="fundRiskLevel" label="基金风险等级" />
                <el-table-column prop="appamt" label="购买金额" />
            </el-table>

            <template #footer>Footer content</template>
        </el-card>
    </div>
</template>

<script setup lang="ts">
    import { Ref, ref } from 'vue'
    // eslint-disable-next-line no-undef
    const tableData: Ref<
        {
            customerName: string
            creditCard: string
            customerRiskLevel: string
            distribution: string
            fundcode: string
            fundName: string
            fundRiskLevel: string
            appamt: string
        }[]
    > = ref([
        {
            customerName: '${custname}',
            creditCard: '${custno}',
            customerRiskLevel: '${custrisklevel}',
            distribution: '${distribution}',
            fundcode: '${fundcode}',
            fundName: '${fundname} ',
            fundRiskLevel: '${fundtrisklevel}',
            appamt: '${appamt}'
        }
    ])
</script>

<style scoped>
    .hint-word {
        margin: 0 0 10px 0;
        color: red;
    }
    .table {
        border-top: 1px #ebeef5 solid;
    }
</style>
