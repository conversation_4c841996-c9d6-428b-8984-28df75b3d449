<!--
 * @Description: 日期区间选择，开始和结束日期组件放在一起显示，可独立选择
 * @Author: chaohui.wu
 * @Date: 2023-03-16 15:55:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:40:08
 * @FilePath: /crm-rpo-template/common/components/moduleBase/DateRange.vue
 * @eg: <date-range
            v-model="data.createDate"
        />
-->

<template>
    <div class="date_range" :class="customClassName">
        <!-- unlink-panels -->
        <el-date-picker
            v-model="pickerData.startDate"
            class="date_range_picker start"
            popper-class="date_range_picker_popper"
            size="small"
            :type="type"
            :align="`left`"
            placeholder="开始日期"
            format="YYYY-MM-DD"
            range-separator=" "
            :value-format="valueFormat"
            :disabled="disabled"
            :disabled-date="disabledStartDate"
            @focus="handleFocus(true)"
            @blur="handleFocus(false)"
            @change="handleChange"
        />
        <el-date-picker
            v-model="pickerData.endDate"
            class="date_range_picker end"
            popper-class="date_range_picker_popper"
            size="small"
            :type="type"
            :align="`left`"
            format="YYYY-MM-DD"
            range-separator="-"
            placeholder="结束日期"
            :value-format="valueFormat"
            :disabled="disabled"
            :disabled-date="disabledEndDate"
            @focus="handleFocus(true)"
            @blur="handleFocus(false)"
            @change="handleChange"
        />
    </div>
</template>

<script>
    import { dateFormat } from '@common/utils/index'

    export default defineComponent({
        name: 'DateRange',
        props: {
            modelValue: {
                type: Object,
                default: () => ({
                    startDate: '',
                    endDate: ''
                })
            },
            disabled: {
                type: Boolean,
                default: false
            },
            valueFormat: {
                type: String,
                default: 'YYYYMMDD'
            },
            type: {
                type: String,
                default: 'date'
            },
            // 限定可选日期小于等于当前日期
            limitLessThanCurrent: {
                type: Boolean,
                default: false
            },
            disabledDate: {
                // 限制选择的开始结束日期区间
                type: Object,
                default: () => {
                    return {
                        startDate: '',
                        endDate: ''
                    }
                }
            }
        },
        emits: ['update:modelValue', 'custChange'],
        data() {
            return {
                isFocus: false
            }
        },
        computed: {
            customClassName() {
                return this.isFocus ? 'is-focus' : ''
            },
            dayNum: () => 24 * 3600 * 1000,
            pickerData: {
                get() {
                    return this.modelValue
                },
                set(val) {
                    this.$emit('update:modelValue', val)
                }
            },
            pickDataDisabled() {
                return this.disabledDate || {}
            }
        },
        created() {
            this.pickerData = this.modelValue
        },

        methods: {
            handleFocus(flag = false) {
                this.isFocus = flag
            },
            // 开始日期 <= 结束日期
            disabledStartDate(time) {
                const ctime = time.getTime()
                const { endDate: endDateCur } = this.pickerData
                const lastDate = dateFormat(endDateCur, 'x')
                const newDisable = lastDate ? ctime >= +lastDate : false
                // 有disabledDate情况
                const { startDate, endDate } = this.pickDataDisabled
                const sDate = startDate ? dateFormat(startDate, 'x') : ''
                const eDate = endDate ? dateFormat(endDate, 'x') : ''
                if (sDate || eDate) {
                    const minDisable = sDate ? ctime < sDate - this.dayNum : false
                    const maxDisable = eDate ? ctime >= eDate : false
                    return minDisable || newDisable || maxDisable
                }
                const lessThanCurrent = this.limitLessThanCurrent
                    ? new Date().getTime() < ctime
                    : false

                return newDisable || lessThanCurrent
            },
            // 结束日期 >= 开始日期
            disabledEndDate(time) {
                const ctime = time.getTime()
                const { startDate: startDateCur } = this.pickerData
                const firstDate = dateFormat(startDateCur, 'x')
                const newDisable = firstDate ? ctime <= firstDate - this.dayNum : false

                // 有disabledDate情况
                const { startDate, endDate } = this.pickDataDisabled
                const sDate = startDate ? dateFormat(startDate, 'x') : ''
                const eDate = endDate ? dateFormat(endDate, 'x') : ''

                if (sDate || eDate) {
                    const minDisable = sDate ? ctime < sDate - this.dayNum : false
                    const maxDisable = eDate ? ctime >= eDate : false
                    return newDisable || minDisable || maxDisable
                }

                const lessThanCurrent = this.limitLessThanCurrent
                    ? new Date().getTime() < ctime
                    : false

                return newDisable || lessThanCurrent
            },
            handleChange() {
                this.$emit('custChange')
            }
        }
    })
</script>

<style lang="less">
    // 合并两个datePicker的容器样式
    .date_range {
        display: flex;
        align-items: center;
        padding: 1px 0 1px 7px;
        white-space: nowrap;
        background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
        background-image: none;
        border: 1px solid @border_color_01;
        border-radius: var(--el-input-border-radius, var(--el-border-radius-base));

        &:hover {
            border-color: @border_hover;
        }

        &.is-focus {
            border-color: @border_focus;
        }

        .date_range_picker {
            position: relative;
            width: auto;
            height: 20px;
            padding-right: 21px;
            // background: url(../../assets/images/calendar.png) no-repeat right 3px;
            background-size: 15px auto;

            &:nth-of-type(2) {
                display: inline-flex;
                padding-right: 2px;
                // .el-input__suffix
                .el-input__prefix {
                    display: none;
                }
                // .el-input__inner {
                //     width: 90px;
                // }
                &::before {
                    position: absolute;
                    top: 50%;
                    left: -20px;
                    height: 20px;
                    font-size: 10px;
                    font-weight: bold;
                    line-height: 16px;
                    color: @font_color_03;
                    text-align: center;
                    content: '--';
                    transform: translateY(-50%);
                }
            }

            .el-input__wrapper {
                padding: 0;
                box-shadow: none;
            }

            .el-input__inner {
                width: 80px;
                height: 20px;
                padding: 0 2px;
                font-size: 12px;
                line-height: 20px;
                color: @font_color_02;
                text-align: left;

                &:hover,
                &:focus {
                    border-color: #7078b8;
                }

                &::placeholder {
                    color: @font_color_03;
                }
            }

            &.is-disabled .el-input__inner {
                border-bottom: 1px solid #7078b8;
            }

            // .el-input__prefix,
            .el-input__suffix {
                // display: none;
                .el-input__suffix-inner {
                    position: relative;

                    .clear-icon {
                        position: absolute;
                        top: 50%;
                        left: -18px;
                        width: 16px;
                        height: 16px;
                        transform: translateY(-50%);
                    }
                    // pointer-events: all;
                    // display: inline-flex;
                    // align-items: center;
                    // justify-content: flex-end;
                }
            }

            &.el-date-editor.el-input.is-disabled {
                .el-input__inner {
                    color: #666666;
                    background-color: #ffffff;
                }
            }
        }
    }

    .date_range_picker_popper {
        .el-picker-panel__icon-btn {
            margin-top: 0 !important;
        }
    }
</style>
