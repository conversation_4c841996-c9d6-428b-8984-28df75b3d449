/*
 * @Description: charts颜色
 * @Author: chaohui.wu
 * @Date: 2023-03-16 11:00:06
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-08 18:01:27
 * @FilePath: /crm-asset-web/src/components/charts/scripts/chartColors.js
 *
 */
/**
 * echarts图表的颜色
 */

import { switchColorToRgba } from './methods'

// 折线图色值
const lineColors = [
    '#F04950',
    '#6268A2',
    '#959595',
    '#333335',
    '#EE703F',
    '#7E4A9B',
    '#8A662C',
    '#44488E',
    '#BA67E9',
    '#3FAEEE',
    '#DC494F',
    '#9EA2C9',
    '#CBCBCB',
    '#5A5A63',
    '#F8B399',
    '#B074D1',
    '#C8A060',
    '#1C26CE',
    '#CB96E9',
    '#AFDEFA'
]

// 图表色值
export const chartColors = {
    line: {
        colors: lineColors,
        shadowColors: lineColors.map(v => switchColorToRgba(v, 0.5)),
        activeColors: lineColors
    },
    // 柱状堆叠图颜色，分三组，每组由深及浅，取色横向跳跃取
    stackBarColors: [
        ['#C94649', '#EEB2B4', '#E1777A', '#D57C56', '#E39A79', '#DB8A66', '#E5B88C'],
        ['#8588B7', '#B4B6D1', '#55598D', '#628497', '#A9C6CB', '#866EA9', '#B79BC7'],
        ['#7D7D7E', '#CACACA', '#A7A7A8', '#606063', '#C4C4C4', '#99999B', '#B7B7B7']
    ],
    // 堆叠面积图颜色
    stackLineColors: [
        ['#D55659', '#E1777A', '#DB8A66', '#E5B88C', '#EEB2B4', '#D57C56', '#E39A79'],
        ['#8588B7', '#626697', '#866EA9', '#B79BC7', '#B4B6D1', '#628497', '#A9C6CB'],
        ['#7D7D7E', '#A7A7A8', '#99999B', '#B7B7B7', '#CACACA', '#969696', '#C4C4C4']
    ],
    // 柱状图+折线图
    lineBarColors: ['#c94649', '#83859e', '#636363', '#f04950', '#6268a2', '#333335'],
    // 柱状图
    barColors: ['#E73D3D', '#8F8EB4', '#CECECE'],
    simiCircleColors: ['#D20000 ', '#004360']
}

// 图表legend的svg图形
export const legendSvg = {
    // 正方形 10*10
    rect: 'path://M-0.000,-0.000 L10.000,-0.000 L10.000,10.000 L-0.000,10.000 L-0.000,-0.000 Z',
    // 线条 10*3
    line: 'path://M-0.000,-0.000 L10.000,-0.000 L10.000,3.000 L-0.000,3.000 L-0.000,-0.000 Z',
    // 倒立红色三角形
    triangle: 'path://M0.000,10.000 L19.000,10.000 L9.500,-0.000 L0.000,10.000 Z'
}
