import { axiosRequest } from '@common/utils/index'
import type { IVisitFeedbackDetailReq, ISaveFeedbackReq } from './type/apiReqType'
import { IVisitFeedbackDetailRes } from './type/apiResType'

/**
 * 查询客户拜访纪要反馈明细
 * @param params 请求参数
 * @returns
 */
export function getVisitFeedbackDetail(params: IVisitFeedbackDetailReq) {
    return axiosRequest({
        url: '/customer/visitMinutes/visitFeedbackDetail',
        method: 'post',
        data: params
    })
}

/**
 * 保存陪访人/主管反馈
 * @param params 请求参数
 * @returns
 */
export function saveFeedback(params: ISaveFeedbackReq) {
    return axiosRequest({
        url: '/customer/visitMinutes/saveFeedback',
        method: 'post',
        data: params
    })
}
