/*
 * @Description: 数据转换ts
 * @Author: chao<PERSON>.wu
 * @Date: 2023-04-13 10:14:18
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-18 14:46:01
 * @FilePath: /dtms-product-web/src/utils/dataChange.ts
 *
 */
import { assetTypeReq } from '@/constant/index'
import { dateFormat, getString } from './dataTrans'

/**
 * @description: o值传参数处理
 * @return {*}
 */
export const zeroTrans = ({ val, fixed }: { val: string | number; fixed?: number }) => {
    return val === '' ? '' : fixed ? Number(val).toFixed(fixed) : Number(val)
}

/**
 * @description: 将枚举值转为下拉列表
 * @param {object} param1
 * @return {*}
 */
export const transMapToArray = (val: Map<string | number, string>) => {
    return Array.from(val).map(item => {
        return {
            key: item[0],
            label: item[1]
        }
    })
}

/**
 * @description: 将数字专为会计计数法
 * @param {*} num ｜ number
 * @param {*} fixed | number
 * @return {*} string
 */
export const formaAccountingNumber = ({ num = '0', fixed = 2 }: any) => {
    if (isNaN(Number(num))) {
        return num.toString()
    }

    // Round the number to 2 decimal places
    num = parseFloat(Number(num).toFixed(fixed))

    // Convert the number to a string
    const numStr = num.toString()

    // Split the number into integer and decimal parts
    const parts = numStr.split('.')
    const integerPart = parts[0]
    const decimalPart = parts.length > 1 ? '.' + parts[1] : '.' + '0'.repeat(fixed)

    // Add commas to the integer part
    const formattedIntegerPart = parseInt(integerPart).toLocaleString()

    // Combine the integer and decimal parts and return the result
    return formattedIntegerPart + decimalPart
}

export const formatNumber = ({ num = '0', fixed = 2 }: any) => {
    if (isNaN(Number(num))) {
        return num.toString()
    }

    // Round the number to 2 decimal places
    num = parseFloat(Number(num).toFixed(fixed))

    // Convert the number to a string
    const numStr = num.toString()

    // Split the number into integer and decimal parts
    const parts = numStr.split('.')
    const integerPart = parts[0]
    const decimalPart = parts.length > 1 ? '.' + parts[1] : '.' + '0'.repeat(fixed)

    // Add commas to the integer part
    const formattedIntegerPart = parseInt(integerPart).toLocaleString()

    // Combine the integer and decimal parts and return the result
    return formattedIntegerPart + decimalPart
}

/**
 * @description: 展示出千分位
 * @param {*} num ｜ number
 * @return {*} string
 */
export const formatNumberWithThousandSeparator = ({ num = '0', fixed = 2 }: any): string => {
    debugger

    if (isNaN(Number(num))) {
        return num.toString()
    }

    // Round the number to 2 decimal places
    num = parseFloat(Number(num).toFixed(fixed))

    // 检查转换结果是否为NaN，避免后续处理出错
    if (isNaN(num)) {
        return '0'
    } // 或者其他你希望展示的默认值或错误信息

    // 转换为字符串进行处理
    const numStr = num.toString()

    // 分离整数和小数部分
    const [integerPart, decimalPart] = numStr.includes('.') ? numStr.split('.') : [numStr, '']

    // 对整数部分进行千分位格式化
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

    // 根据需求决定是否限制小数位数，这里展示全部小数，不进行限制
    // 若要限制，例如到2位小数，可使用：decimalPart = decimalPart.slice(0, 2);

    // 组合整数和小数部分
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '.00')
}

export const formatNumberWithThousandSeparator3 = ({ num = '0', fixed = 2 }: any): string => {
    debugger

    if (isNaN(Number(num))) {
        return num.toString()
    }

    // Round the number to 2 decimal places
    num = parseFloat(Number(num).toFixed(fixed))

    // 检查转换结果是否为NaN，避免后续处理出错
    if (isNaN(num)) {
        return '0'
    } // 或者其他你希望展示的默认值或错误信息

    // 转换为字符串进行处理
    const numStr = num.toString()

    // 分离整数和小数部分
    const [integerPart, decimalPart] = numStr.includes('.') ? numStr.split('.') : [numStr, '']

    // 对整数部分进行千分位格式化
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

    // 根据需求决定是否限制小数位数，这里展示全部小数，不进行限制
    // 若要限制，例如到2位小数，可使用：decimalPart = decimalPart.slice(0, 2);

    // 组合整数和小数部分
    return formattedIntegerPart + (decimalPart ? '.' + decimalPart : '')
}

/**
 * 判断字段是否有值及无值时的展示处理
 * @param val
 * @param blankPlaceholder: 无数据时的展示字符，默认'-'
 */
export const formatValue = (val: string | number, blankPlaceholder = '—') => {
    if (!getString(val)) {
        return blankPlaceholder
    }
    return val || (val === 0 ? 0 : blankPlaceholder)
}
/**
 * @description: 类型处理
 * @param {any} param1
 * @return {*}
 */
export const formatType = ({ c, placeholder = '', typeList = assetTypeReq }: any) => {
    const curArr = typeList.filter((item: any) => item.key === c)[0]
    return curArr?.label ? curArr.label : placeholder
}

/**
 * @description: 类型二级标题枚举
 * @param {any} param1
 * @return {*}
 */
export const formatSub = ({ c, placeholder = '', typeList = assetTypeReq }: any) => {
    const curArr = typeList.filter((item: any) => item.key === c)[0]
    return curArr?.subTitle ? curArr.subTitle : placeholder
}

/**
 * @description: 国内海外枚举
 * @param {any} param1
 * @return {*}
 */
export const formatIsOverseas = ({ c }: any) => (c === 'O' ? '否' : '是')

/**
 * @description: 百分比/展示处理
 * @return {*}
 */
export const formatRatioPersent = ({
    val,
    amount,
    showCust = false,
    fixed = 2,
    sign = false,
    unit = '%'
}: any) => {
    const valTpl = Number(val)
    const amountTpl = Number(amount)
    if (amount !== undefined) {
        if (valTpl === 0 && amountTpl === 0) {
            return `0%`
        } else if (valTpl < 0.5 && amountTpl > 0) {
            return `<0.5%`
        } else if (valTpl === 100) {
            return `100%`
        } else if (valTpl > 99.5) {
            return `>99.5%`
        }
    }
    if (showCust) {
        if (Math.abs(valTpl) === 0) {
            return `0%`
        } else if (Math.abs(valTpl) < 0.5) {
            return valTpl > 0 ? `<0.5${unit}` : `>-0.5${unit}`
        } else if (Math.abs(valTpl) === 100) {
            return valTpl > 0 ? `100${unit}` : `-100${unit}`
        } else if (Math.abs(valTpl) > 99.5) {
            return valTpl > 0 ? `>99.5${unit}` : `<-99.5${unit}`
        }
    }
    return valTpl || valTpl === 0
        ? `${
              sign && valTpl > 0 ? '+' + valTpl.toFixed(fixed) + unit : valTpl.toFixed(fixed) + unit
          }`
        : '-'
}

/**
 * 添加单位
 * @param val
 * @param blankPlaceholder: 无数据时的展示字符，默认'-'
 */
// eslint-disable-next-line max-params
export const addUnit = ({
    val,
    fixed = 2,
    sign = false,
    unit = '%',
    blankPlaceholder = '-'
}: any) => {
    if (!getString(val) || !val) {
        return blankPlaceholder
    }
    return Number(val) || Number(val) === 0 ? Number(val).toFixed(fixed) + unit : val + unit
}

/**
 * @description: 添加正负号
 * @param {string} val
 * @return {*}
 */
export const addPlusOrMinusSign = (val: string | number) => {
    if (!val || val === '0' || val === '0.00') {
        return val
    }
    return Number(val) > 0 ? '+' + val : val
}

/**
 * @description: 数据转换国内海外组合为100%
 * @param {*} dataTpl
 * @return {*}
 */
export const transHundredStr = (dataTpl: { ratio: number; data: number; value: string }[]) => {
    // 获取数据中较小值
    const { ratio, data, value } = dataTpl[0]
    const { ratio: ratio1, data: data1, value: value1 } = dataTpl[1]
    if (ratio >= ratio1) {
        if (ratio1 === 0 && data1 === 0) {
            return [`${value} ${ratio}%`, `${value1} ${ratio1}%`]
        } else if (ratio1 < 0.5 && data1 !== 0) {
            return [`${value} >${99.5}%`, `${value1} <${0.5}%`]
        }
        return [`${value} ${Number(ratio).toFixed(0)}%`, `${value1} ${Number(ratio1).toFixed(0)}%`]
    } else if (ratio === 0 && data === 0) {
        return [`${value} ${ratio}%`, `${value1} ${ratio1}%`]
    } else if (ratio < 0.5 && data !== 0) {
        return [`${value} <${0.5}%`, `${value1} >${99.5}%`]
    }
    return [`${value} ${Number(ratio).toFixed(0)}%`, `${value1} ${Number(ratio1).toFixed(0)}%`]
}

/**
 * @description: table表格 类型
 * @return {*}
 */
// 给表格数据加'%'，空值显示'-'，传递formatter方法的时候使用
export const addPercentUnitForTableValue = (a: any, b: any, c: any) => addUnit({ val: c })
// 给表格数据加'%'，空值显示''，传递formatter方法的时候使用
export const addNullUnitTableVal = (a: any, b: any, c: any) =>
    addUnit({ val: c, blankPlaceholder: '' })
// 给表格数据进行日期格式化
export const formateDateForTableValue = (a: any, b: any, c: any) => (c ? dateFormat(c) : '-')
// 处理表格数值型数据
export const formatNumForTableValue = (a: any, b: any, c: any) => c ?? '-'
// 表格数据的空值显示'-'，传递formatter方法的时候使用
export const formatTableValue = (a: any, b: any, c: any) => formatValue(c)
// table表格类型处理
export const tableType = (a: any, b: any, c: any) => formatType({ c, placeholder: '合计' })
// table 国内海外展示
export const tableIsOverseas = (a: any, b: any, c: any) => formatIsOverseas({ c })
// table会计计数法
export const tableFormatNum = (a: any, b: any, c: any) => formaAccountingNumber({ num: c })

/**
 * @description: 数字千分位格式化 保留两位小数
 * @param a
 * @param b
 * @param c
 */
export const numberWithThousandSeparatorTwoDecimalPlaces = (a: any, b: any, c: any) =>
    formatNumberWithThousandSeparator({ num: c })

/**
 * @description: 数字千分位格式化 整数展示
 * @param a
 * @param b
 * @param c
 */
export const numberWithThousandSeparatorNoPoint = (a: any, b: any, c: any) =>
    formatNumberWithThousandSeparator3({ num: c })
