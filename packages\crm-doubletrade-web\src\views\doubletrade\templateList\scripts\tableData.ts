/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 16:27:08
 * @FilePath: /crm-web/packages/crm-template/src/views/assetReport/reportList/scripts/tableData.ts
 *
 */
import { TableColumnItem } from '@/types/index'
import { formatTableValue } from '@common/utils/index'
import { TEMPLATE_LIST_MAP } from '@/constant'

/**
 * @description: table表格数据
 * @return {*}
 */
export const tempListTableColumn: TableColumnItem[] = [
    {
        key: 'id',
        label: '模版ID',
        minWidth: 60,
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'tempName',
        label: '模版名称',
        minWidth: 120,
        width: 200,
        formatter: formatTableValue
    },
    {
        key: 'custType',
        label: '客户类型',
        width: 80,
        formatter: (a: any, b: any, c: any) => {
            return c ? TEMPLATE_LIST_MAP.CUSTTYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'preType',
        label: '成单方式',
        width: 80,
        formatter: (a: any, b: any, c: any) => {
            return c ? TEMPLATE_LIST_MAP.PRETYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'tempType',
        label: '模版适用类型',
        width: 80,
        formatter: (a: any, b: any, c: any) => {
            return c ? TEMPLATE_LIST_MAP.TEMPTYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'checkflag',
        label: '审核状态',
        width: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? TEMPLATE_LIST_MAP.CHECKFLAG_MAP.get(c) : ''
        }
    },
    {
        key: 'effectdt',
        label: '生效时间',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'failuredt',
        label: '失效时间',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'credt',
        label: '创建时间',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'creator',
        label: '创建人',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'moddt',
        label: '修改时间',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'modifier',
        label: '修改人',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'checkdt',
        label: '审核时间',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'checker',
        label: '审核人',
        width: 80,
        formatter: formatTableValue
    }
]
