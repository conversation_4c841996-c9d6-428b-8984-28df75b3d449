<template>
    <el-button class="btn-view-range" type="primary" size="small" @click="viewRange"
        >查看产品适用范围</el-button
    >

    <el-dialog v-model="dialogVisible" title="产品范围" width="1000">
        <div>
            <table-wrapper
                class-name="template-table"
                :show-operation-left="true"
                :show-tabs-panel="true"
                @searchFn="handleSearch"
            >
                <template #searchArea>
                    <label-item label="基金简称">
                        <CrmSearchCode v-model="jjdm" />
                    </label-item>
                </template>
                <template #tableContentMiddle>
                    <base-table
                        :is-loading="listLoading"
                        :columns="tableColumn"
                        :data="tableData"
                        style="width: 100%"
                        height="100%"
                        :no-index="false"
                        :no-select="false"
                        :border="true"
                        :show-operation="false"
                        operation-width="150"
                    >
                    </base-table>
                </template>
            </table-wrapper>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref } from 'vue'
    // eslint-disable-next-line vue/require-prop-types
    const props = defineProps(['propsData'])
    import CrmSearchCode from './CrmSearchCode.vue'
    import { tableColumn } from '../config/templateConst'
    import { ElMessage } from 'element-plus'
    import { fetchRes } from '@common/utils'
    import { queryMatchfund } from '@/api/project/doubletrade/doubletradeList'

    const dialogVisible = ref(false)
    const jjdm = ref<string>('')

    const tableData = ref([])
    const listLoading = ref(false)

    // 查看产品使用范围
    function viewRange() {
        handleMatch()
        dialogVisible.value = true
    }

    function handleSearch() {
        if (jjdm.value === '' || jjdm.value === null) {
            ElMessage({
                message: '基金代码为空！',
                showClose: true,
                type: 'error'
            })
            return
        }
        handleMatch()
    }

    function handleMatch() {
        const riskArr = props.propsData.proRiskLevel.join(',')
        const params = {
            jjdm: jjdm.value,
            risklevel: riskArr,
            proType: props.propsData.proType,
            tempType: props.propsData.tempType
        }

        fetchRes(queryMatchfund(params), {
            successCB: (res: any) => {
                if (res === null || res === undefined || res === '') {
                    tableData.value = []
                    return
                }
                tableData.value = res.list
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
</script>

<style scoped>
    .template-table {
        height: 578px;
    }
    .btn-view-range {
        position: absolute;
        top: 0px;
        right: -10px;
    }
</style>
