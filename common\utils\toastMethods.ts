/*
 * @Description: 提示方法
 * @Author: chaohui.wu
 * @Date: 2023-04-13 19:22:58
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-15 11:19:18
 * @FilePath: /crm-web/common/utils/toastMethods.ts
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

/**
 * @description: 消息弹框组件
 * @param title string
 * @param content string
 * @param confirmBtn string
 * @param cancelBtn string
 * @param useHtml boolean
 * @param successCB Function
 * @param cancelCB Function
 * @return {*}
 */
export const messageBox = (
    params: {
        title?: string
        content?: string
        confirmBtn?: string
        cancelBtn?: string
        showCancelButton?: boolean
        showConfirmButton?: boolean
        useHtml?: boolean
        [keyof: string]: any
    },
    successCB?: Function | null,
    cancelCB?: Function | null
) => {
    ElMessageBox.confirm(params.content, params?.title ?? '提示', {
        confirmButtonText: params?.confirmBtn ?? '确定',
        cancelButtonText: params?.cancelBtn ?? '取消',
        customClass: 'blank-message-box',
        center: true,
        dangerouslyUseHTMLString: params?.useHtml ?? false,
        ...params
    })
        .then(() => {
            successCB && successCB(params)
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '取消'
            })
            cancelCB && cancelCB()
        })
}

/**
 * @description: toast
 * @param message string | VNode | Function
 * @param type string | 'success' | 'error' | 'warning'
 * @return {*}
 */
export const message = ({ message, type = 'success' }: any) => {
    return ElMessage({
        message,
        type
    })
}
