<!--
 * @Description: 客户拜访纪要
 * @Author: hongdong.xie
 * @Date: 2024-06-12 10:15:39
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2024-06-12 10:15:39
 * @FilePath: /crm-web/packages/crm-template/src/views/customerVisit/visitRecord/visitRecordIndex.vue
-->
<template>
    <div class="visit-record-module">
        <crm-dialog
            v-model="dialogVisible"
            width="800px"
            title="客户拜访纪要"
            :slot-list="['default', 'footer']"
            :close-on-click-modal="false"
            class-name="visit-record-dialog"
        >
            <div class="visit-record-content">
                <!-- 基本信息 -->
                <div class="section-title">基本信息</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td class="label-cell">客户姓名<span class="required">*</span></td>
                            <td>
                                <crm-input
                                    v-model="formData.customerName"
                                    placeholder="请输入客户姓名"
                                />
                            </td>
                            <td class="label-cell">投顾客户号<span class="required">*</span></td>
                            <td>
                                <crm-input
                                    v-model="formData.customerNo"
                                    placeholder="请输入投顾客户号"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">拜访日期<span class="required">*</span></td>
                            <td>
                                <date-picker
                                    v-model="formData.visitDate"
                                    type="date"
                                    placeholder="选择日期"
                                    format="YYYYMMDD"
                                    value-format="YYYYMMDD"
                                />
                            </td>
                            <td class="label-cell">沟通方式<span class="required">*</span></td>
                            <td>
                                <crm-select
                                    v-model="formData.communicationMethod"
                                    :option-list="communicationMethodOptions"
                                    placeholder="请选择沟通方式"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">客户存量</td>
                            <td>
                                <crm-input
                                    v-model="formData.customerAssets"
                                    placeholder="请输入客户存量"
                                />
                            </td>
                            <td class="label-cell">客户综合健康度（1-5星）</td>
                            <td>
                                <crm-select
                                    v-model="formData.healthLevel"
                                    :option-list="healthLevelOptions"
                                    placeholder="请选择健康度"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">拜访目的<span class="required">*</span></td>
                            <td colspan="3">
                                <div class="checkbox-group">
                                    <el-checkbox v-model="formData.visitPurpose.firstMeeting"
                                        >初次见面</el-checkbox
                                    >
                                    <el-checkbox v-model="formData.visitPurpose.productService"
                                        >产品服务</el-checkbox
                                    >
                                    <el-checkbox v-model="formData.visitPurpose.ipsVisit"
                                        >IPS访谈</el-checkbox
                                    >
                                    <el-checkbox v-model="formData.visitPurpose.innovationVisit"
                                        >创新路访</el-checkbox
                                    >
                                    <el-checkbox v-model="formData.visitPurpose.leadsVisit"
                                        >Leads及准客户拜访</el-checkbox
                                    >
                                    <el-checkbox v-model="formData.visitPurpose.other"
                                        >其他</el-checkbox
                                    >
                                    <span v-if="formData.visitPurpose.other">投后维护</span>
                                </div>
                                <div v-if="formData.visitPurpose.ipsVisit" class="ips-report">
                                    IPS报告：
                                    <el-link type="primary" @click="handleViewIpsReport">{{
                                        formData.ipsReportName
                                    }}</el-link>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">提供资料<span class="required">*</span></td>
                            <td colspan="3">
                                <crm-input
                                    v-model="formData.providedMaterials"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入提供的资料"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">陪访人</td>
                            <td colspan="3">
                                <crm-input
                                    v-model="formData.accompaniedPersons"
                                    placeholder="请输入陪访人"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">客户参与人员及角色</td>
                            <td colspan="3">
                                <crm-input
                                    v-model="formData.customerParticipants"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入客户参与人员及角色"
                                />
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 客户反馈 -->
                <div class="section-title">客户反馈</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td class="label-cell">对产品或服务的具体反馈<br />（创新路访必填）</td>
                            <td>
                                <crm-input
                                    v-model="formData.productFeedback"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入反馈内容"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">对于IPS报告反馈<br />（IPS访谈必填）</td>
                            <td>
                                <crm-input
                                    v-model="formData.ipsFeedback"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入反馈内容"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">近期可用于加仓的金额<br />（IPS访谈必填）</td>
                            <td>
                                <div class="amount-input">
                                    <el-checkbox v-model="formData.hasRmbAmount"
                                        >人民币</el-checkbox
                                    >
                                    <crm-input v-model="formData.rmbAmount" class="amount-value" />
                                    <span class="unit">万</span>

                                    <el-checkbox
                                        v-model="formData.hasForeignAmount"
                                        class="foreign-checkbox"
                                        >外币</el-checkbox
                                    >
                                    <crm-input
                                        v-model="formData.foreignAmount"
                                        class="amount-value"
                                    />
                                    <span class="unit">万</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                近期关注的资产类别或自有产品<br />（IPS访谈必填）
                            </td>
                            <td>
                                <crm-input
                                    v-model="formData.interestedAssets"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入关注的资产类别或自有产品"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                评估客户对创新业务、家族信托、身份、法税的需求
                            </td>
                            <td>
                                <crm-input
                                    v-model="formData.innovationNeeds"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入评估内容"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">下一步工作计划：</td>
                            <td>
                                <crm-input
                                    v-model="formData.nextStepPlan"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入下一步工作计划"
                                />
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 陪访专区 -->
                <div class="section-title">陪访专区（陪访人填写：项目经理/分总/区域总/其他）</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td rowspan="2" class="label-cell accompany-person">
                                陪访人：{{ formData.accompanyPerson }}
                            </td>
                            <td class="label-cell">本次陪访最要经验或教训</td>
                            <td class="label-cell">该客户下阶段工作建议</td>
                        </tr>
                        <tr>
                            <td>
                                <crm-input
                                    v-model="formData.visitExperience"
                                    type="textarea"
                                    :rows="4"
                                    placeholder="请输入内容"
                                />
                            </td>
                            <td>
                                <crm-input
                                    v-model="formData.workSuggestion"
                                    type="textarea"
                                    :rows="4"
                                    placeholder="请输入内容"
                                />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <button class="custom-button save-button" @click="handleSave">保存</button>
                    <button class="custom-button cancel-button" @click="handleCancel">取消</button>
                </div>
            </template>
        </crm-dialog>
    </div>
</template>

<script lang="ts" setup>
    import { ref, reactive } from 'vue'
    import { ElMessage } from 'element-plus'
    import { useVisible } from '@/hooks/useVisible'

    defineOptions({
        name: 'VisitRecordIndex'
    })

    const props = defineProps<{
        modelValue: boolean
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'save', data: any): void
    }>()

    // 使用自定义hook处理弹窗显示逻辑
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    // 沟通方式选项
    const communicationMethodOptions = ref([
        { label: '线上会议', value: '线上会议' },
        { label: '电话沟通', value: '电话沟通' },
        { label: '面对面会议', value: '面对面会议' }
    ])

    // 健康度选项
    const healthLevelOptions = ref([
        { label: '1星', value: '1星' },
        { label: '2星', value: '2星' },
        { label: '3星', value: '3星' },
        { label: '4星', value: '4星' },
        { label: '5星', value: '5星' }
    ])

    // 表单数据
    const formData = reactive({
        customerName: '张三',
        customerNo: '100000001',
        visitDate: '20250201',
        communicationMethod: '线上会议',
        customerAssets: '100,000.00',
        healthLevel: '3星',
        visitPurpose: {
            firstMeeting: false,
            productService: false,
            ipsVisit: true,
            innovationVisit: true,
            leadsVisit: false,
            other: true
        },
        ipsReportName: '张三的资产配置报告20240612',
        providedMaterials: '公司介绍、IPS报告、宏观策略报告、产品介绍',
        accompaniedPersons: 'XXX1、XXX2',
        customerParticipants: '',
        productFeedback: '文案示例文案示例文案示例文案示例文案示例文案示例',
        ipsFeedback:
            '文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例文案示例',
        hasRmbAmount: true,
        rmbAmount: '300',
        hasForeignAmount: false,
        foreignAmount: '',
        interestedAssets: '文案示例文案示例文案示例文案示例文案示例文案示例',
        innovationNeeds: '',
        nextStepPlan: '文案示例文案示例文案示例文案示例文案示例文案示例',
        accompanyPerson: 'XXX',
        visitExperience: '',
        workSuggestion: ''
    })

    // 查看IPS报告
    const handleViewIpsReport = () => {
        ElMessage({
            type: 'info',
            message: '查看IPS报告：' + formData.ipsReportName
        })
    }

    // 取消
    const handleCancel = () => {
        handleClose()
    }

    // 保存
    const handleSave = () => {
        // 表单验证
        if (!formData.customerName) {
            return ElMessage({
                type: 'warning',
                message: '请输入客户姓名'
            })
        }
        if (!formData.customerNo) {
            return ElMessage({
                type: 'warning',
                message: '请输入投顾客户号'
            })
        }
        if (!formData.visitDate) {
            return ElMessage({
                type: 'warning',
                message: '请选择拜访日期'
            })
        }
        if (!formData.communicationMethod) {
            return ElMessage({
                type: 'warning',
                message: '请选择沟通方式'
            })
        }

        // 检查拜访目的是否至少选择一项
        const hasSelectedPurpose = Object.values(formData.visitPurpose).some(value => value)
        if (!hasSelectedPurpose) {
            return ElMessage({
                type: 'warning',
                message: '请至少选择一项拜访目的'
            })
        }

        if (!formData.providedMaterials) {
            return ElMessage({
                type: 'warning',
                message: '请输入提供资料'
            })
        }

        // 保存数据
        emit('save', formData)
        handleClose()
        ElMessage({
            type: 'success',
            message: '保存成功'
        })
    }
</script>

<style lang="less" scoped>
    .visit-record-module {
        .visit-record-content {
            max-height: 70vh;
            overflow-y: auto;
            padding: 0 10px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0 10px;
            padding-left: 10px;
            background-color: #f5f7fa;
            line-height: 36px;
            border-radius: 4px;
        }

        .info-table {
            margin-bottom: 20px;

            .crm-table {
                width: 100%;
                border-collapse: collapse;

                td {
                    padding: 8px;
                    border: 1px solid #ebeef5;

                    &.label-cell {
                        width: 180px;
                        background-color: #f5f7fa;
                        text-align: right;
                        color: #606266;
                        font-size: 14px;
                    }

                    &.accompany-person {
                        width: 120px;
                        text-align: center;
                    }
                }

                .required {
                    color: #f56c6c;
                    margin-right: 4px;
                }

                .checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 15px;
                }

                .ips-report {
                    margin-top: 10px;
                }

                .amount-input {
                    display: flex;
                    align-items: center;

                    .amount-value {
                        width: 100px;
                        margin: 0 5px;
                    }

                    .unit {
                        margin-right: 20px;
                    }

                    .foreign-checkbox {
                        margin-left: 20px;
                    }
                }
            }
        }

        .dialog-footer {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 20px;

            .custom-button {
                width: 120px;
                height: 40px;
                border-radius: 4px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s;
                border: 1px solid;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .save-button {
                background-color: #d9333f;
                color: white;
                border-color: #d9333f;

                &:hover {
                    background-color: #c62f3a;
                }

                &:active {
                    background-color: #b32a34;
                }
            }

            .cancel-button {
                background-color: white;
                color: #606266;
                border-color: #dcdfe6;

                &:hover {
                    color: #409eff;
                    border-color: #c6e2ff;
                    background-color: #ecf5ff;
                }

                &:active {
                    color: #3a8ee6;
                    border-color: #3a8ee6;
                }
            }
        }
    }
</style>
