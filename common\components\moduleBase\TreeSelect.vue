<!--
 * @Description: 树形选择
 * @Author: chaohui.wu
 * @Date: 2023-03-20 19:32:58
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-03-28 20:04:05
 * @FilePath: /crm-asset-web/src/components/moduleBase/TreeSelect.vue
 * @Link: https://element-plus.gitee.io/zh-CN/component/tree-select.html
-->
<template>
    <el-tree-select
        :class="`tree-select`"
        popper-class="crm_select_popper crm_select_popper_vue3"
        size="small"
        :collapse-tags="$attrs.multiple"
        v-bind="$attrs"
    >
        <!-- <slot name="default"></slot>
        <el-option
            v-for="item in optionList"
            :key="item[valueFormat]"
            :label="item[labelFormat]"
            :value="item[valueFormat]"
        >
            <slot name="dropItem" :scope="item"></slot>
        </el-option> -->
    </el-tree-select>
</template>

<script setup>
    import { Download, ArrowDown } from '@element-plus/icons-vue'
</script>
<style lang="less" scoped></style>
