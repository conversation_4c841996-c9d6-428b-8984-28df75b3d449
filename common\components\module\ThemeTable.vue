<!--
 * @Description: 定制table
 * @Author: chaohui.wu
 * @Date: 2023-06-20 16:43:31
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 15:04:10
 * @FilePath: /crm-template/src/components/module/ThemeTable.vue
 *  
-->
<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
    <el-table
        ref="multipleTable"
        v-loading="isLoading"
        class="theme-table"
        :class="className"
        :resizable="true"
        stripe
        :data="data"
        :height="height"
        :empty-text="isLoading ? ' ' : '暂无数据'"
        :span-method="objectSpanMethod"
        :row-class-name="handleRowClassName"
        :cell-class-name="cellClassName"
        :cell-style="styleHandle"
        :header-cell-style="handleTheadStyle"
        v-bind="$attrs"
        @select="handleSelect"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortColumns"
    >
        <el-table-column
            v-if="noSelect"
            :key="`column-select`"
            type="selection"
            width="55"
            :align="`center`"
            :fixed="pops && pops.selectFixed"
        />

        <el-table-column
            v-if="noIndex"
            :key="`column-index`"
            type="index"
            label="序号"
            width="50"
            :fixed="pops && pops.selectFixed"
        />
        <el-table-column
            v-for="(store, index) in columns"
            v-show="getTableColumnState(columns, store.key)"
            :key="`columns${index}`"
            :label="store.label"
            :prop="store.key"
            :align="store.align || 'center'"
            :min-width="store.minWidth || ''"
            :width="store.width || ''"
            :formatter="store.formatter"
            :show-overflow-tooltip="store.showOverflowTooltip !== false"
            :sortable="store.sortable"
            :sort-orders="store.sortOrders"
            :fixed="fixedHandle(store.key)"
            :filters="store.filters"
        >
            <!-- 多层表头 -->
            <template v-if="store.child">
                <el-table-column
                    v-for="(item, i) in store.child"
                    :key="`clumn-child${i}`"
                    :label="item.label"
                    :prop="item.key"
                    :align="item.align || 'center'"
                    :min-width="item.minWidth || ''"
                    :width="item.width || ''"
                    :formatter="item.formatter"
                />
            </template>
            <!-- table头部自定义 -->
            <template v-if="store.slotHeader" #header>
                <slot :name="store.slotHeaderName"></slot>
            </template>

            <!-- 内容自定义 -->
            <template v-if="store.type === 'slot'" #default="scope">
                <slot
                    :name="store.slotName"
                    :scope="{ ...scope, firstFundManager: store.firstFundManager }"
                ></slot>
            </template>
        </el-table-column>

        <el-table-column
            v-if="showOperation"
            :key="`column-operation`"
            label="操作"
            :min-width="operationWidth"
            :align="`center`"
            :fixed="operationFixed ? 'right' : false"
        >
            <template #default="scope">
                <slot name="operation" :scope="scope" />
            </template>
        </el-table-column>
    </el-table>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'BaseTable',
        props: {
            className: {
                type: [String],
                default: ''
            },
            columns: {
                type: Array,
                default: () => {
                    return []
                }
            },
            data: {
                type: Array,
                default: () => {
                    return []
                }
            },
            /**
             * table表中各列的单独配置项
             * pops.selectFixed ===> 若table中有需要固定在左侧的列  该值需要传left 将多选框和序号固定
             */
            pops: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            isLoading: {
                type: Boolean,
                default: false
            },
            objectSpanMethod: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            showOperation: {
                type: Boolean, // 是否需要操作列
                default: true
            },
            operationFixed: {
                type: Boolean, // 是否需要操作列
                default: true
            },
            noSelect: {
                type: Boolean,
                default: true
            },
            noIndex: {
                type: Boolean,
                default: true
            },
            height: {
                type: String,
                default: '100%'
            },
            operationWidth: {
                type: String,
                default: '130'
            },
            mutilSort: {
                type: Boolean,
                default: false // 是否需要多列排序，箭头高亮
            },
            // 图标
            columnIcon: {
                type: Boolean,
                default: false
            },
            // 图标宽度
            columnIconWidth: {
                type: Number,
                default: 50
            }
        },
        emits: ['selection-change', 'sort-change', 'select', 'current-change'],
        data() {
            return {
                activeThead: {} // 多列排序，箭头高亮，保存所选择的表头
            }
        },
        methods: {
            getTableColumnState(columns, key) {
                return columns.some(item => item.key === key)
            },
            // 定位
            fixedHandle(key, val) {
                if (this.pops && key) {
                    const pop = this.pops[key]
                    if (pop) {
                        if (pop.fixed) {
                            return pop.fixed
                        }
                    }
                }
                return false
            },
            // 涨红跌绿 需要在pops中透传column的key 值为对象 如{redGreen: true}
            styleHandle(item) {
                // 根据报警级别显示颜色
                const property = item.column.property
                const propertyObj = this.pops[property] ?? {}
                const { redGreen, borderRightNone, borderLeft, borderRight, bold } = propertyObj
                const val = item.row[property]
                if (redGreen) {
                    if (val && String(val).replace(/%/g, '') > 0) {
                        return { color: '#D0021B' }
                    } else if (val && String(val).replace(/%/g, '') < 0) {
                        return { color: '#2F7511' }
                    }
                }
                if (bold) {
                    return { fontWeight: bold }
                }
                if (borderRightNone) {
                    return { borderRight: 'none' }
                }

                if (borderLeft) {
                    return { borderLeft: `1px solid ${borderLeft}` }
                }
                if (borderRight) {
                    return { borderRight: `1px solid ${borderRight}` }
                }
            },
            // 列表选中事件
            handleSelectionChange(val) {
                this.$emit('selection-change', val)
            },
            handleSelect(sel, row) {
                this.$emit('select', sel, row)
            },
            handleClickTable(row) {
                this.$emit('current-change', row)
            },
            handleSortColumns(column) {
                this.$emit('sort-change', column)
                // 多列排序，箭头高亮
                if (!this.mutilSort) {
                    return
                }
                const { prop, order } = column
                this.activeThead[prop] = order || ''
            },
            // 多列排序，箭头高亮
            handleTheadStyle({ row, column, rowIndex, columnIndex }) {
                if (!this.mutilSort) {
                    return
                }
                if (this.activeThead[column.property]) {
                    column.order = this.activeThead[column.property]
                }
            },
            handerRowStyle({ row, rowIndex }) {
                return 'theme-header'
            },
            handleRowClassName(rowObj, rowIndex) {
                const { row } = rowObj
                if (row && row.lineStatus === '1') {
                    return 'off-line-bg'
                }
            },
            cellClassName({ row, column, rowIndex, columnIndex }) {
                switch (column.property) {
                    case 'dqpz':
                    case 'balanceHb':
                        return 'theme-active'
                    default:
                        break
                }
            }
        }
    })
</script>
<style lang="less" scoped></style>
