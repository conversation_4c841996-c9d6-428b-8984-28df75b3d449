/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 16:27:08
 * @FilePath: /crm-web/packages/crm-template/src/views/assetReport/reportList/scripts/tableData.ts
 *
 */
import { TableColumnItem } from '@/types/index'
import { formatTableValue } from '@common/utils/index'

/**
 * @description: table表格数据
 * @return {*}
 */
export const reportListTableColumn: TableColumnItem[] = [
    {
        key: 'fileName',
        label: '报告标题',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '所属投顾',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'orgName',
        label: '投顾所属部门',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'creDt',
        label: '创建时间',
        width: 200,
        sortable: 'custom',
        sortOrders: ['ascending', 'descending'],
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
