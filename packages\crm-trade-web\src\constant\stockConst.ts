/*
 * @Description: 存量分成枚举
 * @Author: chao<PERSON>.wu
 * @Date: 2023-08-03 15:44:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 14:08:11
 * @FilePath: /crm-web/packages/crm-performance-web/src/constant/stockConst.ts
 *
 */
import { transMapToArray } from '@common/utils'
/**
 * @description: 产品和机构类型
 * @return {*}
 */
enum IS_FLAG {
    YES = '1',
    NO = '0',
    ALL = ''
}

/**
 * @description: 分成列表枚举集合
 * @return {*}
 */
export enum CONFIG_TYPE_SELECT {
    ALL = '',
    YC = '2',
    YD_XS = '3',
    YD_GZ = '4',
    CFHZ_XS = '5',
    CFHZ_GZ = '6',
    CFHZ_XLHD = '7',
    JG_DKH = '8',
    JG_RL = '9'
}

/**
 * @description: 层级枚举枚举集合
 * @return {*}
 */
export enum CONFIG_LEVEL_SELECT {
    ALL = '',
    AFP = '1',
    FZ = '3',
    QY_FZ = '4',
    QY_Z = '5',
    XSZJ = '6'
}

/**
 * @description: 行级数据 对应的 操作列表
 * @return {*}
 */
export enum ORERATE_LIST_SELECT {
    EDIT = '3',
    DELATE = '4',
    AUDT = '5'
}

/**
 * @description: 分成列表枚举集合
 * @return {*}
 */
export const STOCK_LIST_MAP = {
    // 分成枚举
    CONFIG_TYPE_MAP: new Map([
        ['', '全部'],
        ['2', '育成'],
        ['3', '异动-协商'],
        ['4', '异动-规则'],
        ['5', '重复划转-协商'],
        ['6', '重复划转-规则'],
        ['7', '重复划转-新老划断'],
        ['8', '接管-单客户'],
        ['9', '接管-人力']
    ]),
    // 层级枚举
    CONFIG_LEVEL_MAP: new Map([
        ['', '全部'],
        ['1', '理财师'],
        ['3', '分总'],
        ['4', '区域执行副总'],
        ['5', '区域总'],
        ['6', '销售总监']
    ]),
    // 审核状态
    AYUDT_STATUS_MAP: new Map([
        ['', '全部'],
        ['0', '待审核'],
        ['1', '审核通过'],
        ['2', '审核不通过']
    ]),
    // 操作类型
    OPT_TYPE_MAP: new Map([
        ['0', '新增'],
        ['1', '修改'],
        ['2', '删除'],
        ['3', '审核']
    ]),
    // 交易状态
    TRADE_STATUS_MAP: new Map([
        [IS_FLAG.ALL, '全部'],
        [IS_FLAG.YES, '已确认'],
        [IS_FLAG.NO, '未确认']
    ]),
    //预约状态
    ORDER_STATUS_MAP: new Map([
        ['12', '正常预约(不含撤销)'],
        ['', '全部'],
        ['1', '未确认'],
        ['2', '已确认'],
        ['4', '已撤销']
    ]),
    // 打款状态
    PAY_STATUS_MAP: new Map([
        ['', '全部'],
        ['1', '未打款'],
        ['2', '已打款'],
        ['3', '到账确认'],
        ['4', '退款']
    ]),
    //交易类型:
    TRADE_TYPE_MAP: new Map([
        ['', '全部'],
        ['buy', '买入（购买+追加）'],
        ['1', '购买'],
        ['2', '追加'],
        ['3', '赎回']
    ]),
    //是否线上签约:
    IS_SIGN_ONLINE_MAP: new Map([
        ['', '全部'],
        ['0', '否'],
        ['1', '是'],
        ['2', '待确认']
    ]),
    // 签约状态
    SIGN_FLAG_MAP: new Map([
        ['', '全部'],
        [IS_FLAG.YES, '已签约'],
        [IS_FLAG.NO, '待签约']
    ]),
    // 赎回方式
    REDEEM_MAP: new Map([
        ['1', '按份额赎回'],
        ['2', '按金额赎回']
    ]),
    // 客户类型
    CUST_TYPE_MAP: new Map([
        ['1', '个人用户'],
        ['9', '基金产品'],
        ['0', '机构客户'],
        ['2', '产品客户']
    ]),
    // 币种类型
    CURRENCY_TYPE_MAP: new Map([
        ['156', '人民币'],
        ['840', '美元'],
        ['344', '港元'],
        ['954', '欧元'],
        ['392', '日元'],
        ['826', '英镑'],
        ['250', '法郎'],
        ['280', '马克']
    ])
}

/**
 * @description: 分成类型
 * @param {*} Array
 * @return {*}
 */
export const CONFIG_TYPE = transMapToArray(STOCK_LIST_MAP.CONFIG_TYPE_MAP)

/**
 * @description: 层级
 * @return {*}
 */
export const CONFIG_LEVEL = transMapToArray(STOCK_LIST_MAP.CONFIG_LEVEL_MAP)

/**
 * @description: 审核状态
 * @return {*}
 */
export const AYUDT_STATUS = transMapToArray(STOCK_LIST_MAP.AYUDT_STATUS_MAP)
/**
 * 交易状态
 */
export const TRADE_STATUS = transMapToArray(STOCK_LIST_MAP.TRADE_STATUS_MAP)

/**
 * 预约状态
 */
export const ORDER_STATUS = transMapToArray(STOCK_LIST_MAP.ORDER_STATUS_MAP)

/**
 * 付款状态
 */
export const PAY_STATUS = transMapToArray(STOCK_LIST_MAP.PAY_STATUS_MAP)

/**
 * 是否线上签约
 */
export const IS_ONLINE_SIGN = transMapToArray(STOCK_LIST_MAP.IS_SIGN_ONLINE_MAP)

/**
 * 签约状态
 */
export const SIGN_STATUS = transMapToArray(STOCK_LIST_MAP.SIGN_FLAG_MAP)

/**
 * 交易类型
 */
export const TRADE_TYPE = transMapToArray(STOCK_LIST_MAP.TRADE_TYPE_MAP)

/**
 * 交易类型
 */
export const CURRENCT_TYPE = transMapToArray(STOCK_LIST_MAP.CURRENCY_TYPE_MAP)
