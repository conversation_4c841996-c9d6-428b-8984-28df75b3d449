<!--
 * @Description: 操作明细/客户明细
 * @Author: chaohui.wu
 * @Date: 2023-04-07 10:56:38
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-12-26 09:50:02
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/custDetail.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :before-close="handleClose"
        :close-on-click-modal="false"
    >
        <template #default>
            <div class="ext-invest-index-module">
                <base-table
                    :columns="columnList"
                    :data="dataList"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    :border="true"
                    height="100%"
                    operation-width="60"
                >
                    <template #edit="{ scope }">
                        <el-button
                            v-if="operationShow(scope.row, ORERATE_LIST_SELECT.UPDATE_LIMIT)"
                            size="small"
                            :text="true"
                            link
                            @click="handleEdit(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </base-table>
            </div>
        </template>
        <!-- 删除确认 -->
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
    <EditLimit
        v-if="editLimitVisiable"
        v-model="editLimitVisiable"
        :trans-data="editLimitObj"
        @editCallback="fetchList"
    ></EditLimit>
</template>

<script setup lang="ts">
    import {
        deepClone,
        fetchRes,
        addUnit,
        formatTableValue,
        dateTrans,
        excludeArr
    } from '@common/utils/index'

    import { ORERATE_LIST_SELECT } from '@/constant/index'
    import { custDetail, operateDetail } from '@/api/project/stockSplit/stockSplitList'
    import { STOCK_LIST_MAP } from '@/constant'
    import { useVisible } from '../scripts/hooks/useVisible'
    import EditLimit from './editLimit.vue'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            transData?: {
                id: string
                title: string
                type: string
                custList?: any[]
                isAudit?: boolean
            }
        }>(),
        {
            visibleCus: true,
            transData: () => {
                return {
                    id: '',
                    title: '',
                    type: '',
                    custList: [],
                    isAudit: false
                }
            }
        }
    )
    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        console.log(val.operateVoList)
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const editLimitObj = ref({
        title: '',
        id: '',
        custNo: '',
        custName: '',
        upperLimit: ''
    })

    const editLimitVisiable = ref<boolean>(false)

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const columnList = computed(() => {
        const { type, id } = props?.transData || {}
        console.log(type)
        if (type === 'operateDetail') {
            return [
                {
                    key: 'creatorName',
                    label: '操作人',
                    width: 100,
                    formatter: formatTableValue
                },
                {
                    key: 'optType',
                    label: '操作',
                    width: 100,
                    formatter: (a: any, b: any, c: any) => {
                        return STOCK_LIST_MAP.OPT_TYPE_MAP.get(c) || '-'
                    }
                },
                {
                    key: 'createTimestamp',
                    label: '操作时间',
                    width: 150,
                    formatter: (a: any, b: any, c: any) => dateTrans(c, 'YYYY-MM-DD HH:mm:ss')
                },
                {
                    key: 'auditStatus',
                    label: '审核状态',
                    width: 120,
                    formatter: (a: any, b: any, c: any) => {
                        return c ? STOCK_LIST_MAP.AYUDT_STATUS_MAP.get(c) : '-'
                    }
                },
                {
                    key: 'auditRemark',
                    label: '审核意见',
                    minWidth: 100
                }
            ]
        }
        if (type === 'check') {
            return [
                {
                    key: 'custNo',
                    label: '投顾客户号',
                    minWidth: 100,
                    formatter: formatTableValue
                },
                {
                    key: 'custName',
                    label: '客户姓名',
                    minWidth: 100,
                    formatter: formatTableValue
                },
                {
                    key: 'upperLimit',
                    label: '分配上线_原',
                    minWidth: 100,
                    formatter: (a: any, b: any, c: any) =>
                        addUnit({ val: c, fixed: 2, unit: '%', blankPlaceholder: '-' })
                }
            ]
        }
        return [
            {
                key: 'custNo',
                label: '投顾客户号',
                minWidth: 100,
                formatter: formatTableValue
            },
            {
                key: 'custName',
                label: '客户姓名',
                minWidth: 100,
                formatter: formatTableValue
            },
            {
                key: 'upperLimit',
                label: '分配上线_原',
                minWidth: 100,
                formatter: (a: any, b: any, c: any) =>
                    addUnit({ val: c, fixed: 2, unit: '%', blankPlaceholder: '-' })
            },
            {
                key: 'operateVoList',
                label: '操作',
                slotName: 'edit',
                type: 'slot',
                minWidth: 100
            }
        ]
    })

    const dataList = ref<any>([])
    /**
     * @description: 初始化
     * @return {*}
     */
    const fetchList = () => {
        // 操作详情
        if (props?.transData?.type === 'operateDetail') {
            fetchRes(operateDetail({ id: props.transData.id }), {
                successCB: (res: any) => {
                    const { rows } = res || {}
                    dataList.value = rows || []
                },
                errorCB: () => {
                    dataList.value = []
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } else if (
            props?.transData?.type === 'customDetail' ||
            props?.transData?.type === 'check'
        ) {
            // 客户详情
            const { custList, isAudit } = props.transData || {}
            if (isAudit) {
                dataList.value = custList || []
            } else {
                fetchRes(custDetail({ id: props.transData.id }), {
                    successCB: (res: any) => {
                        const { rows } = res || {}
                        dataList.value = rows || []
                    },
                    errorCB: () => {
                        dataList.value = []
                    },
                    successTxt: '',
                    failTxt: '',
                    fetchKey: ''
                })
            }
        }
    }

    const handleEdit = (val: any) => {
        console.log(val)
        editLimitObj.value = {
            title: '修改',
            id: val.id,
            custNo: val.custNo,
            custName: val.custName,
            upperLimit: val.upperLimit
        }
        console.log(editLimitObj.value)
        editLimitVisiable.value = true
    }

    onMounted(() => {
        fetchList()
    })
</script>

<style lang="less" scoped>
    .notice-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;

        .txt-notice {
            padding: 4px 11px;
            // font-size: 12px;
            // font-family: Microsoft YaHei;
            // color: @theme_main;
            // background-color: rgba(208, 2, 27, 0.05);
        }
    }

    .ext-invest-index-module {
        display: flex;
        flex-direction: column;
        height: calc(56vh - 76px);
    }
</style>
