/*
 * @Description: el-table通用ts类
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:35:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:31:45
 * @FilePath: /crm-rpo-template/common/types/tableColumn.d.ts
 *
 */

export {}
declare module '@common/types/tableColumn' {
    interface TableColumnItem {
        /**
         * 关键字
         */
        key: string
        /**
         * 标签
         */
        label: string
        /**
         * 类型
         */
        type?: string
        /**
         * 宽度
         */
        width?: number | string
        /**
         * 最小宽度
         */
        minWidth?: number | string
        /**
         * 最大宽度
         */
        maxWidth?: number | string
        /**
         * 过滤函数
         */
        formatter?: Function
        /**
         * 插槽名
         */
        slotName?: string
        /**
         * 对齐方式
         */
        align?: string
        /**
         * 是否排序
         * 'custom' | 后端排序
         */
        sortable?: boolean | 'custom'
        /**
         * 排序点击事件
         */
        sortOrders?: any[]
        /**
         * 下拉选项
         */
        filters?: any[]
        /**
         * 是否自定义头部
         */
        slotHeader?: boolean
        slotHeaderName?: string
        showOverflowTooltip?: boolean
    }
    enum SortOrderCumstom {
        ascending = 'ASC',
        descending = 'DESC',
        default = 'DESC'
    }
    export { TableColumnItem, SortOrderCumstom }
}
