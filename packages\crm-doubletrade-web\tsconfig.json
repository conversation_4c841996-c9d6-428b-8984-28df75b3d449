{
    "compilerOptions": {
        "outDir": "./",
        "baseUrl": "./",
        "paths": {
            "@/*": ["src/*"],
            "@common/*": ["../../common/*"]
        },
        "types": ["element-plus/global"],
        "target": "ES5",
        "module": "ESNext",
        "sourceMap": true, // 生成相应的 .map文件。
        "useDefineForClassFields": true,
        "moduleResolution": "Node",
        "strict": true,
        "allowJs": true,
        "jsx": "preserve",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "esModuleInterop": true,
        "lib": ["ESNext", "DOM"],
        "skipLibCheck": true,
        "noEmit": true
    },
    "include": [
        "src/**/*.ts",
        "src/**/**/*.ts",
        "src/**/**/**/*.ts",
        "src/**/**/**/**/*.ts",
        "src/**/**/**/**/**/*.ts",
        "src/**/*.d.ts",
        "src/**/**/*.d.ts",
        "src/**/**/**/*.d.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "src/**/**/*.vue",
        "src/**/**/**/*.vue",
        "src/**/**/**/**/*.vue",
        "src/**/**/**/**/**/*.vue",
        "src/**/**/**/**/**/**/*.vue",
        "src/**/**/**/**/**/**/**/*.vue",
        "src/**/**/**/**/**/**/**/**/*.vue",
        "src/**/**/**/**/**/**/**/**/**/*.vue",
        "src/**/**/**/**/**/**/**/**/**/**/*.vue",
        "./auto-imports.d.ts",
        "./components.d.ts",
        "*.ts",
        "*.vue"
    ],
    "exclude": ["node_modules", "public", "dist", "*.test.ts"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
