<!--
 * @Description: 添加用户信息
 * @Author: chaohui.wu
 * @Date: 2023-09-12 15:22:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-02-18 16:29:35
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addCustom.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="600px"
        height="600px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 编辑的弹框 -->
            <el-form ref="ruleFormRef" :model="formList" :rules="formRules">
                <!-- 架构ID(北森) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="beisenOrgId"
                            style="margin-top: 15px; margin-bottom: 15px"
                            :required="true"
                        >
                            <LabelItemCust
                                :label="beisenOrgId.label"
                                :required="true"
                                labelwidth="200px"
                            >
                                <crm-input
                                    v-model="formList.beisenOrgId"
                                    :clearable="true"
                                    disabled="true"
                                    :style="{ width: '200px' }"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 架构名称(北森) -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="beisenOrgName"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="beisenOrgName.label"
                                :required="true"
                                labelwidth="200px"
                            >
                                <crm-select
                                    v-model="formList.beisenOrgId"
                                    :option-list="beisenOrgList"
                                    label-format="orgNameBeisen"
                                    value-format="orgIdBeisen"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    :disabled="beisenNameDisabled"
                                    :placeholder="beisenOrgName.placeholder"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 所属部门 -->
                <el-row>
                    <el-col>
                        <el-form-item prop="orgCode" style="margin-top: 15px; margin-bottom: 15px">
                            <LabelItemCust label="所属部门" :required="true" labelwidth="200px">
                                <ReleatedSelect
                                    v-model="formList.orgCons"
                                    :organization-list="organizationList"
                                    :default-org-code="orgCodeDefault"
                                    :have-cons="false"
                                    code-width="130px"
                                    :module="module"
                                    :add-all="true"
                                ></ReleatedSelect>
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 业务中心 -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="centerOrg"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="centerOrg.label"
                                :required="true"
                                labelwidth="200px"
                            >
                                <!-- <select-cust
                                    v-model="formList.centerOrg"
                                    :placeholder="centerOrg.placeholder"
                                    :option-list="centerOrg.selectList"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                /> -->
                                <crm-select
                                    v-model="formList.centerOrg"
                                    :option-list="centerOrg.selectList"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    :placeholder="centerOrg.placeholder"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 起始日期 -->
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="startDate"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <LabelItemCust
                                :label="startDate.label"
                                :required="true"
                                labelwidth="200px"
                            >
                                <el-date-picker
                                    v-model="formList.startDate"
                                    :placeholder="startDate.placeholder"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    show-format="YYYY-MM-DD"
                                    value-format="YYYYMMDD"
                                    class-name="w180"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 结束日期 -->
                <el-row>
                    <el-col>
                        <el-form-item prop="endDate" style="margin-top: 15px; margin-bottom: 15px">
                            <LabelItemCust :label="endDate.label" labelwidth="200px">
                                <el-date-picker
                                    v-model="formList.endDate"
                                    :placeholder="endDate.placeholder"
                                    :clearable="true"
                                    :style="{ width: '200px' }"
                                    show-format="YYYY-MM-DD"
                                    value-format="YYYYMMDD"
                                    class-name="w180"
                                />
                            </LabelItemCust>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="saveFn(ruleFormRef)"
                    >保 存</crm-button
                >
                <crm-button size="small" :radius="true" @click="handleClose">关 闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import ReleatedSelect from '@/views/components/common/releatedSelect.vue'
    import type { FormInstance, FormRules } from 'element-plus'
    import { useVisible } from '@/views/common/scripts/useVisible'

    import { dataList } from './../scripts/labelData'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import SelectCust from '@/views/modBusiness/pageModule/components/SelectCust.vue'
    import {
        beisenOrgConfigInsert,
        beisenOrgConfigUpdate,
        beisenOrgConfigDetail,
        queryBeisenOrg
    } from '@/api/project/beisen/beisenOrgConfig'
    import { downloadFile, fetchRes, message } from '@common/utils/index'
    const { beisenOrgName, beisenOrgId, centerOrg, startDate, endDate, orgCode } = dataList

    const module = ref<string>('B140704')
    const listLoading = ref<boolean>(false)
    const ruleFormRef = ref<FormInstance>()

    const organizationList = ref<any>([])
    const orgCodeDefault = ref<any>('')

    const beisenOrgList = ref<any>([])
    const beisenNameDisabled = ref<boolean>(false)

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            outerOrganizationList?: any[]
            outerOrgCodeDefault?: string
            transData?: {
                title: string
                type: string
                id: string
            }
        }>(),
        {
            visibleCus: false,
            outerOrganizationList: () => [],
            outerOrgCodeDefault: '',
            transData: () => {
                return {
                    title: '添加客户',
                    type: 'add',
                    id: ''
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'custCallBack', params: { val: string[]; optionList: object[] }): void
    }>()

    // hooks-useVisible
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    class FormList {
        beisenOrgName = ''
        beisenOrgId = ''
        centerOrg = ''
        matchInterval = ''
        startDate = ''
        endDate = ''
        orgCons = {
            orgCode: ''
        }
    }

    const formList = reactive<any>(new FormList())
    const formRules = reactive<FormRules<FormList>>({
        beisenOrgName: [{ required: true, message: '请输入架构名称(北森)！', trigger: 'blur' }],
        beisenOrgId: [{ required: true, message: '请输入架构ID(北森)！', trigger: 'blur' }],
        centerOrg: [{ required: true, message: '请选择业务中心！', trigger: 'blur' }],
        orgCons: [{ required: true, message: '请选择所属部门！', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择起始日期！', trigger: 'blur' }]
    })

    const saveFn = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        beisenOrgList.value.forEach((item: any) => {
            if (formList.beisenOrgId === item.orgIdBeisen) {
                formList.beisenOrgName = item.orgNameBeisen
            }
        })
        debugger
        await formEl.validate((valid: boolean, fields: any) => {
            if (valid) {
                if (formList.endDate && formList.endDate < formList.startDate) {
                    message({
                        type: 'error',
                        message: '结束日期不能小于起始日期'
                    })
                    return
                }
                const params = {
                    id: props.transData.id,
                    orgNameBeisen: formList.beisenOrgName,
                    orgIdBeisen: formList.beisenOrgId,
                    orgCode: formList.orgCons.orgCode,
                    centerOrg: formList.centerOrg,
                    startDate: formList.startDate,
                    endDate: formList.endDate
                }
                if (props.transData.id) {
                    fetchRes(beisenOrgConfigUpdate(params), {
                        successCB: (resObj: any) => {
                            listLoading.value = false
                            const { verifyMsg } = resObj
                            if (verifyMsg) {
                                message({
                                    type: 'error',
                                    message: verifyMsg
                                })
                            } else {
                                message({
                                    type: 'success',
                                    message: '成功'
                                })
                                handleClose()
                            }
                        },
                        errorCB: () => {
                            listLoading.value = false
                        },
                        catchCB: () => {
                            listLoading.value = false
                        },
                        successTxt: '',
                        failTxt: '请求失败请重试！',
                        fetchKey: ''
                    })
                } else {
                    fetchRes(beisenOrgConfigInsert(params), {
                        successCB: (resObj: any) => {
                            listLoading.value = false
                            const { verifyMsg } = resObj
                            if (verifyMsg) {
                                message({
                                    type: 'error',
                                    message: verifyMsg
                                })
                            } else {
                                message({
                                    type: 'success',
                                    message: '成功'
                                })
                                handleClose()
                            }
                        },
                        errorCB: () => {
                            listLoading.value = false
                        },
                        catchCB: () => {
                            listLoading.value = false
                        },
                        successTxt: '',
                        failTxt: '请求失败请重试！',
                        fetchKey: ''
                    })
                }
            }
        })
    }

    const initFormList = () => {
        const params = {
            id: props.transData.id
        }
        listLoading.value = true

        if (props.transData.id) {
            beisenNameDisabled.value = true
            fetchRes(beisenOrgConfigDetail(params), {
                successCB: (resObj: any) => {
                    listLoading.value = false
                    const { orgIdBeisen, centerOrg, startDate, endDate, orgCode } = resObj
                    //formList.beisenOrgName = detailVO.orgNameBeisen
                    formList.beisenOrgId = orgIdBeisen
                    formList.centerOrg = centerOrg
                    formList.startDate = startDate
                    formList.endDate = endDate
                    formList.orgCons.orgCode = orgCode
                    orgCodeDefault.value = orgCode
                },
                errorCB: () => {
                    listLoading.value = false
                },
                catchCB: () => {
                    listLoading.value = false
                },
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            })
        }

        fetchRes(queryBeisenOrg(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                beisenOrgList.value = list
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        organizationList.value = props?.outerOrganizationList
        orgCodeDefault.value = props?.outerOrgCodeDefault
        initFormList()
    })
</script>

<style lang="less" scoped></style>
