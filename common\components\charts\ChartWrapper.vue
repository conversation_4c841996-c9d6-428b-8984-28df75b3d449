<!--
 * @Description: echarts组件通用容器
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2023-06-05 17:33:14
 * @FilePath: /crm-asset-web/src/components/charts/ChartWrapper.vue
 *  
-->
<!-- echarts图表容器 -->
<template>
    <div class="chart_wrapper" :style="{ width, height }">
        <div v-if="isLoading || !!options" ref="chartRef" class="chart_container"></div>
        <p v-else class="chart_no_data">{{ isLoading ? '加载中' : '暂无数据' }}</p>
    </div>
</template>

<script setup lang="ts">
    import { watch, computed, ref, getCurrentInstance, nextTick } from 'vue'
    import customChart from './scripts/echartsConfig'
    import { ECBasicOption } from 'echarts/types/dist/shared'
    import { ECharts } from 'echarts/core'
    const { ctx: that } = getCurrentInstance() as any

    const props = withDefaults(
        defineProps<{
            width: string
            height: string
            isLoading?: boolean
            options: ECBasicOption
            chartListeners?: Function
        }>(),
        {
            width: '100%',
            height: '300px',
            isLoading: true,
            options: undefined,
            chartListeners: undefined
        }
    )

    const chartRef = ref(null)
    let myChart: ECharts
    const initCharts = () => {
        // 获取echartsDom元素
        if (chartRef.value) {
            if (myChart) {
                myChart.dispose()
            }
            myChart = customChart.init(
                chartRef.value,
                {},
                {
                    renderer: 'svg',
                    useDirtyRect: false
                }
            )
            props.options && myChart.setOption(props.options, true)
            window.addEventListener('resize', function () {
                myChart.resize()
            })
            // 初始化行为
            nextTick(() => {
                props.chartListeners && props.chartListeners(myChart)
            })
        }
    }
    watch(
        [() => props.options],
        () => {
            nextTick(() => {
                initCharts()
            })
        },
        {
            immediate: true
            // deep: true
        }
    )
</script>
<style lang="less">
    @import './styles/index';
</style>
