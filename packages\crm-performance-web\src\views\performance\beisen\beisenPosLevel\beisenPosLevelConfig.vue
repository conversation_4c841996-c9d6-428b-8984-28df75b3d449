<!--
 * @Description: 北森crm架构映射
 * @Author: jianji<PERSON>.yang
 * @Date: 2024-10-21 13:31:47
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-10-21 13:31:47
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/stockList.vue
-->
<template>
    <div class="report-list-module">
        <TableWrapCust class-name="crm_wraper" @searchFn="queryList">
            <template #searchArea>
                <table class="table-cust-select">
                    <tr>
                        <!-- 职级名称(北森) -->
                        <td>
                            <LabelItemCust :label="positionsLevelNameBeisen.label" minwidth="160px">
                                <crm-input
                                    v-model="queryForm.positionsLevelNameBeisen"
                                    :placeholder="positionsLevelNameBeisen.placeholder"
                                    :clearable="true"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <!-- 层级(CRM) -->
                        <td>
                            <LabelItemCust :label="userLevelCrm.label" minwidth="200px">
                                <crm-select
                                    v-model="queryForm.userLevelCrm"
                                    placeholder="请选择层级"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="userLevelCrmList"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <!-- 职级(CRM) -->
                        <td>
                            <LabelItemCust :label="positionsLevelCrm.label" minwidth="200px">
                                <crm-select
                                    v-model="queryForm.positionsLevelCrm"
                                    placeholder="请选择职级"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="positionsLevelCrmList"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                    <tr>
                        <!-- 副职(CRM) -->
                        <td>
                            <LabelItemCust :label="subPositionsLevelCrm.label" minwidth="200px">
                                <crm-select
                                    v-model="queryForm.subPositionsLevelCrm"
                                    placeholder="请选择副职"
                                    label-format="constName"
                                    value-format="constCode"
                                    :option-list="subPositionsLevelList"
                                    :style="{ width: '180px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <!-- 起止日期 -->
                        <td>
                            <LabelItemCust :label="matchInterval.label">
                                <date-range
                                    v-model="queryForm.matchInterval"
                                    show-format="YYYY-MM-DD"
                                    :placeholder="matchInterval.placeholder"
                                    style-type="fund"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                </table>
            </template>
            <template #operationBtns>
                <ButtonCust
                    v-show="addShow"
                    size="small"
                    plain
                    type="primary"
                    :radius="true"
                    @click="handleAdd"
                >
                    新增
                </ButtonCust>
                <ButtonCust size="small" :radius="false" plain @click.stop="queryList"
                    >查询</ButtonCust
                >
                <ButtonCust size="small" :radius="true" plain @click="resetQueryForm"
                    >清空</ButtonCust
                >
                <ButtonCust
                    v-show="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="handleExport"
                >
                    导出
                </ButtonCust>
            </template>
            <template #tableContentMiddle>
                <BaseTableCust
                    :columns="tableColumn"
                    :data="tableList"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :no-index="false"
                    :border="true"
                    operation-width="200"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="modifyShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleModify(scope.row)"
                            >修改</el-button
                        >
                        <el-button size="small" :text="true" link @click="handleDelete(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </BaseTableCust>
            </template>
        </TableWrapCust>
        <AddOrModConfig
            v-if="addOrModVisible"
            v-model="addOrModVisible"
            :trans-data="addOrModObj"
            :outer-user-level-crm-list="userLevelCrmList"
            :outer-positions-level-crm-list="positionsLevelCrmList"
            :outer-sub-positions-level-list="subPositionsLevelList"
            :outer-positions-level-crm-map="positionsLevelCrmMap"
            :out-positions-level-crm-data="positionsLevelCrmData"
            @callBack="queryList"
        ></AddOrModConfig>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download } from '@element-plus/icons-vue'
    import AddOrModConfig from './components/addOrModConfig.vue'
    import { MANAGE_BEISEN_POS_LEVEL_CONFIG_OPER_PERMISSION } from '@/constant/performanceConst'
    import { fetchRes, messageBox } from '@common/utils/index'
    import { dataList } from './scripts/labelData'
    import { posLevelConfigListTableColumn, showTableColumn } from './scripts/tableData'
    import { getMenuPermission } from '@/api/base/menuRoles'
    import {
        beisenPosLevelConfigQuery,
        beisenPosLevelConfigDelete,
        beisenPosLevelConfigExport
    } from '@/api/project/beisen/beisenPosLevel/beisenPosLevelConfig'

    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { levelConstantInit } from '@/api/project/common/common'

    const userLevelCrmList = ref([])
    const positionsLevelCrmList = computed(() => {
        queryForm.positionsLevelCrm = ''
        const ulc = queryForm.userLevelCrm
        console.log('queryForm.userLevelCrm', ulc)
        if (ulc === '') {
            return positionsLevelCrmData.value
        }
        const map1 = positionsLevelCrmMap.value
        for (const key in map1) {
            if (ulc === key) {
                const value1 = map1[key]
                return value1
            }
        }
        return []
    })
    const subPositionsLevelList = ref([])
    const positionsLevelCrmMap = ref([])
    const positionsLevelCrmData = ref([])

    const {
        positionsLevelBeisen,
        positionsLevelNameBeisen,
        userLevelCrm,
        positionsLevelCrm,
        subPositionsLevelCrm,
        matchInterval
    } = dataList

    const listLoading = ref<boolean>(false)

    const addShow = ref<boolean>(false)
    const exportShow = ref<boolean>(false)
    const modifyShow = ref<boolean>(false)
    const deleteShow = ref<boolean>(false)

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            posLevelConfigListTableColumn.map(item => item.key),
            posLevelConfigListTableColumn
        )
    })

    /**
     * @description: 新增/编辑弹框
     * @return {*}
     */
    const addOrModVisible = ref<boolean>(false)

    const addOrModObj = ref({
        title: '',
        id: '',
        type: ''
    })

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        positionsLevelBeisen = ''
        positionsLevelNameBeisen = ''
        userLevelCrm = ''
        positionsLevelCrm = ''
        subPositionsLevelCrm = ''
        matchInterval = {
            startDate: '',
            endDate: ''
        }
    }

    const queryForm = reactive(new QueryForm())

    /**
     * @description: 重置
     * @param {*} void
     * @return {*}
     */
    const resetQueryForm = (): void => {
        queryForm.positionsLevelNameBeisen = ''
        queryForm.userLevelCrm = ''
        queryForm.positionsLevelCrm = ''
        queryForm.subPositionsLevelCrm = ''
        queryForm.matchInterval.startDate = ''
        queryForm.matchInterval.endDate = ''
    }

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    const handleAdd = (): void => {
        addOrModVisible.value = true
        addOrModObj.value = {
            title: '新增',
            id: '',
            type: 'add'
        }
    }

    const handleModify = (val: any): void => {
        const { id } = val || {}
        addOrModVisible.value = true
        addOrModObj.value = {
            title: '修改',
            id: id,
            type: 'add'
        }
    }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            positionsLevelNameBeisen: queryForm.positionsLevelNameBeisen,
            userLevelCrm: queryForm.userLevelCrm,
            positionsLevelCrm: queryForm.positionsLevelCrm,
            subPositionsLevelCrm: queryForm.subPositionsLevelCrm,
            startDate: queryForm.matchInterval.startDate,
            endDate: queryForm.matchInterval.endDate
        }
        fetchRes(beisenPosLevelConfigQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                tableList.value = list
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 基于条件导出
     * @return {*}
     */
    const handleExport = async () => {
        const params = {
            positionsLevelNameBeisen: queryForm.positionsLevelNameBeisen,
            userLevelCrm: queryForm.userLevelCrm,
            positionsLevelCrm: queryForm.positionsLevelCrm,
            subPositionsLevelCrm: queryForm.subPositionsLevelCrm,
            startDate: queryForm.matchInterval.startDate,
            endDate: queryForm.matchInterval.endDate
        }
        const res: any = await beisenPosLevelConfigExport(params)
        debugger
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleDelete = (val: any): void => {
        const { id } = val || {}
        const params = {
            id: id
        }
        messageBox(
            {
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `确定删除该职级的映射信息？`
            },
            () => {
                beisenPosLevelConfigDelete(params).then(() => {
                    queryList()
                })
            },
            () => false
        )
    }

    const module = ref<string>('090302')

    const getMenuAuth = async () => {
        const params = {
            menuCode: module.value
        }
        const res: any = await getMenuPermission(params)
        const { rows } = res.data
        if (rows.length > 0) {
            rows.forEach((item: any) => {
                if (
                    item.operateCode === MANAGE_BEISEN_POS_LEVEL_CONFIG_OPER_PERMISSION.EXPORT &&
                    item.display === '1'
                ) {
                    exportShow.value = true
                }
                if (
                    item.operateCode === MANAGE_BEISEN_POS_LEVEL_CONFIG_OPER_PERMISSION.ADD &&
                    item.display === '1'
                ) {
                    addShow.value = true
                }
                if (
                    item.operateCode ===
                        MANAGE_BEISEN_POS_LEVEL_CONFIG_OPER_PERMISSION.MODIFY_SHOW &&
                    item.display === '1'
                ) {
                    modifyShow.value = true
                }
                if (
                    item.operateCode ===
                        MANAGE_BEISEN_POS_LEVEL_CONFIG_OPER_PERMISSION.DELETE_SHOW &&
                    item.display === '1'
                ) {
                    deleteShow.value = true
                }
            })
        }
    }

    const initData = async () => {
        fetchRes(levelConstantInit(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { userLevelList, posLevelList, subPosLevelList, posLevelMap } = resObj
                userLevelCrmList.value = userLevelList
                positionsLevelCrmData.value = posLevelList
                subPositionsLevelList.value = subPosLevelList
                positionsLevelCrmMap.value = posLevelMap
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description 提示
     */
    onMounted(() => {
        // 获取当前页面权限
        getMenuAuth()
        initData()
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
