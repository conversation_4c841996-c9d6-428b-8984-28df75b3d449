/*
 * @Description:
 * @Author: chaohui.wu
 * @Date: 2023-07-27 11:00:22
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-27 11:24:45
 * @FilePath: /crm-web/common/components/charts/scripts/resize.ts
 *
 */
/**
 * vue mixin 窗口大小变化的时候更新图表样式
 */

import { debounce } from './methods'

export default {
    props: {
        // 当前图表是否处于被选中的tab页面，用于切换tab的时候resize图表，避免图表宽度压缩
        inActiveTab: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            _sidebarElm: null,
            _resizeHandler: null
        }
    },
    watch: {
        inActiveTab(newVal) {
            newVal && this._resizeHandler()
        }
    },
    mounted() {
        this._resizeHandler = debounce(
            () => {
                this.chart && this.chart.resize()
                this.rsquareChart && this.rsquareChart.resize()
            },
            50,
            false
        )
        this._initResizeEvent()
        this._initSidebarResizeEvent()
    },
    beforeUnmount() {
        this._destroyResizeEvent()
        this._destroySidebarResizeEvent()
    },
    // to fixed bug when cached by keep-alive
    // https://github.com/PanJiaChen/vue-element-admin/issues/2116
    activated() {
        this._initResizeEvent()
        this._initSidebarResizeEvent()
    },
    deactivated() {
        this._destroyResizeEvent()
        this._destroySidebarResizeEvent()
    },
    methods: {
        // use $_ for mixins properties
        // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential
        _initResizeEvent() {
            window.addEventListener('resize', this._resizeHandler)
        },
        _destroyResizeEvent() {
            window.removeEventListener('resize', this._resizeHandler)
        },
        _sidebarResizeHandler(e) {
            if (e.propertyName === 'width') {
                this._resizeHandler()
            }
        },
        _initSidebarResizeEvent() {
            this._sidebarElm = document.getElementsByClassName('sidebar-container')[0]
            this._sidebarElm &&
                this._sidebarElm.addEventListener('transitionend', this._sidebarResizeHandler)
        },
        _destroySidebarResizeEvent() {
            this._sidebarElm &&
                this._sidebarElm.removeEventListener('transitionend', this._sidebarResizeHandler)
        }
    }
}
