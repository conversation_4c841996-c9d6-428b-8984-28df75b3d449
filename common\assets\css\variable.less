/*
 * @Description: 主题基础变量可全局引用
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-01 14:22:54
 * @FilePath: /crm-web/common/assets/css/variable.less
 *  
 */

@import './utils.less';

@max_content_width: calc(~'100% - 20px'); // 内容最大宽度
@max_dialog_height: 70vh; // 弹框最大高度
@min_content_width: 500px; // 内容最大宽度
@header_bar_height: 52px; // 顶部栏的高度
@tags_view_bar_height: 35px; // 访问历史tab栏高度
@template_spacing: 30px; // 模板页面左右间距

// @theme_main: #c82d30; // 主题色
// @theme_main_hover: #d94848; // 主题hover
@theme_main: #32596a; // 主题色
@theme_main_hover: #0e87bb; // 主题hover
@bg_main_hover: rgba(50, 89, 106, 0.1); // hover背景色
@bg_main_active: rgba(14, 135, 187, 0.1); // hover背景色

@theme_main_blue: #32596a; // 主题色
@theme_main_hover_blue: #f8f8f8; // 主题hover

@theme_rise: #c82d30; // 数据涨幅
@theme_down: #018800; // 数据跌幅

@bg_main: #f5f6f9; // 页面背景色
@bg_main_table: #fdf6f6; // 主题表格
@bg_main_hover_blue: rgba(200, 45, 48, 0.1); // hover背景色
@bg_main_table_blue: #f8f8f8; // 主题表格
@bg_main_selected: #ffffff; // 选中背景色
@bg_main_01: #eff1f5; // 背景色弹框
@bg_main_02: #e4e5ec; // 页面背景色浅色表头
@bg_main_03: #333335; // 页面背景色深色色表头
@bg_main_04: #f1f9fe; // 文字提示背景色
@bg_main_05: #3a3e5b; // 报告编辑页顶部背景色
@bg_main_06: #d0021b; // 标签背景色
@bg_main_07: rgba(208, 2, 27, 0.1); // 标签背景色
@bg_main_08: #ffdfdf; // 红色文字标签背景色
@bg_main_09: #f7f7f7; // 说明背景

@border_color: #f5f6f9; // 分割线
@border_color_01: #eaeaea; // 分割线
@border_color_02: #71717b; // 默认border
@border_color_03: #dcdfe6; // 默认border
@border_color_04: #979797; // 资配顶部按钮边框
@border_color_05: #dfdfdf; // tips文字边框
@border_color_06: #ffffff; // tips文字边框
@border_color_06: #d5d6d9; // 富文本编辑器边框
@border_focus: #696c82; // focus颜色
@border_hover: #c0c4cc; // hover颜色

@font_color: #2a3050; // 主标题字体颜色
@font_color_01: #ffffff; // 标题字体颜色
@font_color_02: #434549; // 主内容/输入项/输入框
@font_color_03: #c0c4cc; // 注释辅助说明文字
@font_color_04: #252b3d; // table表格表头
@font_color_05: #333333; // 表格内容/分页/搜索组件字体颜色
@font_color_06: #c01b26; // tab切换选中颜色
@font_color_07: #d0021b; // 红色提示文字标签/开关打开
@font_color_08: #666666; // 灰色提示文字标签
@font_color_09: #3a3e5b; // 无数据灰色提示
@font_color_10: #9497a7; // 无数据灰色提示
@font_color_11: #535353; // 无数据灰色提示
@font_color_12: #7f7f7f; // 计算说明文字颜色
@font_link_more: #5a66cb; // 大片超文本链接
@font_link: #5362eb; // 超文本
@font_tip: #fb6922; // 提示性文字
@font_notice: #999999; // 公告提醒文字
@font_success: #018800; // 成功提示
@font_red_light: #e57471; // 高亮提示文字
