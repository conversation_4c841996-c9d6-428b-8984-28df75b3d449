<!--
 * @Description: 添加页/编辑/审核
 * @Author: chaohui.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 19:47:30
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/addStock.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="900px"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :before-close="dialogHandleClose"
        :close-on-click-modal="false"
    >
        <div id="download2Pdf" class="stock-form-module">
            <el-form
                ref="dialogForm"
                class="crm_dialog_form"
                label-width="140px"
                :model="formList"
                :disabled="false"
                :scroll-to-error="true"
                :inline="true"
                validate-on-rule-change
                @submit.prevent
            >
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="客户姓名"
                    prop="custName"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.custName"
                        :disabled="true"
                        :clearable="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="客户编号"
                    prop="custNo"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.custNo"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="电话号码"
                    prop="phone"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.phone"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="证件号"
                    prop="idNo"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.idNo"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                    <base-button
                        size="small"
                        :text="true"
                        :underline="false"
                        link
                        @click="getmobile(formList.preBookId)"
                        >查看</base-button
                    >
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="产品名称"
                    prop="productName"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.productName"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="产品代码"
                    prop="productCode"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.productCode"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == '2' && formList.onlineSignFlag == '是'"
                    label="赎回方式"
                    prop="redeemMethod"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.redeemMethod"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == '2' && formList.onlineSignFlag == '是'"
                    label="赎回去向"
                    prop="redeemDirection"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.redeemDirection"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 && formList.onlineSignFlag == '是'"
                    label="支付方式"
                    prop="payMentType"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.payMentType"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="交易类型"
                    prop="tradeType"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.tradeType"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="客户类型"
                    prop="custType"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.custType"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.tradeMode == 2"
                    label="币种"
                    prop="currency"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.currency"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.redeemMethod == '按金额赎回'"
                    label="申请金额"
                    prop="appAmt"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.appAmt"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1 || formList.redeemMethod == '按份额赎回'"
                    label="申请份额"
                    prop="appVol"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.appVol"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="业务类型" prop="busiType" :inline-message="true">
                    <crm-input
                        v-model="formList.busiType"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="交易状态" prop="tradeStatus" :inline-message="true">
                    <crm-input
                        v-model="formList.tradeStatus"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="是否线上签约" prop="onlineSignFlag" :inline-message="true">
                    <crm-input
                        v-model="formList.onlineSignFlag"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="签约状态" prop="signFlag" :inline-message="true">
                    <crm-input
                        v-model="formList.signFlag"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="交易时间" prop="expectTradeDt" :inline-message="true">
                    <crm-input
                        v-model="formList.expectTradeDt"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="电邮地址" prop="email" :inline-message="true">
                    <crm-input
                        v-model="formList.email"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
                <el-form-item
                    v-if="formList.tradeMode == 1"
                    label="手续费率"
                    prop="feeRate"
                    :inline-message="true"
                >
                    <crm-input
                        v-model="formList.feeRate"
                        :disabled="true"
                        show-word-limit
                        type="text"
                        :style="{ width: '200px' }"
                        resize="none"
                    />
                </el-form-item>
            </el-form>
            <MobileDetail
                v-if="detailStockVisiable"
                v-model="detailStockVisiable"
                :trans-data="stockObj"
            ></MobileDetail>
            <base-table
                v-if="formList.showBankInfo == true"
                :columns="bankColumnList"
                :data="bankdataList"
                style="width: 100%"
                :show-operation="false"
                :no-select="false"
                :no-index="false"
                :stripe="true"
                :border="true"
                height="100%"
                operation-width="60"
            >
            </base-table>
            <el-table :data="dataList">
                <el-table-column prop="fileTypeName" label="客户查看/签约材料名称" width="250">
                    <template #default="{ row }">
                        {{ row.fileTypeName }}
                        <span v-if="row.isRequire === '1'" style="color: red">*</span>
                    </template>
                </el-table-column>
                <el-table-column prop="fileName" label="资料" width="300">
                    <template #default="{ row }">
                        <a
                            :href="row.filePath"
                            target="_blank"
                            style="color: blue; text-decoration: underline"
                            >{{ row.fileName }}</a
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="valid" label="材料合格" width="300">
                    <template #default="{ row }">
                        {{ row.valid }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="dialogHandleClose"
                    >关 闭</crm-button
                >
                <crm-button size="small" :radius="true" @click="export2Pdf">导 出</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import type { FormInstance, FormRules } from 'element-plus'
    import html2canvas from 'html2canvas'
    import jsPDF from 'jspdf'
    import {
        deepClone,
        fetchRes,
        message,
        messageBox,
        excludeArr,
        fetchAll,
        zeroTrans,
        addUnit,
        dateTrans,
        formatTableValue
    } from '@common/utils/index'
    import ReleatedSelect from './releatedSelect.vue'
    // import CustDetail from './custDetail.vue'
    import { queryHkTradeInfo, queryMobile } from '@/api/project/stockSplit/stockSplitList'

    import { CONFIG_LEVEL, CONFIG_TYPE_SELECT, STOCK_LIST_MAP } from '@/constant/index'
    import { useDiaVisible } from '../scripts/hooks/useDiaVisible'
    import { TradeInfoReq } from '@/api/project/stockSplit/type/apiReqType'
    import MobileDetail from './mobileDetail.vue'
    import { ElMessage } from 'element-plus'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            consCustNo?: string
            consCode?: string
            orgCode?: string
            formerConsCode?: string
            formerOrgCode?: string
            consultList?: any[]
            organizationList?: any[]
            formerConsultList?: any[]
            formerOrganizationList?: any[]
            transData?: {
                [x: string]: string
                title: string
                type: string
                id: string
                custNo: string
            }
        }>(),
        {
            visibleCus: true,
            consCustNo: '',
            consCode: '',
            orgCode: '',
            formerConsCode: '',
            formerOrgCode: '',
            consultList: () => [],
            organizationList: () => [],
            formerConsultList: () => [],
            formerOrganizationList: () => [],
            transData: () => {
                return {
                    title: '新增',
                    type: 'add',
                    id: '',
                    custNo: ''
                }
            }
        }
    )

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const columnList = computed(() => {
        return [
            {
                key: 'fileTypeName',
                label: '客户查看/签约材料名称',
                width: 250
            },

            {
                key: 'fileName',
                label: '资料',
                width: 300
            },
            {
                key: 'valid',
                label: '材料合格',
                width: 300
            }
        ]
    })

    /**
     * @description: 列表处理
     * @param {*} computed
     * @return {*}
     */
    const bankColumnList = computed(() => {
        return [
            {
                key: 'bankName',
                label: '银行名称',
                width: 250,
                formatter: formatTableValue
            },
            {
                key: 'bankAcctNo',
                label: '账户号码',
                width: 300
            }
        ]
    })

    let dataList = ref<any>([])
    let bankdataList = ref<any>([])

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    /**
     * @description: 详情
     * @return {*}
     */
    const stockObj = {
        phone: ''
    }
    /**
     * @description: 添加客户信息
     * @return {*}
     */
    const addCustomStockVisiable = ref<boolean>(false)
    const showAddCustom = () => {
        addCustomStockVisiable.value = true
    }

    /**
     * @description: form校验规则
     * @return {*}
     */
    const listStr = [
        'configType',
        'validFlag',
        'configLevel',
        'formerConstObj',
        'constObj',
        'activeFlag',
        'calStartDt',
        'calEndDt',
        'custNoList',
        'formerCalRate',
        'newlyCalRate'
    ]
    const rulesList = ref(listStr)

    class FormList {
        id = ''
        configType = ''
        constObj = {
            orgCode: '',
            consCode: '',
            consList: []
        }
        formerConstObj = {
            orgCode: '',
            consCode: '',
            consList: []
        }
        calDt = {
            startDate: '',
            endDate: ''
        }
        custName = ''
        productName = ''
        configLevel = ''
        custNo = ''
        auditStatus = ''
        validFlag = '1'
        activeFlag = ''
        calStartDt = ''
        calEndDt = ''
        custNoList = []
        activeDt = '' // 激活时间
        formerCalRate = null
        newlyCalRate = null
        lockDurationAmt = ''
        remark = ''
        audtAdvice = ''
    }

    const formList = ref<any>(new FormList())
    /**
     * @description: hooks
     * @return {*}
     */
    const { dialogVisible, moduleTyping, getParams, handleClose, verifyMethods } = useDiaVisible({
        emit,
        props,
        formList
    })

    /**
     * @description: 关闭弹框
     * @return {*}
     */
    const dialogForm = ref<FormInstance>()
    const dialogHandleClose = () => {
        // 关闭弹框后逻辑
        handleClose({
            msgInfo: '取消',
            callBack: () => {
                if (!dialogForm.value) {
                    return
                }
                dialogForm.value.resetFields()
            }
        })
    }

    /**
     * @description: 层级枚举联动
     * @param {*} computed
     * @return {*}
     */
    const configLevelMap = computed(() => {
        switch (formList.value.configType) {
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
                return excludeArr(CONFIG_LEVEL, [''])
            default:
                return excludeArr(CONFIG_LEVEL, ['', '1'])
        }
    })

    /**
     * @description: form表单禁用联动
     * @param {*} computed
     * @return {*}
     */
    const formDisableKeyArr = computed<string[]>(() => {
        let curStrList = ['activeFlag', 'activeDt']
        const editDisableArr = ['configType', 'configLevel', 'audtAdvice']
        if (moduleTyping.value.isAdd) {
            curStrList = curStrList.filter(item => !curStrList.includes(item))
        } else if (moduleTyping.value.isEdit) {
            curStrList = [...editDisableArr]
        } else if (moduleTyping.value.isCheck) {
            curStrList = [
                ...listStr,
                'formerConstObj',
                'constObj',
                'activeFlag',
                'activeDt',
                'formerCalRate',
                'newlyCalRate',
                'lockDurationAmt',
                'remark'
            ]
        }
        switch (formList.value.configType) {
            case CONFIG_TYPE_SELECT.JG_RL:
                // 当 「分成类型」=接管-人力 时，该字段可编辑，必填项；
                rulesList.value = listStr
                return curStrList
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
            default:
                // 当「分成类型」=！接管-人力 时，该字段置灰不可编辑，非必填项
                rulesList.value = listStr.filter(item => !curStrList.includes(item))
                return curStrList
        }
    })

    /**
     * @description: form表单展示联动
     * @param {*} computed
     * @return {*}
     */
    const formShowKeyArr = computed((): string[] => {
        const listKey = [
            'configType',
            'validFlag',
            'configLevel',
            'formerConstObj',
            'constObj',
            'activeFlag',
            'activeDt',
            'calStartDt',
            'calEndDt',
            'custNoList',
            'formerCalRate',
            'newlyCalRate',
            'lockDurationAmt',
            'remark',
            'audtAdvice'
        ]
        const { configType } = formList.value || {}
        // add
        let curStrList = ['activeFlag', 'activeDt', 'audtAdvice']
        const ratroList = ['formerCalRate', 'newlyCalRate']
        // edit
        if (moduleTyping.value.isEdit) {
            curStrList = ['activeFlag', 'activeDt']
        }
        // audt
        if (moduleTyping.value.isCheck) {
            curStrList = []
        }
        let showKeyArr = []
        switch (configType) {
            case CONFIG_TYPE_SELECT.YC:
                // 当 「分成类型」=育成，该字段置灰不可编辑，非必填；当「分成类型」！=育成，必填
                curStrList.push('custNoList')
                showKeyArr = listKey.filter(item => !curStrList.includes(item))
                break
            case CONFIG_TYPE_SELECT.JG_RL:
                showKeyArr = listKey
                break
            case CONFIG_TYPE_SELECT.YD_XS:
            case CONFIG_TYPE_SELECT.CFHZ_XS:
            case CONFIG_TYPE_SELECT.CFHZ_GZ:
            case CONFIG_TYPE_SELECT.CFHZ_XLHD:
            default:
                // 当「分成类型」=！接管-人力 时，该字段置灰不可编辑，非必填项
                showKeyArr = listKey.filter(item => !curStrList.includes(item))
                break
        }

        switch (configType) {
            case CONFIG_TYPE_SELECT.YC:
            case CONFIG_TYPE_SELECT.YD_XS:
            case CONFIG_TYPE_SELECT.CFHZ_XS:
                break
            default:
                showKeyArr = showKeyArr.filter(item => !ratroList.includes(item))
                break
        }

        return showKeyArr
    })

    /**
     * @description: 展示选项或者禁止选择
     * @param {*} curKey
     * @param {*} type 1 禁止 默认展示
     * @return {*}
     */
    const isShowFlag = (curKey: string, type?: '1') => {
        switch (type) {
            case '1':
                return unref(formDisableKeyArr).includes(curKey)
            default:
                return formShowKeyArr.value.includes(curKey)
        }
    }

    /**
     * @description: 清空level选项
     * @return {*}
     */
    const initLevel = () => {
        formList.value.configLevel = ''
    }

    /**
     * @description: 层级联动交互
     * @return {*}
     */
    // const initConfigLevel = () => {
    //     if (moduleTyping.value.isAdd) {
    //         // 切换层级后重置客户默认下拉列表
    //         custOptions.value = []
    //         formList.value.custNoList = []
    //     }
    // }

    /**
     * @description: 获取用户信息
     * @param {*} query
     * @return {*}
     */
    const custOptions = ref<{ label: string; value: string }[]>([])

    /**
     * @description: 重复数组
     * @param {*} tempArr
     * @return {*}
     */
    const repatArr = (tempArr: any[], key: string) => {
        const newArr = []
        for (let i = 0; i < tempArr.length; i++) {
            if (newArr.indexOf(tempArr[i][key]) === -1) {
                newArr.push(tempArr[i][key])
            } else {
                tempArr.splice(i, 1)
            }
        }
        return tempArr
    }

    const detailStockVisiable = ref<boolean>(false)

    const getmobile = (id: string) => {
        if (window.confirm('是否确认进行操作？')) {
            fetchRes(
                queryMobile({
                    tradeId: id
                }),
                {
                    successCB: (res: any) => {
                        stockObj.phone = res.mobile
                        detailStockVisiable.value = true
                    },
                    errorCB: (res: any) => {
                        ElMessage({
                            type: 'error',
                            message: res.description
                        })
                    },
                    catchCB: () => {
                        ElMessage({
                            type: 'error',
                            message: '该用户没有对应权限'
                        })
                    },
                    successTxt: '',
                    failTxt: '',
                    fetchKey: ''
                }
            )
        }
    }

    /**
     * @description: 审核初始化
     * @return {*}
     */
    // eslint-disable-next-line vue/no-setup-props-destructure
    const preBookId = props.transData.id
    // eslint-disable-next-line vue/no-setup-props-destructure
    const custNo = props.transData.custNo
    const tradeinfo: TradeInfoReq = {
        tradeId: preBookId,
        custNo: custNo
    }

    const getString = (val: number | string | undefined | null) => {
        return val === 0 ? '0' : val ? '' + val : ''
    }
    const audtList = () => {
        fetchRes(queryHkTradeInfo(tradeinfo), {
            successCB: (res: any) => {
                // 编辑初始化
                const { fileVoList, bankCardVOList } = res || {}
                const tradeType = STOCK_LIST_MAP.TRADE_TYPE_MAP.get(res.tradeType)
                const custType = STOCK_LIST_MAP.CUST_TYPE_MAP.get(res.custType)
                const currency = STOCK_LIST_MAP.CURRENCY_TYPE_MAP.get(res.currency)
                const tradeStatus = STOCK_LIST_MAP.TRADE_STATUS_MAP.get(res.tradeStatus)
                const onlineSignFlag = STOCK_LIST_MAP.IS_SIGN_ONLINE_MAP.get(res.onlineSignFlag)
                const signFlag = STOCK_LIST_MAP.SIGN_FLAG_MAP.get(res.signFlag)
                const redeemMethod = STOCK_LIST_MAP.REDEEM_MAP.get(res.redeemMethod)
                dataList = fileVoList
                bankdataList = bankCardVOList
                const preBookId = res.preBookId
                const appAmt = getString(res.appAmt)
                    .toString()
                    .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
                const appVol = getString(res.appVol)
                    .toString()
                    .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
                // 处理银行卡显示与否
                // 交易类型
                const tradeMode = res.tradeMode
                // 支付方式
                const payMentType = res.payMentType
                // 赎回去向
                const redeemDirection = res.redeemDirection
                let showBankInfo = false
                console.log(payMentType.indexOf('dfhkashklj'))
                if (
                    (tradeMode === '1' && payMentType.indexOf('电汇') === 0) ||
                    (tradeMode === '2' && redeemDirection.indexOf('回银行卡') === 0)
                ) {
                    showBankInfo = true
                }
                debugger
                formList.value = {
                    ...res,
                    tradeType,
                    custType,
                    currency,
                    tradeStatus,
                    onlineSignFlag,
                    signFlag,
                    redeemMethod,
                    preBookId,
                    appAmt,
                    appVol,
                    showBankInfo
                }
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 失去焦点重置数字输入框
     * @param {*} key
     * @return {*}
     */
    const handleCurNum = (key: string) => {
        const numTrans = Number(formList.value[key].replace(/\$\s?|(,*)/g, ''))
        formList.value[key] = numTrans && numTrans > 0 ? numTrans.toFixed(2) : ''
    }

    const export2Pdf = () => {
        const divToExport = document.getElementById('download2Pdf')
        const custName = formList.value.custName
        const fundName = formList.value.productName
        html2canvas(divToExport).then((canvas: { toDataURL: (arg0: string) => string }) => {
            const image = canvas.toDataURL('image/png')
            const pdf = new jsPDF()
            const img = new Image()
            img.src = image
            img.onload = function () {
                const desiredWidth = 200 // 设置所需的宽度
                const scaleFactor = desiredWidth / img.width // 计算缩放比例
                const newWidth = img.width * scaleFactor // 计算新的宽度
                const newHeight = img.height * scaleFactor // 计算新的高度

                const imageOptions = {
                    imageData: img,
                    x: 0,
                    y: 0,
                    width: newWidth,
                    height: newHeight
                }

                pdf.addImage(imageOptions)
                const fileName = custName + '-' + fundName + '-' + '交易单.pdf'
                pdf.save(fileName)
            }
        })
    }
    onBeforeMount(() => {
        audtList()
    })
</script>

<style lang="less" scoped>
    .is-no-data {
        padding: 15vh 0;
        text-align: center;
    }

    .txt-red {
        margin-left: 15px;
        font-size: 12px;
        color: red;
    }

    .cust-list-box {
        display: flex;
        align-items: center;
    }

    :deep(.el-select__tags) {
        .el-tag__content {
            max-width: 28px;
            overflow: hidden;
        }
    }

    .search-box {
        width: 40px;
        height: 24px;
        padding-left: 15px;
        font-size: 18px;
        color: @font_link_more;
        cursor: pointer;
    }

    .crm_dialog_form {
        width: 100%;
        height: 100%;

        .el-form-item {
            margin-bottom: 10px;

            :deep(.el-input-number) {
                text-align: left;
            }
        }

        :deep(.el-input) {
            .el-input__wrapper {
                padding-left: 10px;

                .el-input__inner {
                    text-align: left;
                }
            }
        }
    }
</style>
