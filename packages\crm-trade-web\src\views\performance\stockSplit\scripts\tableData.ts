/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 16:57:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/tableData.ts
 *
 */
import { TableColumnItem } from '@/types/index'
import {
    formatTableValue,
    dateTrans,
    addUnit,
    formatThousandsForTableValue,
    formateDateForTableValue,
    formatThousandsVOLForTableValue,
    formatThousandsAMTForTableValue
} from '@common/utils/index.js'
import { STOCK_LIST_MAP } from '@/constant'
import { formatter } from 'element-plus'

/**
 * @description: table表格数据
 * @return {*}
 */
export const stockListTableColumn: TableColumnItem[] = [
    {
        key: 'custName',
        label: '客户姓名',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'hkCustId',
        label: '客户编号',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'productName',
        label: '产品名称',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'productCode',
        label: '产品代码',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'tradeType',
        label: '交易类型',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.TRADE_TYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'orderStatus',
        label: '预约状态',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.ORDER_STATUS_MAP.get(c) : ''
        }
    },
    {
        key: 'isOnlineSign',
        label: '是否线上签约',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.IS_SIGN_ONLINE_MAP.get(c) : ''
        }
    },
    {
        key: 'signStatus',
        label: '签约状态',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.SIGN_FLAG_MAP.get(c) : ''
        }
    },
    {
        key: 'signDt',
        label: '签约时间',
        minWidth: 100,
        formatter: formateDateForTableValue
    },
    {
        key: 'payStatus',
        label: '打款状态',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.PAY_STATUS_MAP.get(c) : ''
        }
    },
    {
        key: 'tradeStatus',
        label: '交易状态',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.TRADE_STATUS_MAP.get(c) : ''
        }
    },
    // 币种枚举
    {
        key: 'currency',
        label: '币种',
        minWidth: 100,
        formatter: (a: any, b: any, c: any) => {
            return c ? STOCK_LIST_MAP.CURRENCY_TYPE_MAP.get(c) : ''
        }
    },
    {
        key: 'appAmt',
        label: '申请金额',
        minWidth: 80,
        formatter: formatThousandsVOLForTableValue
    },
    {
        key: 'appVol',
        label: '申请份额',
        minWidth: 100,
        formatter: formatThousandsAMTForTableValue
    },
    {
        key: 'ackAmt',
        label: '确认金额',
        minWidth: 100,
        formatter: formatThousandsVOLForTableValue
    },
    {
        key: 'ackVol',
        label: '确认份额',
        minWidth: 100,
        formatter: formatThousandsAMTForTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
