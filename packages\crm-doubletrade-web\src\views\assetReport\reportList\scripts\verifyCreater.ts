/*
 * @Description: 创建人角色校验
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-23 10:35:27
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:33:17
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/verifyCreater.ts
 *
 */
import { defineStore } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { checkCustAuth } from '@/api/project/reportList/reportListAll'
import { CheckCustAuthVo } from '@/api/project/reportList/type/apiType'

export const useVerifyCreater = defineStore('verifyCreater', {
    state: () => ({
        dialogVisible: true,
        verifyCodes: [
            {
                verify: '0000',
                msg: '成功',
                callBackFunc: 'successCallBack'
            },
            {
                verify: 'C020000',
                msg: '成功',
                callBackFunc: 'successCallBack'
            },
            {
                verify: '1010',
                msg: '未查询到客户'
            },
            {
                verify: 'C021010',
                msg: '未查询到客户'
            },
            {
                verify: '1020',
                msg: '该功能目前不对基金产品客户开放'
            },
            {
                verify: 'C021020',
                msg: '该功能目前不对基金产品客户开放'
            },
            {
                verify: '1030',
                msg: '客户当前没有一账通号，暂不支持创建资配报告'
            },
            {
                verify: 'C021030',
                msg: '客户当前没有一账通号，暂不支持创建资配报告'
            },
            {
                verify: '1040',
                msg: '该客户未完成风险评测，请先完成风险评测后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: 'C021040',
                msg: '该客户未完成风险评测，请先完成风险评测后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: '1041',
                msg: '该客户风险测评已过期，请先完成风险测评后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: 'C021041',
                msg: '该客户风险测评已过期，请先完成风险测评后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: '1050',
                msg: '该客户风险等级为C0，仅适合配置货币基金，不支持创建资配报告，请重新调整客户风险等级后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: 'C021050',
                msg: '该客户风险等级为C0，仅适合配置货币基金，不支持创建资配报告，请重新调整客户风险等级后再创建报告',
                confirmButtonText: '去测评',
                callBackFunc: 'showRiskQuestionnaire'
            },
            {
                verify: '1060',
                msg: '该客户当前无任何持仓，请设置客户的最大可投资金额',
                confirmButtonText: '去调整',
                callBackFunc: 'showAmountAdjustment'
            },
            {
                verify: 'C021060',
                msg: '该客户当前无任何持仓，请设置客户的最大可投资金额',
                confirmButtonText: '去调整',
                callBackFunc: 'showAmountAdjustment'
            }
        ]
    }),
    getters: {
        getCurVerify: state => {
            return (code: string) => state.verifyCodes.filter(item => item.verify === code)
        }
    },
    actions: {
        async verifyMethod(params: CheckCustAuthVo, callBack: Function) {
            const custAuthRes: any = await checkCustAuth(params)
            const { conscustno } = params
            const { verify, msg, assetId, conscustNo } = custAuthRes?.data || {}
            const msgObj = this.getCurVerify(verify)[0]
            switch (verify) {
                case '1010':
                case 'C021010':
                case '1020':
                case 'C021020':
                case '1030':
                case 'C021030':
                    ElMessage({
                        type: 'warning',
                        message: msgObj.msg
                    })
                    break
                case '1040':
                case 'C021040':
                case '1041':
                case 'C021041':
                case '1050':
                case 'C021050':
                case '1060':
                case 'C021060':
                    ElMessageBox.confirm(msgObj.msg, '提示', {
                        confirmButtonText: msgObj.confirmButtonText,
                        cancelButtonText: '取消',
                        customClass: 'blank-message-box',
                        center: true
                    })
                        .then(() => {
                            callBack && callBack({ assetId, conscustNo, ...msgObj })
                        })
                        .catch(() => {
                            ElMessage({
                                type: 'warning',
                                message: '取消'
                            })
                        })
                    break
                case '0000':
                case 'C020000':
                    callBack &&
                        callBack({
                            assetId,
                            conscustNo,
                            ...msgObj
                        })
                    break
                case '0001':
                case 'C020001':
                case '0002':
                case 'C020002':
                default:
                    break
            }
        }
    }
})
