import Plugin from '@ckeditor/ckeditor5-core/src/plugin'
import ButtonView from '@ckeditor/ckeditor5-ui/src/button/buttonview'
import FullIcon from './images/full.svg'
const IconStr = 'fullPage'

export default class FullPage extends Plugin {
    containerEl
    isFullscreen = false
    toggleFull() {
        if (this.isFullscreen) {
            this.containerEl.classList.remove('editor-fix')
            this.containerEl.querySelector('.ck-editor__main').style.maxHeight = 'initial'
            this.containerEl.querySelector('.ck-editor__main').style.overflowY = 'auto'
        } else {
            this.containerEl.classList.add('editor-fix')
            // eslint-disable-next-line no-debugger
            // debugger
            const offsetHeight = this.containerEl.offsetHeight - 40
            this.containerEl.querySelector('.ck-editor__main').style.maxHeight = offsetHeight + 'px'
            this.containerEl.querySelector('.ck-editor__main').style.overflowY = 'scroll'
        }
    }
    init() {
        const editor = this.editor
        this.containerEl = editor.config.get('fullPage').el
        if (!this.containerEl) {
            throw Error('必要配置 el 缺失')
        }
        if (typeof this.containerEl === 'string') {
            this.containerEl = document.querySelector(this.containerEl)
        }
        editor.ui.componentFactory.add(IconStr, locale => {
            // const command = editor.commands.get(IconStr)

            const view = new ButtonView(locale)

            view.set({
                label: '铺满全页',
                icon: FullIcon,
                tooltip: true,
                isToggleable: true
            })
            view.bind('isOn', 'isEnabled')
            // .to( command, 'value', 'isEnabled' );
            //
            // Callback executed once the image is clicked.
            view.on('execute', () => {
                this.toggleFull(this.isFullscreen)
                this.isFullscreen = !this.isFullscreen
                editor.config.get('fullPage').toggleFull(this.isFullscreen)

                view.set({
                    isOn: this.isFullscreen,
                    label: !this.isFullscreen ? '铺满全页' : '还原'
                })

                // const imageUrl = prompt('Image URL')

                // editor.model.change(writer => {
                //     const imageElement = writer.createElement('imageBlock', {
                //         src: imageUrl
                //     })

                //     // Insert the image in the current selection location.
                //     editor.model.insertContent(imageElement, editor.model.document.selection)
                // })
            })

            return view
        })
    }
}
