/*
 * @Author: xing.zhou
 * @Date: 2021-12-21 17:26:05
 * @LastEditTime: 2023-07-24 10:12:12
 * @LastEditors: chaohui.wu
 * @Description: 消息中心 -- api
 */
import { axiosRequest } from '@common/utils/index'

// 消息列表
export function getMsgList(params: any) {
    return axiosRequest({
        url: '/news/web/info/page',
        method: 'get',
        params
    })
}

// 心跳
export function getMsgPant() {
    return axiosRequest({
        url: '/news/web/pant',
        method: 'get'
    })
}

// 消息数量
export function getMsgCount() {
    return axiosRequest({
        url: '/news/web/count',
        method: 'get'
    })
}

// 读消息
export function readMsg(params: any) {
    return axiosRequest({
        url: `/news/web/read`,
        method: 'post',
        data: params
    })
}

// 批量读消息
export function readAllMsg(params: any) {
    return axiosRequest({
        url: `/news/web/list/read`,
        method: 'post',
        data: params
    })
}
