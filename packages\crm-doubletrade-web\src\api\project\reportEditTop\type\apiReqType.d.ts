/*
 * @Description: reportEditTop 传参数 ts类型定义
 * @Author: chaohui.wu
 * @Date: 2023-03-25 23:24:06
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-17 19:50:27
 * @FilePath: /crm-asset-web/src/api/project/reportEditTop/type/apiReqType.d.ts
 *
 */

export {}
declare module './apiReqType' {
    type ReportEditParams = {
        conscustNo: string
        amount: number
        assetId: string
        reportName: string
    }
    type MaxInvestParams = Pick<ReportEditParams, 'conscustNo' | 'maxInvest' | 'unit'>
    type ReportNameParams = Pick<ReportEditParams, 'assetId' | 'reportName'>
    type TopInfoParams = Pick<ReportEditParams, 'assetId' | 'conscustNo'>
    export { MaxInvestParams, ReportNameParams, TopInfoParams }
}
