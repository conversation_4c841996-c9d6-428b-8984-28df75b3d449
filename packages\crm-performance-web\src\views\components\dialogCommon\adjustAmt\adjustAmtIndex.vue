<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:15:31
 * @FilePath: /crm-rpo-template/packages/crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="400px"
        :title="dialogBase.title"
        :slot-list="['default', 'footer']"
        :before-close="() => handleClose(dialogForm)"
        :close-on-click-modal="false"
        @keyup.enter.stop="confirmFn(dialogForm)"
    >
        <div v-show="dialogBase.noticeTxt" class="notice-box">
            <p class="notice-txt">{{ dialogBase.noticeTxt }}</p>
        </div>
        <el-form
            ref="dialogForm"
            class="adjust_dialog_form"
            :model="formList"
            :rules="formRules"
            :disabled="isView"
            :scroll-to-error="true"
            @submit.prevent
        >
            <div class="form-content">
                <el-form-item :label="adjustAmount.label" prop="amount">
                    <div class="editor-num-box">
                        <el-input-number
                            v-model="formList.amount"
                            class="no-border border-underline"
                            :precision="2"
                            :min="0"
                            size="small"
                            :value-on-clear="null"
                            :controls="false"
                            :placeholder="adjustAmount.placeholder"
                            @blur="(val:any) => handleBlur('adjustAmount', val)"
                        />
                        <span class="number-unit">万元</span>
                    </div>
                </el-form-item>
            </div>
        </el-form>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="confirmFn(dialogForm)"
                    >确 认</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { dataList } from './scripts/labelData'
    import { saveMaxInvest } from '@/api/project/reportEditTop/reportEditTopList'
    import { fetchRes, messageBox } from '@common/utils/index'

    const { dialogBase, adjustAmount } = dataList

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                conscustNo: string
                amount: number
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    conscustNo: '',
                    amount: 0
                }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
    }>()

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: form校验规则
     * @return {*}
     */
    const formRules = reactive<FormRules>({
        amount: [
            {
                required: true,
                message: '最大可投资金额仅支持>=0数字(最多2位小数)单位（万元）',
                trigger: ['change', 'blur']
            }
        ]
    })

    /**
     * @description: 表单字段
     * @return {*}
     */
    class FormList {
        conscustNo = props.transData.conscustNo
        amount = props.transData.amount
    }
    const formList = ref<any>(new FormList())
    const dialogForm = ref<FormInstance>()

    /**
     * @description: 是否编辑页面
     * @param {*} computed
     * @return {*} [isView|预览页] * [edit|编辑页]
     */
    const isView = computed<boolean>(() => props.dialogType === 'view')

    /**
     * @description: 失去焦点
     * @param {*} inputNumberName
     * @param {*} val
     * @return {*}
     */
    const handleBlur = (inputNumberName: string, val: any) => {
        const curNumber = Number(val?.target?.value) || 0
        switch (inputNumberName) {
            case 'adjustAmount':
                if (curNumber <= 0) {
                    val.target.value = '0.00'
                    formList.amount = 0
                }
                break
            default:
                break
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        if (!formEl) {
            return formEl
        }
        dialogVisible.value = false
        formEl.resetFields()
    }

    /**
     * @description: 自定义调整数据
     * @param {*} computed
     * @return {*}
     */
    const custamountAdjust: any = computed(() => {
        return {
            conscustNo: props.transData.conscustNo,
            maxInvest: formList.value.amount,
            unit: '1'
        }
    })

    /**
     * @description: 表单提交
     * @param {*} formEl
     * @return {*}
     */
    const loadingFlag = ref<boolean>(false)
    const confirmFn = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                loadingFlag.value = true
                fetchRes(saveMaxInvest(custamountAdjust.value), {
                    successCB: (res: any) => {
                        loadingFlag.value = false
                        dialogVisible.value = false
                        // 重置
                        formEl.resetFields()
                    },
                    errorCB: (res: any) => {
                        loadingFlag.value = false
                        ElMessage({
                            type: 'error',
                            message: res?.description || '请求失败'
                        })
                    },
                    catchCB: () => {
                        loadingFlag.value = false
                    },
                    successTxt: '调整成功',
                    failTxt: '',
                    fetchKey: ''
                })
            }
        })
    }
</script>

<style lang="less" scoped>
    .notice-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 28px;
        background-color: @bg_main_04;

        .notice-txt {
            height: 16px;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            line-height: 16px;
            color: @font_color_05;
        }
    }

    .is-no-data {
        padding: 15vh 0;
        text-align: center;
    }

    .adjust_dialog_form {
        .form-content {
            box-sizing: border-box;
            max-height: calc(56vh - 80px);
            overflow: auto;

            &::-webkit-scrollbar {
                width: 0;
            }

            :deep(.el-form-item) {
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-top: 10px;
                margin-bottom: 30px;

                .el-form-item__label {
                    align-items: center;
                    justify-content: center;
                    width: 200px;
                    height: auto;
                    margin-bottom: 11px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 20px;
                    color: @font_color;
                }

                .el-form-item__content {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .el-radio-group {
                        padding-left: 27px;

                        .el-radio__label {
                            height: 17px;
                            font-family: 'Microsoft YaHei';
                            font-size: 13px;
                            line-height: 17px;
                            color: #2a3050;
                        }
                    }
                }
            }

            :deep(.el-form-item__error) {
                width: 180px;
            }

            .editor-num-box {
                :deep(.el-input-number) {
                    width: 120px;

                    .el-input {
                        .is-error {
                            border-bottom: 1px solid @theme_main;
                        }
                    }

                    &.no-border {
                        .el-input {
                            --el-input-focus-border-color: transparent;
                            --el-input-hover-border-color: transparent;
                            --el-select-input-focus-border-color: transparent;
                            --el-input-border-color: transparent;
                            --el-color-danger: transparent;

                            .el-input__wrapper {
                                padding: 0 5px 0 0;
                                box-shadow: 0 0 0 1px var(--el-color-danger) inset;
                            }
                        }
                    }

                    &.border-underline {
                        .el-input {
                            border-bottom: 1px solid @border_color_02;

                            .is-error {
                                border-bottom: 1px solid @theme_main;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
