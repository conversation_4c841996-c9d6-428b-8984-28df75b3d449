<!--
 * @Description: 输入框组件
 * @Author: chao<PERSON>.wu
 * @Date: 2023-07-27 13:21:08
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-01 14:40:13
 * @FilePath: /crm-web/common/components/moduleBase/CrmInput.vue
 * props 与 emit与el-input一致
 * 详见文档 https://element-plus.gitee.io/zh-CN/component/input.html#input-%E8%BE%93%E5%85%A5%E6%A1%86
 * 通用输入框组件
-->
<template>
    <el-input class="crm-input" :class="[className]" v-bind="$attrs" size="small">
        <template v-for="slotName in dataList" #[slotName]>
            <slot v-if="slotName" :key="slotName" :name="slotName" />
        </template>
    </el-input>
</template>

<script lang="ts" setup>
    /**
     * @description: 自定义组件名称
     * @return {*}
     */
    defineOptions({
        name: 'CrmInput'
    })

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            slotList?: []
            className?: string
        }>(),
        {
            slotList: () => [],
            className: ''
        }
    )

    const dataList = ref(props.slotList)

    watch(
        [() => props.slotList],
        (newVal: any) => {
            dataList.value = newVal || []
        },
        {
            immediate: true,
            deep: true
        }
    )
</script>

<style lang="less" scoped>
    .crm-input {
        width: 100%;
        font-size: 12px;
        line-height: 24px;
        --el-input-focus-border-color: @border_focus;

        &.el-textarea {
            margin-bottom: 5px;
        }

        &.el-input--small {
            .el-input__inner {
                height: 24px;
                padding: 0 8px;

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        .el-input__wrapper {
            padding: 1px 5px 1px 1px;
            border-radius: 2px;

            &.is-focus,
            &:focus {
                border-color: @border_focus;
            }
        }

        &.is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        .el-input__inner {
            color: @font_color_02;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }

        input[type='number'] {
            appearance: textfield;
        }
    }
</style>
<style lang="less">
    .el-tabs__item {
        &.is-active {
            color: @theme_main;
        }

        &.is-focus {
            color: @theme_main_hover;
        }

        &:hover {
            color: @theme_main_hover;
        }
    }

    .el-input {
        --el-input-text-color: @font_color_02;
        --el-input-focus-border-color: @border_focus;
        --el-select-input-focus-border-color: @border_focus;
    }
</style>
