{
    // "typescript.tsdk": "node_modules/typescript/lib",
    "editor.tabSize": 4,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.stylelint": "explicit"
    },
    "stylelint.enable": true,
    "stylelint.validate": [
        "css",
        "less",
        "postcss",
        "scss",
        "vue",
        "sass",
        "html"
    ],
    "[markdown]": {
        "editor.unicodeHighlight.ambiguousCharacters": false,
        "editor.unicodeHighlight.invisibleCharacters": false,
        "diffEditor.ignoreTrimWhitespace": false,
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    },
    "[less]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.suggest.insertMode": "replace",
        "gitlens.codeLens.scopes": ["document"]
    },
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "javascript.validate.enable": true,
    "files.associations": {
        "*.json": "jsonc"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "typescript.tsdk": "node_modules/typescript/lib"
}
