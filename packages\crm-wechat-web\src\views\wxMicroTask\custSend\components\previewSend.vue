<!--
 * @Description: 发送预览
 * @Author: chaohui.wu
 * @Date: 2023-08-01 11:28:26
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-25 15:40:49
 * @FilePath: /crm-web/packages/crm-wechat-web/src/views/wxMicroTask/custSend/components/previewSend.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        title="预览发送对象"
        :slot-list="['default']"
        :before-close="handleClose"
        :close-on-click-modal="false"
        @keyup.enter.stop="confirmFn"
    >
        <p class="txt-notice">
            共 {{ addWechatAccountNumTpl }} 位客户添加所属投顾企微 且 绑定一账通号
        </p>
        <base-table
            :columns="cloumnList"
            class="send-preview-list"
            :data="dataList"
            style="width: 100%"
            :stripe="true"
            height="100%"
            :border="true"
            operation-width="150"
            :show-operation="false"
            :no-select="false"
            :no-index="false"
        >
        </base-table>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { previewSend } from '@/api/project/customSend/customSendList'
    import { returnCodeRes } from '@/constant/index'
    import { fetchRes, formatTableValue, tableIsBol } from '@common/utils/index'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            visibleCus?: boolean
            cloumnList?: any[]
            paramsData?: any
        }>(),
        {
            visibleCus: true,
            cloumnList: () => {
                return [
                    {
                        key: 'conscustNo',
                        label: '投顾客户号',
                        minWidth: 100,
                        formatter: formatTableValue
                    },
                    {
                        key: 'custName',
                        label: '客户姓名',
                        minWidth: 100,
                        formatter: formatTableValue
                    },
                    {
                        key: 'addWechatAccount',
                        label: '是否添加企微且绑定',
                        minWidth: 100,
                        formatter: tableIsBol
                    }
                ]
            },
            paramsData: () => {
                return { importType: '1', sequenceNo: '' }
            }
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: 关闭表单
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '关闭'
        })
        dialogVisible.value = false
    }

    /**
     * @description: 提交表单确定
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = async (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'success',
            message: '确定'
        })
        dialogVisible.value = false
    }

    const curData = computed(() => {
        return props.paramsData
    })

    const dataList = ref([])
    const addWechatAccountNumTpl = ref<string | number>(0)
    const initPreviewMsg = () => {
        fetchRes(previewSend(curData.value), {
            successCB: (res: any) => {
                // 成功初始化
                if (res) {
                    const { list, addWechatAccountNum } = res || {}
                    dataList.value = list
                    addWechatAccountNumTpl.value = Number(addWechatAccountNum)
                }
            },
            errorCB: () => {
                dataList.value = []
            },
            catchCB: () => {
                dataList.value = []
            },
            successTxt: '',
            failTxt: '预览发送对象失败，请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        initPreviewMsg()
    })
</script>

<style lang="less" scoped>
    .txt-notice {
        margin-bottom: 10px;
        font-size: 14px;
        color: @font_color;
    }
</style>
