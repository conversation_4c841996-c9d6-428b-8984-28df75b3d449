/*
 * @Description: 校验状态列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-27 18:01:57
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-05 13:48:57
 * @FilePath: /crm-template/src/types/verifyList.d.ts
 *
 */

export {}
declare module '@common/types/verifyList' {
    interface VerifyCodes {
        verify: string
        msg: string
        confirmButtonText: string
        callBackFunc: string
    }
    interface MsgFetchRes {
        /**
         * 成功回调
         */
        successCB?: Function | null
        /**
         * 失败回调
         */
        errorCB?: Function | null
        /**
         * 失败回调
         */
        catchCB?: Function | null
        /**
         * 成功文本
         */
        successTxt: string
        /**
         * 失败文本
         */
        failTxt: string
        /**
         * 埋点标记
         */
        fetchKey?: string
    }
    export { VerifyCodes, MsgFetchRes }
}
