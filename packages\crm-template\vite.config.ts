/*
 * @Description: vite默认配置项
 * @Author: chaohui.wu
 * @Date: 2023-06-20 16:43:31
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-25 15:13:23
 * @FilePath: /crm-rpo-template/packages/crm-template/vite.config.ts
 *
 */
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import autoprefixer from 'autoprefixer'
import eslintPlugin from 'vite-plugin-eslint'
import { visualizer } from 'rollup-plugin-visualizer' // 分析工具
import viteCompression from 'vite-plugin-compression'
import { createHtmlPlugin } from 'vite-plugin-html'
// import Icons from 'unplugin-icons/vite'
// import IconsResolver from 'unplugin-icons/resolver'
// import UnoCSS from 'unocss/vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import ckeditor5 from '@ckeditor/vite-plugin-ckeditor5'
import { createRequire } from 'node:module'
const require = createRequire(import.meta.url)
const path = require('path')
const resolve = (dir: string) => path.join(__dirname, dir)
process.env.VUE_APP_TIME = new Date().toLocaleString()
const resolveRoot = (dir: string) => path.join(__dirname, `../../${dir}`)
const curModuleName = path.basename(path.join(__dirname, './'))

// console.log('路径', resolveRoot('public/index.html'))

// https://vitejs.dev/config/
export default defineConfig({
    base: './',
    plugins: [
        vue(),
        createHtmlPlugin({
            minify: false,
            pages: [
                {
                    /**
                     * 在这里写entry后，你将不需要在`index.html`内添加 script 标签，原有标签需要删除
                     * @default src/main.ts
                     */
                    filename: 'index.html',
                    entry: './src/main.ts',
                    /**
                     * 如果你想将 `index.html`存放在指定文件夹，可以修改它，否则不需要配置
                     * @default index.html
                     */
                    template: 'index.html',
                    // template: resolveRoot('./index.html'),
                    /**
                     * 需要注入 index.html ejs 模版的数据
                     */
                    injectOptions: {
                        data: {
                            title: `${curModuleName}`,
                            // injectScript: `<script id="assetWeb" type="text/javascript" src="./msEnv.js?v=<%= VUE_APP_TIME %>"></script>`,
                            VUE_APP_TIME: new Date().getTime().toString()
                        }
                    }
                }
            ]
        }),
        // UnoCSS({}),
        ckeditor5({ theme: require.resolve('@ckeditor/ckeditor5-theme-lark') }),
        AutoImport({
            eslintrc: {
                enabled: true,
                filepath: '.eslintrc-auto-import.json',
                globalsPropValue: true
            },
            imports: ['vue', 'vue-router', 'pinia'],
            dts: true,
            resolvers: [ElementPlusResolver()]
            // , IconsResolver({})
        }),
        Components({
            // dirs: ['src/components'],
            dirs: [resolveRoot('common/components')],
            resolvers: [
                ElementPlusResolver()
                // IconsResolver({
                //     // @iconify-json/ep 是 Element Plus 的图标库
                //     enabledCollections: ['ep']
                // })
            ]
        }),
        // Icons({
        //     // 自动安装图标库
        //     autoInstall: true
        // }),
        createSvgIconsPlugin({
            // 要缓存的图标文件夹
            // iconDirs: [resolve('src/assets/icons')],
            iconDirs: [resolveRoot('common/assets/icons')],
            // 执行 icon name 的格式
            symbolId: 'icon-[name]'
        }),
        eslintPlugin({
            include: [
                'src/*.{js,jsx,vue,ts,tsx}',
                'src/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/**/**/*.{js,jsx,vue,ts,tsx}'
            ],
            cache: false
        }),
        visualizer({
            emitFile: false,
            gzipSize: true, // 分析图生成的文件名
            brotliSize: true, // 收集 brotli 大小并将其显示
            filename: 'analysis-chart.html',
            open: true
        }),
        viteCompression({
            verbose: true,
            disable: false, // 是否禁用压缩模式
            threshold: 1024, // 1kb
            algorithm: 'gzip',
            ext: '.gz'
        })
    ],
    resolve: {
        extensions: ['.ts', '.js', '.vue', '.json'],
        alias: {
            '@': resolve('src'),
            '@common': resolveRoot('common'),
            '@packages': resolveRoot('packages')
        }
    },
    // 解决fsevents模块报错问题
    optimizeDeps: { exclude: ['fsevents'] },
    css: {
        // css预处理器
        preprocessorOptions: {
            less: {
                charset: false,
                additionalData: '@import "@common/assets/css/variable.less";'
            }
        },
        modules: {
            scopeBehaviour: 'local',
            localsConvention: 'camelCase'
        },
        postcss: { plugins: [autoprefixer()] },
        devSourcemap: true
    },
    server: {
        // host: '',
        // port: 3088,
        // strictPort: false,
        open: true,
        proxy: {
            '/assetCurrent': {
                // target: 'http://**************:8087/crm-asset/',
                target: 'http://crm.it34.k8s.howbuy.com/crm-cgi/inner/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/assetCurrent/, '')
            },
            '/oldAsset': {
                // target: 'http://**************:8087/crm-asset/',
                target: 'http://crm.it34.k8s.howbuy.com/crm-asset/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/oldAsset/, '')
            },
            '/hdCurrent': {
                target: 'http://crm.it34.k8s.howbuy.com/crm-cgi/inner/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/hdCurrent/, '')
            }
        }
    },
    build: {
        outDir: resolveRoot(`./dist/${curModuleName}`),
        assetsDir: 'static',
        minify: true,
        target: ['chrome89', 'edge89', 'firefox89', 'safari15'],
        sourcemap: false,
        // target: 'esnext',
        // cssCodeSplit: true,
        rollupOptions: {
            input: {
                index: resolve('index.html')
            },
            output: {
                chunkFileNames: 'static/js/[hash].js',
                entryFileNames: 'static/js/[hash].js',
                assetFileNames: 'static/[ext]/[hash].[ext]'
            }
        }
    }
})
