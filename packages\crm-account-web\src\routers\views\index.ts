/*
 * @Description: 固定路由配置
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-26 13:25:22
 * @FilePath: /crm-web/packages/crm-wechat-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'custSend' },
        children: [
            {
                path: 'custSend',
                name: 'custSend',
                meta: {
                    title: '企微发送'
                },
                component: () => import('@/views/wxMicroTask/custSend/custSendIdx.vue')
            },
            {
                path: 'addCommunicationRecord',
                name: 'addCommunicationRecord',
                meta: {
                    title: '新增沟通记录'
                },
                component: () => import('@/views/communication/addCommunicationRecord.vue')
            },
            {
                path: 'visitMinuteList',
                name: 'visitMinuteList',
                meta: {
                    title: '拜访纪要列表页'
                },
                component: () => import('@/views/visitminutes/visitMinuteList.vue')
            },
            {
                path: 'visitRecord',
                name: 'visitRecord',
                meta: {
                    title: '客户拜访纪要'
                },
                component: () => import('@/views/customerVisit/visitRecord/visitRecordDemo.vue')
            },
            {
                path: 'visitMinuteDetail',
                name: 'visitMinuteDetail',
                meta: {
                    title: '拜访纪要详情'
                },
                component: () => import('@/views/communication/visitMinuteDetail.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
