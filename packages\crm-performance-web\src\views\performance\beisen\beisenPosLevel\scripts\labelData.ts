import { start } from 'repl'

/*
 * @Description: 定义搜索的label列表
 * @Author: chaohui.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 13:07:40
 * @FilePath: /ds-report-web/src/views/modBusiness/pageModule/script/labelData.ts
 *
 */
export const dataList = {
    // 架构名称(北森)
    positionsLevelBeisen: {
        label: '职级编码(北森)',
        placeholder: '请输入'
    },
    positionsLevelNameBeisen: {
        label: '职级名称(北森)',
        placeholder: '请输入'
    },
    userLevelCrm: {
        label: '层级（crm）',
        placeholder: '请选择',
        selectList: ref([])
    },
    positionsLevelCrm: {
        label: '职级（crm）',
        placeholder: '请选择',
        selectList: ref([])
    },
    subPositionsLevelCrm: {
        label: '副职（crm）',
        placeholder: '请选择',
        selectList: ref([])
    },
    //起止日期
    matchInterval: {
        label: '起止日期',
        placeholder: ['开始日期', '结束日期']
    },
    startDate: {
        label: '起始日期',
        placeholder: '请选择'
    },
    endDate: {
        label: '结束日期',
        placeholder: '请选择'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
