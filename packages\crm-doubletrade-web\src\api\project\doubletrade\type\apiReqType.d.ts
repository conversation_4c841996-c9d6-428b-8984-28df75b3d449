/*
 * @Description: 报告列表 ts类型定义
 * @Author: chaohui.wu
 * @Date: 2023-03-20 14:21:21
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:27:48
 * @FilePath: /crm-template/src/api/project/reportList/type/apiReqType.d.ts
 *
 */
export {}
declare module './apiReqType' {
    interface SearchFundReq {
        /**
         * 搜索关键字
         */
        q?: string
    }

    interface TemplateFormReq {
        /**
         * 模版名称
         */
        tempName?: string
        /**
         * 模版适用类型 1-指定产品 2-指定条件
         */
        tempType?: string
        /**
         * 产品代码
         */
        fundCode?: string
        /**
         * 审核状态 1-待审核 2-审核通过 3-审核不通过
         */
        checkflag?: string
        /**
         * 客户类型 0-机构,1-个人
         */
        custType?: string
        /**
         * 成单方式 1-纸质成单 2-电子成单
         */
        preType?: string
        /**
         * 生效时间
         */
        effectdt?: string
        /**
         * 失效时间
         */
        failuredt?: string
    }

    interface UpdateEffectdtReq {
        /**
         * 模版id
         */
        id?: string
        /**
         * 生效时间
         */
        effectdt?: string
        /**
         * 失效时间
         */
        failuredt?: string
    }

    interface TemplateInfoForm {
        /**
         * 模版id
         */
        id?: string
        /**
         * 模版名称
         */
        tempName?: string
        /**
         * 模版适用类型 1-指定产品 2-指定条件
         */
        tempType?: string
        /**
         * 产品代码列表
         */
        fundCodeList?: []
        /**
         * 审核状态 1-待审核 2-审核通过 3-审核不通过
         */
        checkflag?: string
        /**
         * 客户类型 0-机构,1-个人
         */
        custType?: string
        /**
         * 成单方式 1-纸质成单 2-电子成单
         */
        preType?: string
        /**
         * 产品风险等级 1-R1，2-R2，3-R3，4-R4，5-R5
         */
        proRiskLevel?: string
        /**
         * 产品类型 1-资管产品 2-非资管普通产品
         */
        proType?: string
        /**
         * 模版问题列表
         */
        questionList?: QuestionForm[]
    }

    interface QuestionForm {
        /**
         * 问题id
         */
        id?: string
        /**
         * 模版id
         */
        tempId?: string
        /**
         * 问题
         */
        question?: string
        /**
         * 答案
         */
        answer?: string
        /**
         * 审核状态 1-通过 0-驳回
         */
        flag?: string
        /**
         * 审核原因
         */
        checkreason?: string
    }

    interface CheckQuestionReq {
        /**
         * 模版id
         */
        id?: string
        /**
         * 审核结果list
         */
        checkList?: CheckQuestion[]
    }

    interface CheckQuestion {
        /**
         * 模版id
         */
        id?: string
        /**
         * 审核状态 1-待审核 2-审核通过 3-审核不通过
         */
        checkflag?: string
        /**
         * 审核原因
         */
        checkreason?: string
    }

    interface QueryMatchFundReq {
        /**
         * 基金代码
         */
        jjdm?: string
        /**
         * 风险等级（多个,分割）
         */
        risklevel?: string
        /**
         * 产品类型（1-资管产品 2-非资管普通产品）
         */
        proType?: string
    }

    export {
        SearchFundReq,
        TemplateFormReq,
        UpdateEffectdtReq,
        TemplateInfoForm,
        CheckQuestionReq,
        QueryMatchFundReq
    }
}
