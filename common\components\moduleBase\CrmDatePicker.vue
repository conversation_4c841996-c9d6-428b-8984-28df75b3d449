<!--
 * @Description: 日期选择器
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-13 18:50:05
 * @FilePath: /crm-web/common/components/moduleBase/CrmDatePicker.vue
 * @Link https://element-plus.gitee.io/zh-CN/component/date-picker.html
 * @Demo: <crm-date-picker
            v-model="queryForm.reportCreationTime"
            type="daterange"
            show-format="YYYY-MM-DD HH:mm:ss"
            :placeholder="reportCreationTime.placeholder"
        />
-->
<template>
    <el-date-picker
        :class="['crm_date_picker', { crm_date_picker_fund: styleType === 'fund' }, className]"
        popper-class="crm-data-picker-pop"
        size="small"
        :placeholder="placeholder"
        :start-placeholder="startPlaceholder"
        :end-placeholder="endPlaceholder"
        :value-format="valueFormat"
        :format="showFormat"
        range-separator="-"
        :disabled-date="disabledStartDate"
        v-bind="$attrs"
    />
</template>

<script setup lang="ts">
    import { dateFormat } from '../../utils/index'
    defineOptions({
        name: 'CrmDatePicker'
    })

    const props = withDefaults(
        defineProps<{
            placeholder?: string
            startPlaceholder?: string
            endPlaceholder?: string
            disabledDate?: {
                startDate?: string
                endDate?: string
            }
            valueFormat?: string
            limitLessThanCurrent?: boolean
            showFormat?: string
            className?: string
            styleType?: string
        }>(),
        {
            placeholder: '选择日期',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            valueFormat: 'YYYYMMDD',
            showFormat: 'YYYY-MM-DD',
            disabledDate: () => {
                return {
                    startDate: '',
                    endDate: ''
                }
            },
            className: '',
            styleType: '',
            limitLessThanCurrent: false
        }
    )

    const dayNum = computed(() => {
        return 24 * 3600 * 1000
    })

    const disabledStartDate = (time: any) => {
        const ctime = time.getTime()
        // 有disabledDate情况
        const { startDate, endDate } = props.disabledDate
        const sDate: any = startDate ? dateFormat(startDate, 'x') : ''
        const eDate: any = endDate ? dateFormat(endDate, 'x') : ''
        if (sDate || eDate) {
            const minDisable: any = sDate ? ctime < sDate - dayNum.value : false
            const maxDisable: any = eDate ? ctime >= eDate : false
            return minDisable || maxDisable
        }
        const lessThanCurrent = props.limitLessThanCurrent ? new Date().getTime() > ctime : false
        return lessThanCurrent
    }
</script>
<style lang="less">
    .crm_date_picker {
        &.is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        &.el-date-editor {
            width: 100%;
        }

        &.w180 {
            width: 180px;
        }

        &.el-input--small {
            .el-input__wrapper {
                width: 100%;

                &.is-focus {
                    --el-input-focus-border-color: @border_focus;
                    // border-color: @border_focus;
                }

                .el-input__inner {
                    height: 24px;
                    font-size: 12px;
                    line-height: 24px;
                    border-radius: 2px;

                    &:focus,
                    .is-focus {
                        border-color: @border_focus;
                    }
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        &.el-input--prefix .el-input__inner {
            /* padding-left: 20px; */
            padding-right: 0;
        }

        &.el-range-editor {
            width: 100%;

            &.is-active,
            &.is-focus {
                border-color: @border_focus;
                box-shadow: 0 0 0 1px @border_focus inset;
            }
        }

        &.el-range-editor--small.el-input__inner {
            &.is-active,
            &.is-focus {
                border-color: @border_focus;
                box-shadow: 0 0 0 1px @border_focus inset;
            }

            height: 24px;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }

            .el-input__icon {
                height: 24px;
                line-height: 24px;
            }

            .el-range-separator {
                width: 20px;
                font-size: 12px;
                line-height: 16px;
            }

            input {
                font-size: 12px;

                &::placeholder {
                    font-size: 12px;
                }
            }
        }

        .is-disabled {
            .el-input__inner {
                color: @font_color;
            }
        }

        &.crm_date_picker_fund {
            width: auto;
            height: 20px;
            padding-right: 21px;
            background: url(../../assets/images/calendar.png) no-repeat right 3px;
            background-size: 15px auto;

            .el-input__inner {
                width: 72px;
                height: 20px;
                padding: 0 2px;
                font-size: 12px;
                line-height: 20px;
                color: @font_color_05;
                text-align: center;
                border: none;
                border-bottom: 1px solid #7078b8;
                border-radius: 0;

                &:hover,
                &:focus {
                    border-color: #7078b8;
                }

                &::placeholder {
                    color: @font_color_04;
                }
            }

            &.is-disabled .el-input__inner {
                border-bottom: 1px solid #7078b8;
            }

            .el-input__prefix,
            .el-input__suffix {
                display: none;
            }

            &.el-date-editor.el-input.is-disabled {
                .el-input__inner {
                    color: #666666;
                    background-color: @font_color_01;
                }
            }
        }
    }

    .crm-data-picker-pop {
        .el-date-picker__header {
            .el-picker-panel__icon-btn {
                margin-top: 0;
            }
        }
    }
</style>
