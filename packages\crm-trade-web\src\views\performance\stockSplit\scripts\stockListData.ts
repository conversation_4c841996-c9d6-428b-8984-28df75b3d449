/*
 * @Description: 绩效管理data
 * @Author: chaohui.wu
 * @Date: 2023-09-07 13:57:57
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 11:14:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/stockListData.ts
 *
 */

import { fetchSync, makeElementTree } from '@common/utils/index'

export const useStockListData = defineStore('stockListData', {
    state: () => {
        return {
            operateList: [],
            organizationList: [],
            consultList: [],
            formerOrganizationList: [],
            formerConsultList: [],
            orgCodeDefault: '',
            consCodeDefault: '',
            formerOrgCodeDefault: '',
            formerConsCodeDefault: ''
        }
    },
    getters: {},
    actions: {
        setCommonData({
            organizationList,
            consultList,
            formerConsultList,
            formerOrganizationList
        }: any) {
            if (organizationList) {
                this.organizationList = organizationList
            }
            if (consultList) {
                this.consultList = consultList
            }
            if (formerOrganizationList) {
                this.formerOrganizationList = formerOrganizationList
            }
            if (formerConsultList) {
                this.formerConsultList = formerConsultList
            }
        },
        fetchPageInit(type = '') {},
        getPageInit() {
            return Promise.all([this.fetchPageInit(), this.fetchPageInit('all')])
        }
    }
})
