<!--
 * @Description: 风险测评问卷弹框
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 10:15:41
 * @FilePath: /crm-rpo-template/packages/crm-template/src/views/components/dialogCommon/riskyc/riskycIndex.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        :title="dialogBase.title"
        :slot-list="['default', 'footer']"
        :before-close="() => handleClose(dialogForm)"
        :close-on-click-modal="false"
        @keyup.enter.stop="confirmFn(dialogForm)"
    >
        <!-- <loading :visible="loadingFlag" :show-percentage="false" type="2"></loading> -->
        <div class="notice-box">
            <p class="notice-txt">{{ dialogBase.noticeTxt }}</p>
        </div>
        <el-form
            ref="dialogForm"
            class="crm_dialog_form"
            :model="formList"
            :rules="formRules"
            :disabled="isView"
            :scroll-to-error="true"
            @submit.prevent
        >
            <div v-show="!isNoData" class="form-content">
                <!-- :label="`${index + 1}、${item.question}`" -->
                <el-form-item
                    v-for="(item, index) in formList.questions"
                    :key="`questionId${item.examId}${index}`"
                    :prop="`questions.${index}.checked`"
                    width="100%"
                    :inline-message="true"
                    :rules="formRules.questions"
                >
                    <div class="item-label">
                        <span>{{ `${index + 1}、` }}</span>
                        <p>
                            <span v-if="item.label" class="tips-item txt-red">{{ item.label }}</span
                            >{{ `${item.question}` }}
                        </p>
                    </div>
                    <crm-radio
                        v-model="item.checked"
                        :option-list="item.options"
                        label-format="optionDesc"
                        value-format="optionId"
                    />
                </el-form-item>
            </div>
            <div v-show="isNoData" class="is-no-data">{{ dialogBase.noData }}</div>
        </el-form>
        <template #footer>
            <div>
                <crm-button plain size="small" :radius="true" @click="handleClose(dialogForm)"
                    >关 闭</crm-button
                >
                <crm-button size="small" :radius="true" @click="confirmFn(dialogForm)"
                    >确 认</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { dataList } from './scripts/labelData'
    import { getCustKYC, insertCustKYC } from '@/api/project/customerAnalysis/riskAssessmentList'
    import { fetchRes, messageBox } from '@common/utils/index'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            consCustNo: string
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            consCustNo: ''
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    /**
     * @description: 数据初始化
     * @return {*}
     */
    const { dialogBase } = dataList

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: form校验规则
     * @return {*}
     */
    const formRules = reactive<FormRules>({
        questions: [
            {
                required: true,
                message: '数据不能为空',
                trigger: 'blur'
            }
        ]
    })

    class FormList {
        examId = ''
        questions = []
        discode = ''
    }
    const formList = ref<any>(new FormList())

    /**
     * @description: 是否预览页
     * @param {*} computed
     * @return {*}
     */
    const isView = computed<boolean>(() => props.dialogType === 'view')
    const isNoData = computed<boolean>(() => formList.value?.questions?.length <= 0)

    /**
     * @description: loading标记
     * @return {*}
     */
    const loadingFlag = ref<boolean>(false)

    /**
     * @description: kyc风险提示问卷接口
     * @return {*}
     */
    const riskAssessmentList = async () => {
        loadingFlag.value = true
        fetchRes(getCustKYC({ conscustno: `${props.consCustNo}` }), {
            successCB: (res: any) => {
                formList.value = res
                loadingFlag.value = false
            },
            errorCB: () => {
                loadingFlag.value = false
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '',
            failTxt: '请求失败',
            fetchKey: ''
        })
    }

    const dialogForm = ref<FormInstance>()
    /**
     * @description: 关闭表单
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        if (!formEl) {
            return formEl
        }
        dialogVisible.value = false
        formEl.resetFields()
    }

    /**
     * @description: 提交表单确定
     * @param {*} formEl
     * @return {*}
     */
    const confirmFn = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                const { examId, questions } = formList.value
                const custKYCAnswer: {
                    conscustno: string | any
                    examId: string
                    adjustKycList: []
                } = {
                    conscustno: props.consCustNo,
                    examId: examId,
                    adjustKycList: questions.map((item: any) => {
                        const optionCharList =
                            item?.options?.filter(
                                (store: any) => store.optionId === item.checked
                            ) || []
                        const { optionChar } = optionCharList[0]
                        return {
                            ...item,
                            optionChar,
                            optionId: item.checked
                        }
                    })
                }
                loadingFlag.value = true
                fetchRes(insertCustKYC(custKYCAnswer), {
                    successCB: (res: any) => {
                        loadingFlag.value = false
                        dialogVisible.value = false
                        // 重置
                        formEl.resetFields()
                        emit('callBack')
                    },
                    errorCB: () => {
                        loadingFlag.value = false
                    },
                    catchCB: () => {
                        loadingFlag.value = false
                    },
                    successTxt: '调整成功',
                    failTxt: '调整失败，请重试～',
                    fetchKey: ''
                })
            } else {
                console.log('error submit!', fields)
                return false
            }
        })
    }

    onMounted(() => {
        riskAssessmentList()
    })
</script>

<style lang="less" scoped>
    .notice-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 28px;
        background-color: @bg_main_04;

        .notice-txt {
            height: 16px;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            line-height: 16px;
            color: @font_color_05;
            text-align: center;
        }
    }

    .is-no-data {
        padding: 15vh 0;
        text-align: center;
    }

    .crm_dialog_form {
        .form-content {
            box-sizing: border-box;
            max-height: calc(@max_dialog_height - 80px);
            margin-top: 12px;
            overflow: auto;

            &::-webkit-scrollbar {
                width: 0;
            }

            :deep(.el-form-item) {
                flex-direction: column;
                margin-bottom: 20px;

                .el-form-item__label {
                    align-items: flex-start;
                    justify-content: flex-start;
                    height: 22px;
                    margin-bottom: 11px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 22px;
                    color: @font_color;
                }

                .item-label {
                    display: flex;
                    align-items: flex-start;
                    justify-content: flex-start;
                    margin-bottom: 11px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    // height: 22px;
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 22px;
                    color: @font_color;

                    .tips-item {
                        box-sizing: border-box;
                        height: 24px;
                        padding: 1px 8px;
                        margin-right: 10px;
                        font-family: 'Microsoft YaHei', '微软雅黑';
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 22px;
                        background-color: rgba(208, 2, 27, 0.1);

                        &.txt-red {
                            color: #d0021b;
                        }
                    }
                }

                .el-form-item__content {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .el-radio-group {
                        padding-left: 27px;

                        .el-radio__label {
                            height: 17px;
                            font-family: 'Microsoft YaHei';
                            font-size: 13px;
                            line-height: 17px;
                            color: #2a3050;
                        }
                    }
                }
            }
        }
    }
</style>
