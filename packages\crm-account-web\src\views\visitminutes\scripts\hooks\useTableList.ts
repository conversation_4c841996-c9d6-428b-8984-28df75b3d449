/*
 * @Description:页面table列表公共hooks
 * @Author: chaohui.wu
 * @Date: 2023-09-06 18:45:44
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-18 18:10:33
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/hooks/useTableList.ts
 *
 */
import { fetchRes } from '@common/utils/index'
import { useRoute } from 'vue-router'
// 页面权限控制
import { usePermission } from '@/stores/permission'
const permissionStore = usePermission()
const { getMenuRoles, isPremission } = permissionStore

export function useTableList({ fetchList, fetchListParams = {}, queryFormParams = {} }: any) {
    const route = useRoute()
    const { menuCode }: any = route?.query || {}
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = ref(new PageObj())
    const queryForm = ref(new queryFormParams())

    /**
     * @description: 列表请求
     * @return {*}
     */
    const tableList = ref([])
    /**
     * @description: 是否请求数据
     * @return {*}
     */
    const requestData = ref(false)
    const queryList = () => {
        fetchRes(fetchList(fetchListParams()), {
            successCB: (res: any) => {
                // 权限请求成功
                const { list, page, total, size } = res || {}
                tableList.value = list
                pageObj.value.total = Number(total)
                requestData.value = true
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description 重置
     * @param resetQueryForm 重置
     */
    const handleReset = (callBack?: Function) => {
        Object.assign(pageObj.value, new PageObj())
        Object.assign(queryForm.value, new queryFormParams())
        // 组件重置
        callBack && callBack()
    }

    /**
     * @description 提示
     */
    onBeforeMount(() => {
        // 获取当前页面权限
        getMenuRoles({ menuCode })
    })

    return {
        tableList,
        pageObj,
        queryForm,
        isPremission,
        queryList,
        handleReset,
        handleCurrentChange,
        requestData
    }
}
