<!--
 * @Description: 拜访记录列表页
 * @Author: auto-generated
 * @Date: 2023-11-13
 * @LastEditors: auto-generated
 * @LastEditTime: 2023-11-14
 * @FilePath: /crm-web/packages/crm-account-web/src/views/visitminutes/visitMinuteList.vue
-->
<template>
    <div class="visit-list-module">
        <table-wrapper class-name="crm_wraper" @searchFn="queryList">
            <template #searchArea>
                <label-item label="所属投顾">
                    <ReleatedSelect
                        ref="formerSelect"
                        v-model="queryForm.constObj"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        menu-code="020153"
                    ></ReleatedSelect>
                </label-item>
                <label-item label="拜访日期">
                    <date-range
                        v-model="queryForm.visitDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
                <label-item label="创建日期">
                    <date-range
                        v-model="queryForm.createDt"
                        show-format="YYYY-MM-DD"
                        style-type="fund"
                    />
                </label-item>
                <label-item label="拜访目的">
                    <crm-select
                        v-model="queryForm.visitPurpose"
                        placeholder="全部"
                        label-format="label"
                        value-format="key"
                        style="width: 180px"
                        multiple
                        collapse-tags
                    >
                        <el-option
                            v-for="item in VISIT_PURPOSE"
                            :key="item.label"
                            :label="`${item.label}`"
                            :value="item.key"
                        />
                    </crm-select>
                </label-item>
                <label-item label="客户姓名">
                    <crm-input
                        v-model="queryForm.custName"
                        placeholder="请输入客户姓名"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="投顾客户号">
                    <crm-input
                        v-model="queryForm.consCustNo"
                        placeholder="请输入投顾客户号"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="陪访人">
                    <crm-input
                        v-model="queryForm.accompanyingUser"
                        placeholder="请输入陪访人姓名"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="上级主管">
                    <crm-input
                        v-model="queryForm.managerId"
                        placeholder="请输入上级主管姓名"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="反馈情况">
                    <crm-select
                        v-model="queryForm.feedbackStatus"
                        placeholder="全部"
                        label-format="label"
                        value-format="key"
                        style="width: 180px"
                        multiple
                        collapse-tags
                    >
                        <el-option
                            v-for="item in FEEDBACK_STATUS"
                            :key="item.label"
                            :label="`${item.label}`"
                            :value="item.key"
                        />
                    </crm-select>
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    v-show="isPremission('modifyManager')"
                    size="small"
                    :radius="true"
                    plain
                    @click="handleModifyManager"
                    >修改主管</crm-button
                >
                <crm-button
                    v-show="isPremission('export')"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="handleExport"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :columns="visitMinuteTableColumn"
                    :data="tableList"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    :empty-text="requestData ? '暂无数据' : ' '"
                    height="100%"
                    :no-index="false"
                    :border="true"
                    operation-width="100"
                    @selectionChange="handleSelectionChange"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-show="scope.row.canAccompanyFeedback"
                            size="small"
                            :text="true"
                            link
                            @click="handleFeedback(scope.row, '1')"
                            >陪访人反馈</el-button
                        >
                        <el-button
                            v-show="scope.row.canManagerFeedback"
                            size="small"
                            :text="true"
                            link
                            @click="handleFeedback(scope.row, '2')"
                            >主管反馈</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-size-list="[100, 200, 500, 1000, 2000]"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>
    </div>
    <visit-minute-feedback
        v-if="visitMinuteFeedbackVisiable"
        v-model="visitMinuteFeedbackVisiable"
        :trans-data="visitMinuteObj"
        @save="queryList"
    />
    <update-manager
        v-if="updateManagerVisiable"
        v-model="updateManagerVisiable"
        :trans-data="updateManagerObj"
        @callback="queryList()"
    />
</template>

<script lang="ts" setup>
    import { Download } from '@element-plus/icons-vue'
    import { ElMessage } from 'element-plus/es'
    import { visitMinutesList, exportVisitMinutes } from '@/api/project/visitminutes/index'
    import { VISIT_PURPOSE, FEEDBACK_STATUS } from '@/constant/visitMinute'
    import { visitMinuteTableColumn } from './scripts/tableData'
    import { useVisitMinuteListData } from './scripts/visitMinuteListData'
    import { useTableList } from './scripts/hooks/useTableList'
    import ReleatedSelect from './components/releatedSelect.vue'
    import VisitMinuteFeedback from './components/visitMinuteFeedback.vue'
    import UpdateManager from './components/updateManage.vue'
    // 拜访记录数据
    const visitMinuteListStore = useVisitMinuteListData()
    const { getPageInit } = visitMinuteListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(visitMinuteListStore)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        id = ''
        constObj = {
            orgCode: '',
            consCode: ''
        }
        custName = ''
        consCustNo = ''
        visitPurpose = []
        accompanyingUser = ''
        managerId = ''
        accompanyingType = ''
        visitStartDt = ''
        visitEndDt = ''
        visitDt = {
            startDate: '',
            endDate: ''
        }
        createStartDt = ''
        createEndDt = ''
        feedbackStatus = []
        createDt = {
            startDate: '',
            endDate: ''
        }
    }

    /**
     * @description: 列表传参
     * @return {*}
     */
    const fetchListParams = () => {
        const { visitDt, createDt, constObj } = queryForm.value || {}
        const { startDate: visitDateStart, endDate: visitDateEnd } = visitDt || {}
        const { startDate: createDateStart, endDate: createDateEnd } = createDt || {}
        const { orgCode: orgCode, consCode: consCode } = constObj || {}
        // 处理请求接口数据
        return {
            ...pageObj.value,
            ...queryForm.value,
            orgCode,
            consCode,
            visitDateStart,
            visitDateEnd,
            createDateStart,
            createDateEnd
        }
    }

    // hooks
    const {
        tableList,
        pageObj,
        queryForm,
        isPremission,
        queryList,
        handleCurrentChange,
        handleReset,
        requestData
    } = useTableList({
        fetchListParams, // 接口参数
        fetchList: visitMinutesList, // table列表查询接口
        queryFormParams: QueryForm
    })

    /**
     * @description: 重置
     * @param {*} void
     * @return {*}
     */
    const resetQueryForm = (): void => {
        handleReset()
    }

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const visitMinuteObj = ref({
        id: '',
        feedbackType: ''
    })
    /**
     * @description: 编辑
     * @return {*}
     */
    const updateManagerObj = ref({
        ids: [] as string[]
    })

    /**
     * @description: 新增/编辑/详情弹框
     * @return {*}
     */
    const visitMinuteFeedbackVisiable = ref<boolean>(false)
    const updateManagerVisiable = ref<boolean>(false)
    let checkIds = [] as string[]
    const handleModifyManager = (val: any) => {
        if (checkIds.length === 0) {
            ElMessage.warning('请选择要操作的记录')
            return
        }
        updateManagerObj.value = {
            ids: checkIds
        }
        updateManagerVisiable.value = true
    }
    const handleSelectionChange = (sel: any): void => {
        checkIds = sel.map((item: any) => item.id)
        console.log(checkIds)
    }

    const handleFeedback = (val: any, feedbackType = '1'): void => {
        const { id } = val || {}
        visitMinuteObj.value = {
            id: id,
            feedbackType: feedbackType
        }
        visitMinuteFeedbackVisiable.value = true
    }

    /**
     * @description: 基于条件导出
     * @return {*}
     */
    const handleExport = async () => {
        const res: any = await exportVisitMinutes(fetchListParams())
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.value.constObj = {
                    orgCode: orgCodeDefault.value,
                    consCode: consCodeDefault.value,
                    consList: consultList.value
                }
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 提示
     */
    onMounted(() => {
        getPageInit()
    })
</script>
<style lang="less" scoped>
    .visit-list-module {
        height: 100%;
    }
</style>
