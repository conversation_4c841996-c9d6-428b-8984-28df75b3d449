/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 16:57:09
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/tableData.ts
 *
 */
import { TableColumnItem } from '@/types/index'
import { formatTableValue } from '@common/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const posLevelConfigListTableColumn: TableColumnItem[] = [
    {
        key: 'positionsLevelBeisen',
        label: '职级编码(北森)',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'positionsLevelNameBeisen',
        label: '职级名称(北森)',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'userLevelCrmName',
        label: '层级（crm）',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'positionsLevelCrmName',
        label: '职级（crm）',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'subPositionsLevelCrmName',
        label: '副职（crm）',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'startDate',
        label: '起始日期',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'endDate',
        label: '结束日期',
        minWidth: 100,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
