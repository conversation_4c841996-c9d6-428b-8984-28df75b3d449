<!--
 * @Description: loading通用组件
 * @Author: chaohui.wu
 * @Date: 2023-05-10 18:36:28
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-25 18:52:00
 * @FilePath: /crm-rpo-template/common/components/moduleBase/Loading.vue
 *  
-->
<template>
    <div v-show="visible" class="page-loading" :class="[typeClass]">
        <div class="page-loading__content">
            <img src="../../assets/images/loading.gif" class="loading-img" />

            <el-progress
                v-if="showPercentage"
                :percentage="percentage"
                status="exception"
                :stroke-width="5"
                color="#D80013"
                :show-text="false"
            />

            <span class="loading-txt">{{ loadingTxt }}</span>
        </div>
    </div>
</template>

<script lang="ts">
    export default {
        name: 'PageLoading'
    }
</script>
<script lang="ts" setup>
    const props = defineProps({
        visible: {
            type: <PERSON><PERSON>an,
            default: false
        },
        percentage: {
            type: Number,
            default: 0
        },
        // 是否展示进度条
        showPercentage: {
            type: Boolean,
            default: true
        },
        // 加载中文字
        loadingTxt: {
            type: String,
            default: '加载中，请稍候...'
        },
        type: {
            type: String,
            default: '1'
        }
    })

    const typeClass = computed(() => {
        switch (props.type) {
            case '2': // 绝对定位
                return 'box-absolute'
            case '1':
            default:
                return 'tab-content'
        }
    })
</script>

<style lang="less" scoped>
    .page-loading {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);

        &.tab-content {
            top: 116px;
        }

        &.box-absolute {
            position: absolute;
        }

        .page-loading__content {
            position: absolute;
            top: 50%;
            left: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 260px;
            height: 150px;
            padding: 15px;
            text-align: center;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 0 27px 0 rgba(190, 190, 190, 0.5);
            transform: translate(-50%, -50%);

            .loading-img {
                width: 70px;
                height: auto;
                vertical-align: top;
            }

            > .el-progress {
                width: 160px;
                margin-top: 8px;
            }

            .loading-txt {
                margin-top: 12px;
                font-size: 14px;
                font-weight: bold;
                line-height: 19px;
                color: #333335;
            }
        }
    }
</style>
