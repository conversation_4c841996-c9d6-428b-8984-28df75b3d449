<!--
 * @Author: wencai.deng <EMAIL>
 * @Date: 2024-06-05 18:07:45
 * @LastEditors: wencai.deng <EMAIL>
 * @LastEditTime: 2024-06-11 10:24:14
 * @FilePath: /crm-web/packages/crm-doubletrade-web/src/views/doubletrade/createTemplate/createTemplate.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-form
            ref="ruleFormRef"
            :model="formList"
            :rules="rules"
            label-width="auto"
            class="demo-ruleForm"
            status-icon
        >
            <CrmDescriptions ref="descriptions" :props-data="propsData" />

            <QuestionOnAnswer ref="questionOnAnswer" />

            <AddTemplateTable ref="addTemplateTable" :props-data="propsData" />
        </el-form>
        <div class="button-style">
            <el-button @click="cancel">取消</el-button>
            <el-button
                v-if="type === 'add' || type === 'update'"
                type="primary"
                @click="handleSubmit(ruleFormRef)"
                >保存</el-button
            >
            <el-button v-if="type === 'check'" @click="handleOverruled">驳回</el-button>
            <el-button v-if="type === 'check'" type="primary" @click="handleCheck"
                >审核操作</el-button
            >
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue'
    import { useRoute } from 'vue-router'
    import CrmDescriptions from './@components/CrmDescriptions.vue'
    import QuestionOnAnswer from './@components/QuestionOnAnswer.vue'
    import AddTemplateTable from './@components/AddTemplateTable.vue'
    import { fetchRes } from '@common/utils'
    import {
        queryTemplateById,
        addTemplate,
        updateTemplate,
        checkQuestion
    } from '@/api/project/doubletrade/doubletradeList'
    import { TemplateInfoForm } from '@/api/project/doubletrade/type/apiType'
    import { ElMessage, FormInstance, FormRules } from 'element-plus'
    const router = useRouter()

    const descriptions = ref<any>(null)
    const questionOnAnswer = ref<any>(null)
    const addTemplateTable = ref<any>(null)
    const route = useRoute()
    const id = route.query?.id
    const type = route.query?.type
    const copyid = route.query?.copyid

    const isEdit = ref(!!id) //true:编辑 false:新增
    const isCopy = ref(!!copyid && !isEdit.value) //true:复制 false:新增
    console.log('iscopy=' + isCopy.value)
    const propsData = ref({
        isEdit,
        isCopy,
        id,
        type
    })

    const ruleFormRef = ref<FormInstance>()
    const rules = reactive<FormRules<TemplateInfoForm>>({
        tempName: [
            { required: true, message: '模版名称未填写，不允许保存！', trigger: 'blur' },
            { min: 1, max: 20, message: '模版名称不能超过20个字符！', trigger: 'blur' }
        ],
        preType: [{ required: true, message: '成单方式未选择，不允许保存！', trigger: 'change' }],
        tempType: [
            { required: true, message: '模版适用类型未选择，不允许保存！', trigger: 'change' }
        ],
        custType: [{ required: true, message: '客户类型未选择，不允许保存！', trigger: 'change' }]
    })

    const formList = reactive<TemplateInfoForm>({})
    // 保存
    async function handleSubmit(formEl: FormInstance | undefined) {
        if (!formEl) {
            return
        }
        const descriptionsData = descriptions.value.formData
        const fundCodeList = descriptions.value.fundCodeList.filter((item: null) => item !== null)
        const addTemplateTableData = addTemplateTable.value.questionList
        const params = {
            ...descriptionsData,
            proRiskLevel: descriptionsData.proRiskLevel.join(','),
            questionList: addTemplateTableData,
            fundCodeList: fundCodeList
        }
        const validateFlag = validateParams(params)
        if (!validateFlag) {
            return
        }
        await formEl.validate(async (valid: boolean, fields) => {
            if (valid) {
                fetchRes(isEdit.value ? updateTemplate(params) : addTemplate(params), {
                    successCB: isEdit.value ? updateTemplateInfo : addTemplateInfo,
                    successTxt: '',
                    failTxt: '请求失败请重试！',
                    fetchKey: ''
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    /**
     * 表单验证
     * @param params
     */
    function validateParams(params: any): boolean {
        debugger
        let errorMsg = ''
        const splitStr = '、'
        if (isEmpty(params.tempName)) {
            errorMsg += '模版名称' + splitStr
        }
        if (isEmpty(params.custType)) {
            errorMsg += '客户类型' + splitStr
        }
        if (isEmpty(params.preType)) {
            errorMsg += '成单方式' + splitStr
        }
        if (isEmpty(params.tempType)) {
            errorMsg += '模版适用类型' + splitStr
        }
        if (params.tempType === '1') {
            if (
                params.fundCodeList.length === 0 ||
                params.fundCodeList.every((item: null) => isEmpty(item))
            ) {
                errorMsg += '指定产品代码' + splitStr
            }
        } else if (params.tempType === '2') {
            if (params.proRiskLevel.length === 0 && isEmpty(params.proType)) {
                errorMsg += '产品风险等级、产品类型' + splitStr
            }
        }
        if (errorMsg !== '') {
            if (errorMsg.endsWith(splitStr)) {
                errorMsg = errorMsg.substring(0, errorMsg.length - splitStr.length)
            }
            ElMessage({
                message: errorMsg + '未选择，不允许保存！',
                showClose: true,
                type: 'error'
            })
            return false
        }
        if (params.questionList.length === 0) {
            ElMessage({ message: '请录入至少一条问题及答案！', showClose: true, type: 'error' })
            return false
        }

        let qFlag = true
        params.questionList.forEach((item: any, index: number) => {
            if (isEmpty(item.question) || isEmpty(item.answer)) {
                ElMessage({
                    message: '序号' + (index + 1) + '，未填写题目、答案，不允许保存！',
                    showClose: true,
                    type: 'error'
                })
                qFlag = false
                return false
            }
        })
        if (!qFlag) {
            return false
        }

        let questionErrorMsg = ''
        params.questionList.forEach((item: any, index: number) => {
            const questionArr = getPlaceholderArr(item.question)
            const answerArr = getPlaceholderArr(item.answer)
            let paramMsg = ''
            questionArr.forEach((item: any) => {
                paramMsg += item + splitStr
            })
            answerArr.forEach((item: any) => {
                paramMsg += item + splitStr
            })
            if (paramMsg !== '') {
                // 去除结尾的、
                paramMsg = paramMsg.substring(0, paramMsg.length - splitStr.length)
                questionErrorMsg += '序号' + (index + 1) + ',题目中' + paramMsg + ' 参数不存在；'
            }
        })
        if (questionErrorMsg !== '') {
            ElMessage({ message: questionErrorMsg, showClose: true, type: 'error' })
            return false
        }
        return true
    }

    const holderTextArr = [
        'custname',
        'custno',
        'custrisklevel',
        'distribution',
        'fundcode',
        'fundname',
        'fundtrisklevel',
        'appamt'
    ]

    /**
     * 正则匹配占位符字段名称并返回
     * @param str
     */
    function getPlaceholderArr(str: any): any[] {
        const regex = /\$\{([^}]*)\}/g
        const matches = str.match(regex)
        const placeholders = []
        if (matches) {
            for (let i = 0; i < matches.length; i++) {
                const match = matches[i]
                const placeholder = match.substring(2, match.length - 1)
                if (holderTextArr.indexOf(placeholder) === -1) {
                    placeholders.push(placeholder)
                }
            }
        }
        return placeholders
    }
    function handleOverruled() {
        const descriptionsData = descriptions.value.formData
        const templateQuestionList = addTemplateTable.value.questionList
        if (templateQuestionList.length === 0) {
            ElMessage({
                message: '问题为空！',
                showClose: true,
                type: 'error'
            })
            return
        }
        const overruledList = templateQuestionList.filter((item: any) => item.checkflag === '3')
        if (overruledList.length === 0) {
            ElMessage({
                message: '不存在驳回的记录，请检查！',
                showClose: true,
                type: 'error'
            })
            return
        }
        const flag = overruledList.every((item: any) => item.checkreason)
        overruledList.forEach((item: any, index: number) => {
            if (!item.checkreason) {
                ElMessage({
                    message: '序号' + (index + 1) + '，未填写驳回原因，不允许保存！',
                    showClose: true,
                    type: 'error'
                })
            }
        })
        if (!flag) {
            return
        }
        const params = {
            ...descriptionsData,
            checkList: templateQuestionList
        }
        fetchRes(checkQuestion(params), {
            successCB: (res: any) => {
                ElMessage({
                    message: '审核操作成功！',
                    showClose: true,
                    type: 'success'
                })
                router.push({ path: '/templateList' })
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    function handleCheck() {
        const descriptionsData = descriptions.value.formData
        const templateQuestionList = addTemplateTable.value.questionList
        if (templateQuestionList.length === 0) {
            ElMessage({
                message: '问题为空！',
                showClose: true,
                type: 'error'
            })
            return
        }
        const checkList = templateQuestionList.filter((item: any) => item.checkflag === '2')
        if (checkList.length !== templateQuestionList.length) {
            ElMessage({
                message: '存在驳回的记录，不允许审核通过！',
                showClose: true,
                type: 'error'
            })
            return
        }
        const params = {
            ...descriptionsData,
            checkList: templateQuestionList
        }
        fetchRes(checkQuestion(params), {
            successCB: (res: any) => {
                ElMessage({
                    message: '审核操作成功！',
                    showClose: true,
                    type: 'success'
                })
                router.push({ path: '/templateList' })
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 取消
    function cancel() {
        window.history.go(-1)
    }

    const addTemplateInfo = (res: any) => {
        ElMessage({
            message: '新增成功！',
            showClose: true,
            type: 'success'
        })
        router.push({ path: '/templateList' })
    }

    const updateTemplateInfo = (res: any) => {
        ElMessage({
            message: '修改成功！',
            showClose: true,
            type: 'success'
        })
        router.push({ path: '/templateList' })
    }

    onMounted(() => {
        initData()
    })

    function initData() {
        if (isEdit.value || isCopy.value) {
            const tempid: any = isEdit.value ? id : copyid
            fetchRes(queryTemplateById(tempid), {
                successCB: (res: any) => {
                    if (isEdit.value) {
                        descriptions.value.formData.id = res.id
                    }
                    descriptions.value.formData.tempName = res.tempName
                    descriptions.value.formData.preType = res.preType
                    descriptions.value.formData.tempType = res.tempType
                    descriptions.value.formData.custType = res.custType
                    descriptions.value.formData.proType = res.proType
                    res.proRiskLevel.split(',').forEach((item: any) => {
                        descriptions.value.formData.proRiskLevel.push(item)
                    })
                    // 问题&答案
                    res.questionList.forEach((item: any) => {
                        const question = {
                            id: item.id,
                            tempId: res.tempId,
                            question: item.question,
                            answer: item.answer,
                            checkflag:
                                // 数据为空 或 未审核时 默认展示审核通过
                                isEmpty(item.checkflag) || item.checkflag === '1'
                                    ? '2'
                                    : item.checkflag,
                            checkreason: item.checkreason
                        }
                        addTemplateTable.value.questionList.push(question)
                    })
                    // 基金代码
                    res.fundCodeList.forEach((item: any) => {
                        descriptions.value.formData.fundCodes.push(item)
                    })
                },
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            })
        }
    }

    function isEmpty(str: any) {
        if (str === '' || str === undefined || str === null) {
            return true
        }
        return false
    }
</script>

<style scoped>
    .button-style {
        margin-top: 20px;
        text-align: center;
    }
</style>
