{"name": "crm-web", "private": true, "version": "0.0.0", "type": "commonjs", "workspaces": ["common/**", "packages/**"], "scripts": {"prepare": "husky install", "start": "lerna run --parallel start", "dev:common-p": "pnpm run -C packages/crm-common-web dev", "dev:account-p": "pnpm run -C packages/crm-account-web dev", "dev:temp-p": "pnpm run -C packages/crm-template dev", "dev:wechat-p": "pnpm run -C packages/crm-wechat-web dev", "dev:trade-p": "pnpm run -C packages/crm-trade-web dev", "dev:perfor-p": "pnpm run -C packages/crm-performance-web dev", "dev:doubletrade-p": "pnpm run -C packages/crm-doubletrade-web dev", "dev-lerna": "========lerna 命令主要兼容不支持pnpm========", "dev:common": "lerna exec --scope crm-common-web yarn dev", "dev:template": "lerna exec --scope crm-template yarn dev", "dev:perfor": "lerna exec --scope crm-performance-web yarn dev", "dev:wechat": "lerna exec --scope crm-wechat-web yarn dev", "dev:doubletrade": "lerna exec --scope crm-doubletrade-web yarn dev", "build": "vue-tsc && vite build", "build:common-p": "pnpm run -C ./packages/crm-common-web build", "build:account-p": "pnpm run -C ./packages/crm-account-web build", "build:temp-p": "pnpm run -C ./packages/crm-template build", "build:wechat-p": "pnpm run -C ./packages/crm-wechat-web build", "build:doubletrade-p": "pnpm run -C ./packages/crm-doubletrade-web build", "build:trade-p": "pnpm run -C ./packages/crm-trade-web build", "build:perfor-p": "pnpm run -C ./packages/crm-performance-web build", "build-lerna": "========lerna 命令主要兼容不支持pnpm========", "build:common": "lerna exec --scope crm-common-web yarn build", "build:perfor": "lerna exec --scope crm-performance-web yarn build", "build:wechat": "lerna exec --scope crm-wechat-web yarn build", "build:doubletrade": "lerna exec --scope crm-doubletrade-web yarn build", "build:trade": "lerna exec --scope crm-trade-web yarn build", "build:account": "lerna exec --scope crm-account-web yarn build", "preview": "vite preview", "preview:common-p": "pnpm run -C ./packages/crm-common-web preview", "preview:account-p": "pnpm run -C ./packages/crm-account-web preview", "preview:temp-p": "pnpm run -C ./packages/crm-template preview", "preview:wechat-p": "pnpm run -C ./packages/crm-wechat-web preview", "preview:doubletrade-p": "pnpm run -C ./packages/crm-doubletrade-web preview", "preview:trade-p": "pnpm run -C ./packages/crm-trade-web preview", "preview:perfor-p": "pnpm run -C ./packages/crm-performance-web preview", "preview-lerna": "========lerna 命令主要兼容不支持pnpm========", "preview:common": "lerna exec --scope crm-common-web yarn preview", "preview:perfor": "lerna exec --scope crm-performance-web yarn preview", "preview:wechat": "lerna exec --scope crm-wechat-web yarn preview", "preview:doubletrade": "lerna exec --scope crm-doubletrade-web yarn preview", "preview:trade": "lerna exec --scope crm-trade-web yarn preview", "lint-comment": "========eslint 代码校验========", "lint": "eslint --fix --ext .ts,.tsx,.vue,.js,.jsx", "stylelint-comment": "========stylelint 样式校验========", "stylelint": "stylelint \"./**/*.{css,less,vue,html}\" --fix", "prettier-comment": "========代码格式化========", "prettier": "prettier . --write"}, "dependencies": {"@babel/core": "^7.0.0-0", "@element-plus/icons-vue": "^2.0.10", "@vue/reactivity": "^3.2.45", "@vue/shared": "^3.2.45", "axios": "^1.2.1", "echarts": "^5.4.1", "element-plus": "^2.2.27", "js-cookie": "^3.0.5", "less": "^4.1.3", "less-loader": "^11.1.0", "lint-staged": "^13.2.3", "lodash": "^4.17.21", "moment": "^2.29.4", "pinia": "^2.0.28", "vue": "^3.2.45", "vue-dompurify-html": "^4.1.4", "vue-router": "^4.1.6"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@commitlint/config-conventional": "^17.6.6", "@types/node": "^18.11.17", "@typescript-eslint/eslint-plugin": "^5.47.0", "@typescript-eslint/parser": "^5.47.0", "@vitejs/plugin-vue": "^4.0.0", "lerna": "^6.0.1", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "commitizen": "^4.3.0", "commitlint": "^17.6.6", "eslint": "^8.30.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^24.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.8.0", "husky": "^8.0.3", "nacos": "^2.5.1", "postcss": "^8.4.20", "postcss-html": "^1.3.0", "postcss-less": "^6.0.0", "prettier": "^2.8.1", "stylelint": "^15.10.1", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^4.2.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "typescript": "^4.9.4", "unplugin-auto-import": "^0.12.1", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.22.12", "vite": "4.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.0.11"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,ts,jsx,tsx,vue}": ["eslint --fix --ext .ts,.tsx,.vue,.js,.jsx", "prettier . --write"], "*.{cjs,json}": ["prettier . --write"], "*.{less,scss,css}": ["stylelint --fix", "prettier . --write"]}}