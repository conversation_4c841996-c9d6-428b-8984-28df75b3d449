/**
 * @Description: 拜访记录列表表格列
 * @Author: auto-generated
 * @Date: 2023-11-13
 */
import { TableColumnItem } from '@/types/index'

// 拜访记录列表表格列
export const visitMinuteTableColumn: TableColumnItem[] = [
    {
        key: 'createTime',
        label: '创建日期',
        width: 120
    },
    {
        key: 'visitDt',
        label: '拜访日期',
        width: 120
    },
    {
        key: 'visitPurpose',
        label: '拜访目的',
        width: 150
    },
    {
        key: 'consCustNo',
        label: '投顾客户号',
        width: 120
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120
    },
    {
        key: 'creatorName',
        label: '纪要创建人姓名',
        width: 150
    },
    {
        key: 'centerName',
        label: '所属中心',
        width: 120
    },
    {
        key: 'areaName',
        label: '所属区域',
        width: 120
    },
    {
        key: 'branchName',
        label: '所属分公司',
        width: 120
    },
    {
        key: 'visitType',
        label: '沟通方式',
        width: 120
    },
    {
        key: 'marketVal',
        label: '客户存量',
        width: 120
    },
    {
        key: 'healthAvgStar',
        label: '客户综合健康度',
        width: 150
    },
    {
        key: 'giveInformation',
        label: '提供资料',
        width: 120
    },
    {
        key: 'attendRole',
        label: '客户参与人员及角色',
        width: 180
    },
    {
        key: 'productServiceFeedback',
        label: '对产品或服务的具体反馈',
        width: 200
    },
    {
        key: 'ipsFeedback',
        label: '对于IPS报告反馈',
        width: 150
    },
    {
        key: 'addAmount',
        label: '近期可用于加仓的金额',
        width: 180
    },
    {
        key: 'focusAsset',
        label: '近期关注的资产类别或具体产品',
        width: 220
    },
    {
        key: 'estimateNeedBusiness',
        label: '评估客户对创新业务、家族信托、身份、法税的需求',
        width: 300
    },
    {
        key: 'nextPlan',
        label: '下一步工作计划',
        width: 150
    },
    {
        key: 'accompanyingType',
        label: '陪访人类型',
        width: 120
    },
    {
        key: 'accompanyingUser',
        label: '陪访人',
        width: 120
    },
    {
        key: 'accompanySummary',
        label: '陪-本次陪访概要经验或教训',
        width: 200
    },
    {
        key: 'accompanySuggestion',
        label: '陪-该客户下阶段工作的建议',
        width: 200
    },
    {
        key: 'managerName',
        label: '上级主管',
        width: 120
    },
    {
        key: 'managerSummary',
        label: '管-本次陪访概要经验或教训',
        width: 200
    },
    {
        key: 'managerSuggestion',
        label: '管-该客户下阶段工作的建议',
        width: 200
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
