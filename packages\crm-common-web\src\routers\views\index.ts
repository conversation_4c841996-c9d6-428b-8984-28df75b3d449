/*
 * @Description: 固定路由配置
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-10-09 19:08:43
 * @FilePath: /crm-web/packages/crm-common-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'viewer' },
        children: [
            {
                path: 'viewer',
                name: 'viewer',
                meta: {
                    title: '预览'
                },
                component: () => import('@/views/viewer/viewerIdx.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
