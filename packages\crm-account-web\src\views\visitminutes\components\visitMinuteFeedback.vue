<!--
 * @Description: 客户拜访纪要
 * @Author: hongdong.xie
 * @Date: 2024-06-12 10:15:39
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2024-06-12 10:15:39
 * @FilePath: /crm-web/packages/crm-template/src/views/customerVisit/visitRecord/visitRecordIndex.vue
-->
<template>
    <div class="visit-record-module">
        <crm-dialog
            v-model="dialogVisible"
            width="800px"
            title="客户拜访纪要"
            :slot-list="['default', 'footer']"
            :close-on-click-modal="false"
            class-name="visit-record-dialog"
        >
            <div class="visit-record-content">
                <!-- 基本信息 -->
                <div class="section-title">基本信息</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td class="label-cell">客户姓名<span class="required">*</span></td>
                            <td>
                                <div class="readonly-field">{{ formData.custName }}</div>
                            </td>
                            <td class="label-cell">投顾客户号<span class="required">*</span></td>
                            <td>
                                <div class="readonly-field">{{ formData.consCustNo }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">拜访日期<span class="required">*</span></td>
                            <td>
                                <div class="readonly-field">{{ formData.visitDt }}</div>
                            </td>
                            <td class="label-cell">沟通方式<span class="required">*</span></td>
                            <td>
                                <div class="readonly-field">{{ formData.visitType }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">客户存量</td>
                            <td>
                                <div class="readonly-field">{{ formData.marketVal }}</div>
                            </td>
                            <td class="label-cell">客户综合健康度（1-5星）</td>
                            <td>
                                <div class="readonly-field">
                                    {{ formData.healthAvgStar }}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">拜访目的<span class="required">*</span></td>
                            <td colspan="3">
                                <div class="checkbox-group">
                                    <el-checkbox
                                        v-for="item in VISIT_PURPOSE"
                                        :key="item.key"
                                        :model-value="formData.visitPurpose.includes(item.key)"
                                        :label="item.key"
                                        :disabled="true"
                                        class="visit-purpose-checkbox"
                                    >
                                        {{ item.label }}
                                    </el-checkbox>
                                    <span
                                        v-if="formData.visitPurpose.includes('6')"
                                        class="visit-purpose-text"
                                    >
                                        {{ formData.visitPurposeOther }}
                                    </span>
                                    <!-- <el-input
                                        v-if="formData.visitPurpose.includes('6')"
                                        v-model="formData.visitPurposeOther"
                                        class="underline-input"
                                        readonly
                                    /> -->
                                </div>
                                <div v-if="hasIpsReport" class="ips-report">
                                    IPS报告：
                                    <el-link type="primary" @click="handleViewIpsReport">{{
                                        formData.ipsReport.reportTitle
                                    }}</el-link>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">提供资料<span class="required">*</span></td>
                            <td colspan="3">
                                <div class="readonly-field">{{ formData.giveInformation }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">陪访人</td>
                            <td colspan="3">
                                <div class="readonly-field">{{ formData.accompanyingUser }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">客户参与人员及角色</td>
                            <td colspan="3">
                                <div class="readonly-field">{{ formData.attendRole }}</div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 客户反馈 -->
                <div class="section-title">客户反馈</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td class="label-cell">对产品或服务的具体反馈<br />（创新陪访必填）</td>
                            <td>
                                <div class="readonly-field">
                                    {{ formData.productServiceFeedback }}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">对于IPS报告反馈<br />（IPS陪访必填）</td>
                            <td>
                                <div class="readonly-field">{{ formData.ipsFeedback }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">近期可用于加仓的金额<br />（IPS陪访必填）</td>
                            <td>
                                <div class="amount-input">
                                    <div>
                                        <el-checkbox
                                            v-model="hasRmbAmount"
                                            :disabled="true"
                                            class="visit-purpose-checkbox"
                                            label=" "
                                        >
                                        </el-checkbox>
                                        <span>人民币：</span>
                                        <!-- <el-input
                                            v-model="formData.addAmountRmb"
                                            class="underline-input"
                                            :readonly="true"
                                        /> -->
                                        <span class="visit-purpose-text">
                                            {{ formData.addAmountRmb }}
                                        </span>
                                        <span class="unit"> 万</span>
                                    </div>
                                    <div class="mt-2">
                                        <el-checkbox
                                            v-model="hasForeignAmount"
                                            :disabled="true"
                                            class="visit-purpose-checkbox"
                                            label=" "
                                        >
                                        </el-checkbox>
                                        <span>外币：</span>
                                        <!-- <el-input
                                            v-model="formData.addAmountForeign"
                                            class="underline-input"
                                            :readonly="true"
                                        /> -->
                                        <span class="visit-purpose-text">
                                            {{ formData.addAmountForeign }}
                                        </span>
                                        <span class="unit"> 万</span>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                近期关注的资产类别或具体产品<br />（IPS陪访必填）
                            </td>
                            <td>
                                <div class="readonly-field">{{ formData.focusAsset }}</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                评估客户对创新业务、家族信托、身份、法税的需求
                            </td>
                            <td>
                                <div class="readonly-field">
                                    {{ formData.estimateNeedBusiness }}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                下一步工作计划<span class="required">*</span>：
                            </td>
                            <td>
                                <div class="readonly-field">{{ formData.nextPlan }}</div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 陪访专区 -->
                <div class="section-title">陪访专区（陪访人填写：项目经理/分总/区域总/其他）</div>
                <div class="info-table">
                    <table class="crm-table" aria-hidden="true">
                        <tr>
                            <td class="label-cell accompany-person"></td>
                            <td class="label-cell">本次陪访概要经验或教训</td>
                            <td class="label-cell">该客户下阶段工作的建议</td>
                        </tr>
                        <tr>
                            <td
                                v-if="props.transData.feedbackType == '1'"
                                rowspan="4"
                                class="label-cell accompany-person"
                            >
                                陪访人：{{ formData.userName }}
                            </td>
                            <td
                                v-if="props.transData.feedbackType == '2'"
                                rowspan="4"
                                class="label-cell accompany-person"
                            >
                                上级主管：{{ formData.managerName }}
                            </td>
                            <td>
                                <crm-input
                                    v-model="formData.summary"
                                    type="textarea"
                                    :disabled="disabledSummary"
                                    :rows="4"
                                    :placeholder="formData.canNotEditSummary ? '' : '请输入内容'"
                                    :maxlength="300"
                                />
                            </td>
                            <td>
                                <crm-input
                                    v-model="formData.suggestion"
                                    type="textarea"
                                    :rows="4"
                                    placeholder="请输入内容"
                                    :maxlength="300"
                                />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <crm-button class="custom-button" @click="handleSubmit"> 保存 </crm-button>
                    <crm-button class="custom-button cancel-button" @click="handleCancel">
                        取消
                    </crm-button>
                </div>
            </template>
        </crm-dialog>
    </div>
</template>

<script lang="ts" setup>
    import { reactive, watch } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus/es'
    import { useVisible } from '@/hooks/useVisible'
    import type {
        ISaveFeedbackReq,
        IVisitFeedbackDetailReq
    } from '@/api/project/communicate/visitFeedback/type/apiReqType'
    import type { IVisitFeedbackDetailRes } from '@/api/project/communicate/visitFeedback/type/apiResType'
    import { getVisitFeedbackDetail, saveFeedback } from '@/api/project/visitminutes/index'
    import { fetchRes, messageBox } from '@common/utils/index'
    import { VISIT_PURPOSE } from '@/constant/visitMinute'

    defineOptions({
        name: 'VisitRecordIndex'
    })

    // 表单引用
    const route = useRoute()
    const router = useRouter()
    // 获取路由参数
    const visitMinutesId = route.query.visitMinutesId as string
    const feedbackType = route.query.feedbackType as string

    // const props = defineProps<{
    //     modelValue: boolean
    // }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'save', data: any): void
    }>()

    // 使用自定义hook处理弹窗显示逻辑
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    const hasIpsReport = computed(() => {
        return formData.ipsReport && formData.ipsReport.reportId && formData.ipsReport.reportTitle
    })

    const hasRmbAmount = computed(() => {
        return formData.addAmountRmb && formData.addAmountRmb !== '0'
    })

    const hasForeignAmount = computed(() => {
        return formData.addAmountForeign && formData.addAmountForeign !== '0'
    })

    const disabledSummary = computed(() => {
        return props.transData.feedbackType === '2' && formData.canNotEditSummary
    })

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                id: string
                feedbackType: string
            }
        }>(),
        {
            dialogType: 'edit',
            visibleCus: true,
            transData: () => {
                return {
                    id: '',
                    feedbackType: ''
                }
            }
        }
    )

    // 表单数据
    const formData = reactive<IVisitFeedbackDetailRes>({
        custName: '',
        consCustNo: '',
        visitDt: '',
        visitType: '',
        marketVal: '',
        healthAvgStar: '',
        visitPurpose: [],
        visitPurposeOther: '',
        ipsReport: {
            reportId: '',
            reportTitle: ''
        },
        giveInformation: '',
        accompanyingUser: '',
        attendRole: '',
        productServiceFeedback: '',
        ipsFeedback: '',
        addAmountRmb: '',
        addAmountForeign: '',
        focusAsset: '',
        estimateNeedBusiness: '',
        nextPlan: '',
        userName: '',
        accompanyingId: '',
        managerName: '',
        summary: '',
        suggestion: '',
        canNotEditSummary: false
    })

    // 查看IPS报告
    const handleViewIpsReport = () => {
        window.open(
            window._msCrmAssetWebPrefix +
                `/index.html#/assetPreview?conscustno=${formData.consCustNo}&assetId=${formData.ipsReport.reportId}`,
            '_blank'
        )
    }

    // 格式化日期
    const formatDate = (dateStr: string) => {
        if (!dateStr) {
            return ''
        }
        return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`
    }

    // 取消
    const handleCancel = () => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        handleClose()
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = () => {
        dialogVisible.value = false
        // formEl.resetFields()
    }

    // 提交
    const handleSubmit = () => {
        let alertMsg = ''
        if (!formData.canNotEditSummary && !formData.summary) {
            alertMsg = '本次陪访概要经验或教训'
        }
        if (!formData.suggestion) {
            if (alertMsg) {
                alertMsg += '、'
            }
            alertMsg += '该客户下阶段工作的建议'
        }
        if (alertMsg) {
            ElMessage.error('请填写：' + alertMsg)
            return
        }
        const paramsCheck: IVisitFeedbackDetailReq = {
            visitMinutesId: props.transData.id,
            feedbackType: props.transData.feedbackType
        }
        fetchRes(getVisitFeedbackDetail(paramsCheck), {
            successCB: (res: any) => {
                if (
                    res.accompanyingUser !== formData.accompanyingUser ||
                    res.attendRole !== formData.attendRole ||
                    res.giveInformation !== formData.giveInformation ||
                    res.productServiceFeedback !== formData.productServiceFeedback ||
                    res.ipsFeedback !== formData.ipsFeedback ||
                    res.addAmountRmb !== formData.addAmountRmb ||
                    res.addAmountForeign !== formData.addAmountForeign ||
                    res.focusAsset !== formData.focusAsset ||
                    res.estimateNeedBusiness !== formData.estimateNeedBusiness ||
                    res.nextPlan !== formData.nextPlan
                ) {
                    messageBox(
                        {
                            confirmBtn: '继续提交',
                            cancelBtn: '取消',
                            content: `投顾填写的内容有更新<br/><span style="color:red;">是否继续提交反馈意见？</span>`,
                            useHtml: true
                        },
                        () => {
                            hanleSaveFeedback()
                        },
                        () => false
                    )
                } else {
                    hanleSaveFeedback()
                }
            },
            errorCB: (res: any) => {
                console.error('获取拜访反馈详情失败:', res)
            },
            catchCB: (res: any) => {
                console.error('获取拜访反馈详情失败:', res)
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    const hanleSaveFeedback = () => {
        try {
            const params: ISaveFeedbackReq = {
                visitMinutesId: props.transData.id,
                accompanyingId: formData.accompanyingId,
                feedbackType: props.transData.feedbackType,
                summary: formData.summary,
                suggestion: formData.suggestion
            }
            fetchRes(saveFeedback(params), {
                successCB: (res: any) => {
                    if (res.verifyMsg) {
                        ElMessage.error(res.verifyMsg)
                        return
                    }
                    ElMessage.success('保存成功')
                    handleClose()
                    emit('save', res)
                },
                errorCB: (res: any) => {
                    ElMessage.error('保存失败')
                    console.error('保存失败:', res)
                },
                catchCB: (res: any) => {
                    ElMessage.error('保存失败')
                    console.error('保存失败:', res)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } catch (error) {
            console.error('保存反馈失败:', error)
            ElMessage.error('保存反馈失败')
        }
    }

    // 初始化数据
    const initData = () => {
        try {
            const params: IVisitFeedbackDetailReq = {
                visitMinutesId: props.transData.id,
                feedbackType: props.transData.feedbackType
            }
            fetchRes(getVisitFeedbackDetail(params), {
                successCB: (res: any) => {
                    // 更新表单数据
                    Object.assign(formData, res)
                },
                errorCB: (res: any) => {
                    console.error('获取拜访反馈详情失败:', res)
                },
                catchCB: (res: any) => {
                    console.error('获取拜访反馈详情失败:', res)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } catch (error) {
            console.error('获取拜访反馈详情失败:', error)
            ElMessage.error('获取拜访反馈详情失败')
        }
    }

    watch(
        () => dialogVisible.value,
        newVal => {
            if (newVal) {
                console.log('dialog opened')
                initData()
            }
        }
    )
    onMounted(() => {
        initData()
    })
</script>

<style lang="less" scoped>
    td {
        vertical-align: middle;
    }

    .el-checkbox {
        height: auto;
    }

    .visit-purpose-text {
        font-size: 13px;
        text-decoration-line: underline;
    }

    // .underline-input {
    //     width: 80px;

    //     :deep(.el-input__wrapper) {
    //         height: 18px;
    //         padding: 0;
    //         background: transparent;
    //         border-bottom: 1px solid #dcdfe6;
    //         border-radius: 0; // 移除圆角
    //         box-shadow: none;

    //         &:hover {
    //             border-bottom-color: #c0c4cc;
    //         }

    //         &.is-focus {
    //             border-bottom-color: @theme_main;
    //         }
    //     }

    //     :deep(.el-input__inner) {
    //         text-align: center; // 可选：文字居中
    //     }
    // }

    .readonly-field {
        min-height: 20px;
        padding: 5px 0;
        line-height: 1.5;
        word-break: break-all;
    }

    .ml-2 {
        margin-left: 8px;
    }

    .purpose-item {
        display: flex;
        align-items: center;
    }

    .visit-record-module {
        .visit-record-content {
            max-height: 70vh;
            padding: 0 10px;
            overflow-y: auto;
        }

        .section-title {
            padding-left: 10px;
            margin: 15px 0 10px;
            font-size: 16px;
            font-weight: bold;
            line-height: 36px;
            background-color: #f5f7fa;
            border-radius: 4px;
        }

        .info-table {
            margin-bottom: 20px;

            .crm-table {
                width: 100%;
                border-collapse: collapse;

                td {
                    padding: 8px;
                    border: 1px solid #ebeef5;

                    &.label-cell {
                        width: 180px;
                        font-size: 14px;
                        color: #606266;
                        text-align: center;
                        background-color: #f5f7fa;
                    }

                    &.accompany-person {
                        width: 120px;
                        text-align: center;
                    }
                }

                .required {
                    margin-right: 4px;
                    color: #f56c6c;
                }

                .checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 15px;
                }

                .ips-report {
                    margin-top: 10px;
                }

                .amount-input {
                    display: flex;
                    align-items: center;

                    .amount-value {
                        width: 100px;
                        margin: 0 5px;
                    }

                    .unit {
                        margin-right: 20px;
                    }

                    .foreign-checkbox {
                        margin-left: 20px;
                    }
                }
            }
        }

        .dialog-footer {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 20px;

            .custom-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 120px;
                height: 40px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                border: 1px solid;
                border-radius: 4px;
                transition: all 0.3s;
            }

            // .save-button {
            //     color: white;
            //     background-color: #d9333f;
            //     border-color: #d9333f;

            //     &:hover {
            //         background-color: #c62f3a;
            //     }

            //     &:active {
            //         background-color: #b32a34;
            //     }
            // }

            .cancel-button {
                color: #606266;
                background-color: white;
                border-color: #dcdfe6;

                &:hover {
                    color: #409eff;
                    background-color: #ecf5ff;
                    border-color: #c6e2ff;
                }

                &:active {
                    color: #3a8ee6;
                    border-color: #3a8ee6;
                }
            }
        }
    }
</style>
