<!--
 * @Description: 双录配置模版列表页
 * @Author: jiewen.chen
 * @Date: 2024-05-27 10:10:11
-->
<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <label-item label="模版名称">
                    <crm-input
                        v-model="queryForm.tempName"
                        :clearable="true"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="客户类型">
                    <crm-select
                        v-model="queryForm.custType"
                        placeholder="请选择客户类型"
                        label-format="label"
                        value-format="key"
                        clearable
                        :option-list="CUSTTYPE_OPTION"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="成单方式">
                    <crm-select
                        v-model="queryForm.preType"
                        placeholder="请选择成单方式"
                        label-format="label"
                        value-format="key"
                        clearable
                        :option-list="PRETYPE_OPTION"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="模版适用类型">
                    <crm-select
                        v-model="queryForm.tempType"
                        placeholder="请选择模版适用类型"
                        label-format="label"
                        value-format="key"
                        clearable
                        :option-list="TEMPTYPE_OPTION"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="产品代码">
                    <crm-select
                        v-model="queryForm.fundCode"
                        placeholder="请输入产品代码"
                        label-format="jjjc"
                        value-format="jjdm"
                        filterable
                        clearable
                        remote
                        remote-show-suffix
                        :remote-method="queryFundList"
                        :loading="fundNameLoading"
                        :option-list="fundList"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="审核状态">
                    <crm-select
                        v-model="queryForm.checkflag"
                        placeholder="请选择审核状态"
                        label-format="label"
                        value-format="key"
                        clearable
                        :option-list="CHECKFLAG_OPTION"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item label="生效时间">
                    <el-date-picker
                        v-model="queryForm.templateTime"
                        type="datetimerange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        range-separator="-"
                        size="small"
                    />
                </label-item>
            </template>

            <template #operationBtns>
                <crm-button size="plus" :icon="Plus" :radius="true" plain @click="addForm">
                    新增
                </crm-button>
                <crm-button size="plus" :radius="true" plain @click="resetForm"> 清空 </crm-button>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :columns="tempListTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="200"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-show="scope.row.checkflag === '1'"
                            size="small"
                            :text="true"
                            link
                            @click="handleCheck(scope.row.id)"
                        >
                            审核
                        </el-button>
                        <el-button
                            v-show="scope.row.checkflag === '3'"
                            size="small"
                            :text="true"
                            link
                            @click="handleUpdate(scope.row.id)"
                        >
                            修改
                        </el-button>
                        <el-button
                            v-show="scope.row.checkflag === '2'"
                            size="small"
                            :text="true"
                            link
                            @click="handleEffectdt(scope.row)"
                        >
                            修改有效期
                        </el-button>
                        <el-button
                            v-show="scope.row.checkflag === '1'"
                            size="small"
                            :text="true"
                            link
                            @click="handleDelete(scope.row.id)"
                        >
                            删除
                        </el-button>
                        <el-button size="small" :text="true" link @click="handleShow(scope.row.id)">
                            查看
                        </el-button>
                    </template>
                </base-table>
            </template>
            <crm-dialog
                v-model="dialogVisible"
                width="450px"
                title="修改有效期"
                :slot-list="['default', 'footer']"
                :close-on-click-modal="false"
            >
                <el-date-picker
                    v-model="effectdtForm.templateTime"
                    type="datetimerange"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    range-separator="-"
                    :disabled-date="disabledDate"
                    size="small"
                />
                <template #footer>
                    <div class="dialog-footer">
                        <crm-button type="primary" plain size="small" @click="dialogVisible = false"
                            >关 闭</crm-button
                        >
                        <crm-button size="small" :radius="true" @click="confirmEffectdt"
                            >确 认</crm-button
                        >
                    </div>
                </template>
            </crm-dialog>
            <crm-dialog
                v-model="addDialogVisible"
                width="350px"
                title="操作提示"
                :slot-list="['default', 'footer']"
                :close-on-click-modal="false"
            >
                <el-radio-group v-model="addCheckOption">
                    <el-radio :label="0">新增空白模版</el-radio>
                    <el-radio :label="1">
                        从已有模版新增
                        <crm-select
                            v-model="selectTempId"
                            placeholder="请选择"
                            label-format="tempName"
                            value-format="id"
                            filterable
                            clearable
                            remote
                            remote-show-suffix
                            :option-list="templateList"
                            :style="{ width: '150px' }"
                        />
                    </el-radio>
                </el-radio-group>
                <template #footer>
                    <div class="dialog-footer">
                        <crm-button
                            type="primary"
                            plain
                            size="small"
                            @click="addDialogVisible = false"
                            >关 闭</crm-button
                        >
                        <crm-button size="small" :radius="true" @click="confirmAdd"
                            >确 认</crm-button
                        >
                    </div>
                </template>
            </crm-dialog>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { DateModelType, ElMessage, ElMessageBox } from 'element-plus'
    import { fetchRes } from '@common/utils'

    import { CUSTTYPE_OPTION, PRETYPE_OPTION, TEMPTYPE_OPTION, CHECKFLAG_OPTION } from '@/constant'
    import { tempListTableColumn } from './scripts/tableData'
    import {
        deleteTemplateById,
        getFundList,
        getTemplateList,
        queryTemplateList,
        updateEffectdt
    } from '@/api/project/doubletrade/doubletradeList'
    import { Plus } from '@element-plus/icons-vue'
    const router = useRouter()
    const dialogVisible = ref<boolean>(false)
    const addDialogVisible = ref<boolean>(false)
    const addCheckOption = ref<number>(0)
    const selectTempId = ref<string>('')
    const templateList = ref<any>([])
    const querySearchTemplateList = () => {
        fetchRes(getTemplateList(), {
            successCB: (res: any) => {
                // 获取待筛选的基金数据
                templateList.value = res.rows
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        tempName = ''
        custType = ''
        preType = ''
        tempType = ''
        fundCode = ''
        checkflag = ''
        templateTime = ''
        sort = 'creDt'
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    const tableData = ref<object[]>([])
    const queryList = () => {
        const queryFormTpl: any = {
            tempName: queryForm.tempName,
            tempType: queryForm.tempType,
            fundCode: queryForm.fundCode,
            checkflag: queryForm.checkflag,
            custType: queryForm.custType,
            preType: queryForm.preType,
            effectdt: queryForm.templateTime ? queryForm.templateTime[0] : null,
            failuredt: queryForm.templateTime ? queryForm.templateTime[1] : null,
            page: pageObj.value.page,
            size: pageObj.value.size
        }
        fetchRes(queryTemplateList(queryFormTpl), {
            successCB: (res: any) => {
                // 权限请求成功
                const { rows, pages, total } = res || {}
                tableData.value = rows
                pageObj.value.page = Number(pageObj.value.page)
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    /**
     * 调用获取基金的接口
     */
    const fundNameLoading = ref<boolean>(false)
    const fundList = ref<object[]>()
    const queryFundList = async (query: string) => {
        if (query !== '') {
            const queryFormTpl = {
                q: query
            }
            fetchRes(getFundList(queryFormTpl), {
                successCB: (res: any) => {
                    // 获取待筛选的基金数据
                    fundList.value = res.list
                },
                errorCB: (res: any) => {
                    ElMessage({
                        type: 'error',
                        message: res?.description || '请求失败'
                    })
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        }
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description 重置
     */
    const resetForm = () => {
        Object.assign(pageObj, new PageObj())
        Object.assign(queryForm, new QueryForm())
        nextTick(() => {
            queryList()
        })
    }

    /**
     * 新增页面
     */
    const addForm = () => {
        addDialogVisible.value = true
    }

    const confirmAdd = () => {
        // 从已有模版复制
        if (addCheckOption.value === 1) {
            if (selectTempId.value === '' || selectTempId.value === null) {
                ElMessage({
                    message: '请选择要复制的模版！',
                    showClose: true,
                    type: 'error'
                })
                return
            }
            router.push({
                path: '/createTemplate',
                query: {
                    copyid: selectTempId.value,
                    type: 'add'
                }
            })
        } else {
            router.push({
                path: '/createTemplate',
                query: {
                    type: 'add'
                }
            })
        }
        addDialogVisible.value = false
    }

    /**
     * 审核页面
     */
    const handleCheck = (id: string | undefined): void => {
        router.push({
            path: '/createTemplate',
            query: {
                id: id,
                type: 'check'
            }
        })
    }

    /**
     * 修改页面
     */
    const handleUpdate = (id: string | undefined): void => {
        router.push({
            path: '/createTemplate',
            query: {
                id: id,
                type: 'update'
            }
        })
    }

    class EffectdtForm {
        id = ''
        checkdt = ''
        templateTime: any = []
    }
    const effectdtForm = reactive(new EffectdtForm())
    /**
     * 修改有效期
     */
    const handleEffectdt = (row: any): void => {
        effectdtForm.id = row.id
        effectdtForm.templateTime = [row.effectdt, row.failuredt]
        effectdtForm.templateTime = effectdtForm.templateTime.filter((item: any) => item !== '')
        effectdtForm.checkdt = row.checkdt
        dialogVisible.value = true
    }

    /**
     * 禁用部分日期
     * 生效时间:不能选择小于审核通过的日期
     * 失效时间:不能选择小于生效时间的日期
     * @param time
     */
    const disabledDate = (time: Date) => {
        const checkdt = effectdtForm.checkdt ? new Date(effectdtForm.checkdt) : new Date()
        return time.getTime() < checkdt?.getTime()
    }

    /**
     * 提交修改有效期
     */
    const confirmEffectdt = (): void => {
        const form = {
            id: effectdtForm.id,
            effectdt: effectdtForm.templateTime[0],
            failuredt: effectdtForm.templateTime[1]
        }
        fetchRes(updateEffectdt(form), {
            successCB: (res: any) => {
                ElMessage({
                    message: '修改成功！',
                    showClose: true,
                    type: 'success'
                })
                dialogVisible.value = false
                queryList()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * 删除模版
     * @param id
     */
    const handleDelete = (id: String): void => {
        ElMessageBox.confirm(`请确认是否删除模版？`, '操作提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            customClass: 'blank-message-box',
            type: 'warning'
        }).then(() => {
            fetchRes(deleteTemplateById(id), {
                successCB: (res: any) => {
                    ElMessage({
                        message: '删除成功！',
                        showClose: true,
                        type: 'success'
                    })
                    initData()
                },
                successTxt: '',
                failTxt: '删除失败请重试！',
                fetchKey: ''
            })
        })
    }

    const handleShow = (id: string | undefined): void => {
        router.push({
            path: '/createTemplate',
            query: {
                id: id,
                type: 'show'
            }
        })
    }

    /**
     * @description 提示
     */
    onMounted(() => {
        initData()
    })

    /**
     * 加载页面数据
     */
    function initData() {
        queryList()
        querySearchTemplateList()
    }
</script>
<style lang="less" scoped></style>
