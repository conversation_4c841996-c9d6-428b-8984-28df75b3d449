/*
 * @Description: 全局方法ts类
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-12 17:32:22
 * @FilePath: /ds-report-web/src/utils/globalMethods.ts
 *
 */
//声明模块(被匹配的文件都属于该模块)
export {}
declare module '@/utils/globalMethods.js' {
    //声明变量类型
    export const setLocalItem: Object
    export const deleteLocalItem: Object
    export const setSessionItem: Object
    export const getSessionItem: Object
    export const delateSessionItem: Object
    export const logoutWithoutRequest: Object
    export const getLocalItem: Function
    export const transArrayToParams: Function
    export const deepClone: Function
    export const dateFormat: Function
    export const getTableColumnState: Function
    export const downloadFile: Function
    export const flatten: Function
    export const flatTree: Function
}
