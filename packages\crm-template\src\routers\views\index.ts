/*
 * @Description: 固定路由配置
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 15:43:47
 * @FilePath: /crm-template/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'reportList' },
        children: [
            {
                path: 'reportList',
                name: 'reportList',
                meta: {
                    title: '报告列表页'
                },
                component: () => import('@/views/assetReport/reportList/reportListIndex.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
