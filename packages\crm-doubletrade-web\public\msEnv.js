/*
 * @Author: ch<PERSON><PERSON>.<EMAIL>
 * @Date: 2023-02-22 19:50:51
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-28 13:02:06
 * @FilePath: /crm-web/packages/crm-template/public/msEnv.js
 * @Description: 宙斯配置项
 */
var _msEnvFlag = '3' // 1 线上环境  2 测试环境  3 mock环境  4 测试 doc 预览
var _msApiPrefix = window.origin + '/assetCurrent'
var _msPdfViewerPrefix = window.origin + '/pdfViewer/web/viewer.html?file='
var _msApiPrefix_oldAsset = window.origin + '/oldAsset'

if (_msEnvFlag === '2') {
    _msApiPrefix = window.origin + '/hdCurrent'
    // _msApiPrefix = 'http://**************:8087'
    _msPdfViewerPrefix = 'http://**************:8087/pdfViewer/web/viewer.html?file='
} else if (_msEnvFlag === '3') {
    _msApiPrefix = 'https://mock.apipark.cn/m1/2810603-0-default'
    _msApiPrefix_oldAsset = 'https://mock.apipark.cn/m1/2810603-0-default'
}
