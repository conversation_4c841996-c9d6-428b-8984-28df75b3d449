<template>
    <div class="tags-view-container">
        <el-scrollbar ref="tagScrollBar" :always="false" :vertical="false">
            <div class="scrollbar-content">
                <div
                    v-for="(tag, index) in visitedViews"
                    :key="tag && tag.params ? tag.name : tag.path"
                    :ref="`tagItem${index}`"
                    class="scrollbar-item"
                >
                    <router-link
                        :key="tag && tag.params ? tag.name : tag.path"
                        :class="isActive(tag) ? 'active' : ''"
                        :to="
                            tag.params && tag.params.length > 0
                                ? {
                                      name: tag.name,
                                      params: { ...tag.params, refresh: false }
                                  }
                                : { path: tag.path, query: tag.query, fullPath: tag.fullPath }
                        "
                        class="tag-item"
                        :title="tag.title"
                    >
                        {{ tag.title }}
                        <el-icon class="icon-close" @click.prevent.stop="handleCloseTag(tag)"
                            ><close />
                        </el-icon>
                    </router-link>
                </div>
            </div>
        </el-scrollbar>
    </div>
</template>

<script>
    import { useVisitedViewStore } from '../../stores/tagsView'
    import { useUserStore } from '../../stores/user'
    import { Close } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    export default defineComponent({
        components: { Close },
        setup() {
            const visitedViewStore = useVisitedViewStore()
            const userStore = useUserStore()
            return { visitedViewStore, userStore }
        },
        data() {
            return {
                selectedTag: {},
                affixTags: [],
                routes: []
            }
        },
        computed: {
            visitedViews({ visitedViewStore }) {
                return visitedViewStore.visitedViews || []
            }
        },
        watch: {
            $route() {
                this.handleAddTag(this.$route)
                this.handleMoveToTag()
            }
        },
        mounted() {
            this.handleAddTag(this.$route)
        },
        methods: {
            // 是否当前路由
            isActive(tagItem) {
                // return this.$route.path === route.path
                return tagItem.name === this.$route.name
            },

            // 访问路由后添加标签
            handleAddTag(routeInfo) {
                if (routeInfo?.name) {
                    this.visitedViewStore.addVisitedView(routeInfo)
                }
            },

            // 移动到当前选中项、如果出项滚动条
            handleMoveToTag() {
                const currentName = this.$route.name
                const index = this.visitedViews.findIndex(item => item.name === currentName)
                if (index !== -1) {
                    this.$nextTick(() => {
                        const tagItem = this.$refs[`tagItem${index}`]?.[0]
                        const offsetLeft = tagItem?.offsetLeft || 0
                        this.$refs.tagScrollBar.scrollTo(offsetLeft, 0)
                        // this.visitedViewStore.updateVisitedView(this.$route)
                    })
                }
            },

            // 关闭标签
            handleCloseTag(view) {
                this.visitedViewStore.delVisitedView(view).then(() => {
                    // 如果关闭的是当前选中的tag、则把visitedViews tag数组中的最后一项设置为选中
                    if (this.isActive(view)) {
                        this.toLastView(view)
                    }
                })
            },

            // 选中visitedViews最后一项
            toLastView(view) {
                const latestView = this.visitedViews.slice(-1)[0]
                if (latestView) {
                    this.$router.push({ path: latestView.path })
                } else {
                    this.$router.push({ path: '/' })
                }
            }
        }
    })
</script>

<style lang="less">
    @import './index.less';
</style>
