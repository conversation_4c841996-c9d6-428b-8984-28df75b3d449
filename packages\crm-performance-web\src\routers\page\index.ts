/*
 * @Description: 公共路由
 * @Author: chao<PERSON>.wu
 * @Date: 2023-07-24 11:28:40
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-07-24 15:19:39
 * @FilePath: /crm-rpo-template/packages/crm-template/src/routers/page/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'

export default [
    {
        path: '/login',
        name: 'login',
        hidden: false, // 自定义添加的属性
        meta: {
            title: '登录'
        },
        component: () => import('@/page/login.vue')
    },
    {
        path: '/:pathMatch(.*)', // 或者 /:pathMatch(.*)*
        name: '404',
        hidden: false, // 自定义添加的属性
        meta: {
            title: '404'
        },
        component: () => import('@/page/notfount.vue')
    }
] as AddRouteRecordRaw[]
